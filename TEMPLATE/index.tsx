/* 
// TEMPLATE 
// - To augment component props, do so in src/muiTheme.d.ts
// - Note, not all props can be augmented in this manner (typically only variant, size and color have augmentable overrides)

import type { Components, Theme } from "@mui/material";
import {ComponentName, type ComponentNameProps} from "@mui/material";

// - Define styleOverrides, defaultprops, and any component and type overrides here.
export const ComponentNameOverrides: Components<Theme>['MuiComponentName'] = {
    defaultProps: {
    },
    styleOverrides: {
    },
  };

// - In some cases, you'll need to wrap your MUI component to accommodate adjusted props or additional functionality.
// - When doing so, it's important to ensure refs can still be forwarded to the wrapped component.
// - A code sample has been provided below.
// - Note, you may have to adjust your import to [import ComponentName as MUIComponentName]

// - Wrapped component with only Forward Ref
import type {ForwardedRef} from 'react';
import {forwardRef} from 'react';

export const ComponentName = forwardRef((props: ComponentNameProps, ref: ForwardedRef<HTMLDivElement>) => (
  <MUIComponentName ref={ref} {...props} />
));
ComponentName.displayName = 'ComponentName';

// - Remember to import and export components and types from this file in src/index 
export {ComponentName, type ComponentNameProps}

// - To accommodate storybook auto prop generation, you'll need to export a wrapped component. 
// - This is ONLY for storybook usage and does not need to be exported from src/index

export function StoryComponent<C extends React.ElementType>(props: ComponentNameProps<C>) {
  return <ComponentName {...props} />;
}
StoryComponent.displayName = 'Button';
*/
