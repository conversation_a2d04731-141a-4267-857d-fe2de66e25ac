// import {fireEvent, render, screen} from '@testing-library/react';

// import { * } from './index';


// describe('ComponentName', () => {
//   it('should test all custom built functionality', () => {
//     render(<ComponentName onChange={() => {}} options={mockOptions} value={mockSingleValue} />);

//     expect(screen.getByRole('combobox')).toHaveValue('The Shawshank Redemption');
//   });

// });
