/*
import {ANIMATION_ARGTYPES, FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';
import type {ArgTypes} from '@storybook/react';

// Define component specific argtypes / argtype overrides
const COMPONENTNAME_ARGTYPES: ArgTypes = {
  color: {
    table: {
      type: {
        summary: 'enum',
      },
      defaultValue: {
        summary: 'undefined',
      },
    },
    control: 'select',
    options: ['default', ...CATEGORY_KEYS, undefined],
    description: 'The color of the component.',
  },
}

// Define component argtype keys (these are keys that are defined via shared argtypes and component specific argtypes)
const componentNameArgTypes = [
  ...Object.keys(COMPONENTNAME_ARGTYPES),
  'components',
  'componentsProps',
  'slotProps',
  'slots',
  'sx',
];

const argTypes = getArgTypes(componentNameArgTypes, {
  ...ANIMATION_ARGTYPES,
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...COMPONENTNAME_ARGTYPES
});

export default argTypes;
*/
