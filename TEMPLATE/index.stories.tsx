/*
TEMPLATE 

import type {Meta, StoryObj} from '@storybook/react';
import {ComponentName} from 'src/index';

// note, you may not need to define argTypes as these should be autogenerated
// currently storybook has a known bug that prevents autogeneration in some cases
import argTypes from './argTypes';

type Story = StoryObj<typeof ComponentName>;

// Note, storybook controls should be autogenerated. 
// However, there is a known compatibility issue that often results in no automatically generated controls.
// In this case, argTypes must be manually defined.
// See src/storybook-utils/argTypes to reuse shared, predefined argTypes and find argType helpers

const meta: Meta<typeof ComponentName> = {
  component: ComponentName,
  title: 'components/{Category}/ComponentName', // Refer to MUI (or existing) categories for guidance
// note, you may not need to define argTypes as these should be autogenerated
// currently storybook has a known bug that prevents autogeneration in some cases
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=1808%3A17859',
    },
    layout: 'centered',
  },
};

export default meta;

export const Basic: Story = {
  render: args => (
    <ComponentName {...args} />
  ),
};
*/
