{
  "extends": "react-app",
  "plugins": [
    "eslint-plugin-import",
    "eslint-plugin-jsx-a11y",
    "eslint-plugin-react",
    "eslint-plugin-react-hooks"
  ],
  "parser": "@typescript-eslint/parser",
  "settings": {
    "react": {
      "version": "detect" // Tells eslint-plugin-react to automatically detect the version of React to use
    }
  },
  "rules": {
    "no-restricted-globals": ["error", "history"],
    "no-debugger": "error",
    "no-console": "error",
    "no-var": "error",
    "@typescript-eslint/no-shadow": "error",
    "prefer-const": "error",
    "prefer-template": "error",
    "eqeqeq": ["error", "always"],
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": [
      "error",
      {"varsIgnorePattern": "^_", "argsIgnorePattern": "^_"}
    ],
    "@typescript-eslint/no-duplicate-imports": "error",
    "@typescript-eslint/consistent-type-imports": "error",
    "no-restricted-imports": "off",
    "no-restricted-properties": [
      "error",
      {
        "object": "theme",
        "property": "shadows",
        "message": "Please use design system approved `theme.boxShadows` instead."
      }
    ],
    "no-restricted-syntax": [
      "error",
      {
        "selector": "MemberExpression[type=MemberExpression][object.type=MemberExpression][object.object.type=Identifier][object.object.name=theme][object.property.type=Identifier][object.property.name=typography][property.type=Identifier][property.name=fontWeightBold]",
        "message": "Please use design system Typography component instead."
      },
      {
        "selector": "MemberExpression[type=MemberExpression][object.type=MemberExpression][object.object.type=Identifier][object.object.name=theme][object.property.type=Identifier][object.property.name=typography][property.type=Identifier][property.name=fontWeightMedium]",
        "message": "Please use design system Typography component instead."
      },
      {
        "selector": "MemberExpression[type=MemberExpression][object.type=MemberExpression][object.object.type=Identifier][object.object.name=theme][object.property.type=Identifier][object.property.name=typography][property.type=Identifier][property.name=fontWeightRegular]",
        "message": "Please use design system Typography component instead."
      },
      {
        "selector": "MemberExpression[type=MemberExpression][object.type=MemberExpression][object.object.type=Identifier][object.object.name=theme][object.property.type=Identifier][object.property.name=typography][property.type=Identifier][property.name=fontWeightLight]",
        "message": "Please use design system Typography component instead."
      }
    ],
    "@typescript-eslint/no-restricted-imports": [
      "error",
      {
        "patterns": [
          {
            "group": ["**/tokens/muiTheme", "**/tokens/*/colors"],
            "message": "Please use MUI theme-aware props, `styled` theme prop, or the useTheme hook instead."
          },
          {
            "patterns": ["@mui/*/*/*"],
            "message": "Please only use 1st or 2nd level imports. Anything further is not supported by MUI."
          }
        ],
        "paths": [
          {
            "name": "styled-components",
            "message": "Please use `useTheme` and `styled` exported from @mui/material instead."
          },
          {
            "name": "src/index",
            "message": "Import component from component file export to avoid circular dependencies"
          }
        ]
      }
    ]
  },
  "overrides": [
    {
      "files": [
        "src/tokens/LegacyTheme/index.tsx",
        "src/tokens/muiTheme.ts",
        "src/design-system-provider.tsx",
        "src/muiTheme.d.ts",
        "src/styled.d.ts"
      ],
      "rules": {
        "@typescript-eslint/no-restricted-imports": "off"
      }
    },
    {
      "files": ["src/**/*.styled.tsx"],
      "rules": {
        "no-restricted-properties": "off"
      }
    },
    {
      "files": ["src/**/*.stories.tsx"],
      "rules": {
        "@typescript-eslint/no-restricted-imports": [
          "error",
          {
            "patterns": [
              {
                "group": ["**/tokens/muiTheme", "**/tokens/*/colors"],
                "message": "Please use MUI theme-aware props, `styled` theme prop, or the useTheme hook instead."
              },
              {
                "patterns": ["@mui/*/*/*"],
                "message": "Please only use 1st or 2nd level imports. Anything further is not supported by MUI."
              }
            ],
            "paths": [
              {
                "name": "styled-components",
                "message": "Please use `useTheme` and `styled` exported from @mui/material instead."
              }
            ]
          }
        ]
      }
    }
  ],
  "ignorePatterns": ["node_modules", "dist"]
}
