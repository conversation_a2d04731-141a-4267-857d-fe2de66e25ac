import GlobalStyles from '@mui/material/GlobalStyles';
import {useTheme} from '@mui/material/styles';
import React from 'react';
import {createGlobalStyle} from 'styled-components';

import {DesignSystemProvider} from '../src/design-system-provider';
import {SEMANTIC_STATE_KEYS, THEME_KEYS} from '../src/tokens/constants';
import {setMUIxLicense} from '../src/utils/mui';

setMUIxLicense();

export const parameters = {
  backgrounds: {disable: true},
  docs: {
    page: null,
  },
  options: {
    storySort: {
      method: 'alphabetical',
      // this sort seems to be broken
      order: ['Welcome', 'Contributing', 'Migration'],
    },
  },
  controls: {
    /**
     * Display extra information about each control via JSDocs.
     */
    expanded: true,
    /**
     * Don't display controls for these props as we want to limit a consumer's ability to override DS standards
     */
    exclude: ['classes', 'className', 'style', 'sx'],
    matchers: {
      /**
       * Matches any prop with the word Date at the start or end, with a Date Picker control.
       */
      date: /(^date)|(Date$)/,
    },
    sort: 'requiredFirst',
  },
};

// TODO:
// consider using MUI CSSBaseline for a global reset in storybook and
// ScopedCSSBaseline for a global reset in an application
// ScopedCSSBaseline will allow developers to progressively implement MUI across an application
// https://mui.com/material-ui/react-css-baseline/
const GlobalStyle = createGlobalStyle`
  html {
    box-sizing: border-box;
  }

  *, *:before, *:after {
    box-sizing: inherit;
  }
`;

const GlobalStoryBackground: React.FC<{
  muiSurfaceKey: typeof SEMANTIC_STATE_KEYS[number];
}> = ({muiSurfaceKey}) => {
  const muiTheme = useTheme();
  return (
    <GlobalStyles
      styles={{
        body: {
          backgroundColor: muiTheme.palette.semanticPalette.surface[muiSurfaceKey],
        },
      }}
    />
  );
};

export const decorators = [
  Story => {
    const theme = useTheme();
    return <Story theme={theme} />;
  },
  (Story, context) => {
    const {
      muiThemeKey,
      muiSurfaceKey,
    }: {muiThemeKey: THEME_KEYS; muiSurfaceKey: typeof SEMANTIC_STATE_KEYS[number]} =
      context.globals;
    return (
      <DesignSystemProvider muiThemeKey={muiThemeKey}>
        <GlobalStyle />
        <GlobalStoryBackground muiSurfaceKey={muiSurfaceKey} />
        <Story />
      </DesignSystemProvider>
    );
  },
];

export const globalTypes = {
  muiThemeKey: {
    description: 'Change the MUI theme of the preview',
    defaultValue: THEME_KEYS.REGROW_DEFAULT,
    toolbar: {
      icon: 'paintbrush',
      dynamicTitle: true,
      items: [
        {value: THEME_KEYS.REGROW_DEFAULT, title: 'Regrow'},
        {value: THEME_KEYS.FLUROSENSE_LEGACY, title: 'Flurosense (legacy)'},
      ],
    },
  },
  muiSurfaceKey: {
    description: 'Change the background color using MUI surface keys',
    defaultValue: SEMANTIC_STATE_KEYS[0],
    toolbar: {
      icon: 'photo',
      dynamicTitle: true,
      items: SEMANTIC_STATE_KEYS.map(key => ({
        value: key,
        title: `surface ${key}`,
      })),
    },
  },
};

const preview = {};

export default preview;
