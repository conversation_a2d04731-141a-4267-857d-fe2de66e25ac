import {StorybookConfig} from '@storybook/react-vite';
const config: StorybookConfig = {
  stories: ['../guides/**/*.mdx', '../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx)'],
  docs: {
    autodocs: false,
  },
  addons: [
    {
      name: '@storybook/addon-essentials',
      options: {
        // addon-actions don't really provide much value in the current version of storybook
        actions: false,
      },
    },
    '@storybook/addon-a11y',
    'storybook-addon-designs',
    '@storybook/addon-mdx-gfm',
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  staticDirs: ['../public', '../storybook-assets'],
};
module.exports = config;
