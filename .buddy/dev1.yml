- pipeline: '[DEV] Build/Deploy service'
  on: 'CLICK'
  refs:
    - 'refs/heads/*'
  priority: 'NORMAL'
  fail_on_prepare_env_warning: true
  clone_depth: 1
  actions:
    - action: 'Make  docker safe tag'
      type: 'BUILD'
      working_directory: '/buddy'
      docker_image_name: 'library/ubuntu'
      docker_image_tag: '18.04'
      execute_commands:
        - "export DOCKER_SAFE_BRANCH_TAG=$(echo -n $BUDDY_EXECUTION_BRANCH | cut -c 1-30 | sed -Er 's/[\\._/]/-/g ; s/-{1,3}$//g')"
        - 'echo $ssh_gh_actions | base64 -d > gh_actions_ssh '
      volume_mappings:
        - '/:/buddy'
      trigger_condition: 'ALWAYS'
      shell: 'BASH'
    - action: 'Build Docker image'
      type: 'DOCKERFILE'
      dockerfile_path: 'Dockerfile'
      trigger_condition: 'ALWAYS'
      buildkit: true
      secret_id: 'key'
      secret_src: 'gh_actions_ssh'
    - action: 'Push Docker image'
      type: 'DOCKER_PUSH'
      login: '_json_key'
      password: 'secure!H4r9AU5QmXmTu4h5bwFmPw==.7erVJ4GnIosHpB4BXd1iwA=='
      docker_image_tag: '${ENV}-${BUDDY_EXECUTION_ID}-${DOCKER_SAFE_BRANCH_TAG},latest'
      repository: 'flurosat-154904/regrow-us/${app_name}'
      registry: 'us-docker.pkg.dev'
      trigger_condition: 'ALWAYS'
    - action: 'Deploy with Levant'
      type: 'BUILD'
      working_directory: '/buddy/design-system'
      docker_image_name: 'library/ubuntu'
      docker_image_tag: '20.04'
      execute_commands:
        - |
          export NOMAD_TOKEN=${NOMAD_TOKEN_DEV}
          export NOMAD_ADDR="${NOMAD_ADDR_DEV}"

          cd nomad

          wget -O levant ${LEVANT_BINARY_URL}

          chmod +x levant

          ./levant deploy \
            -ignore-no-changes \
            -force-count \
            -var-file=defaults.yml \
            -var-file=${ENV}.yml \
            -var app_version="${ENV}-${BUDDY_EXECUTION_ID}-${DOCKER_SAFE_BRANCH_TAG}" \
            -var commit_hash="${BUDDY_EXECUTION_REVISION}" app.nomad

      setup_commands:
        - 'apt-get update && apt-get -y install wget'
      volume_mappings:
        - '/:/buddy/design-system'
      cache_base_image: true
      shell: 'BASH'
  #    - action: "Send success notification to dev_cicd channel"
  #      type: "SLACK"
  #      content: "✅ <$BUDDY_EXECUTION_URL|$BUDDY_PROJECT_NAME $ENV (#$BUDDY_EXECUTION_ID) >       (Build from *$BUDDY_EXECUTION_BRANCH* by $BUDDY_INVOKER_NAME)"
  #      blocks: "[]"
  #      channel: "CH4GG81B4"
  #      channel_name: "dev_cicd"
  #      trigger_condition: "ALWAYS"
  #      integration_hash: "3DmJqAMrlNbWpn9Wdyg5Kx2n6z"
  #    - action: "Send failure notification to dev_cicd channel"
  #      type: "SLACK"
  #      trigger_time: "ON_FAILURE"
  #      content: "⛔  <$BUDDY_EXECUTION_URL|$BUDDY_PROJECT_NAME $ENV (#$BUDDY_EXECUTION_ID) >       (Build from *$BUDDY_EXECUTION_BRANCH* by $BUDDY_INVOKER_NAME)"
  #      blocks: "[]"
  #      channel: "CH4GG81B4"
  #      channel_name: "dev_cicd"
  #      trigger_condition: "ALWAYS"
  #      integration_hash: "3DmJqAMrlNbWpn9Wdyg5Kx2n6z"
  variables:
    - key: 'DOCKER_SAFE_BRANCH_TAG'
      value: ''
      settable: true
    - key: 'ENV'
      value: 'dev'
    - key: 'app_name'
      value: 'design-system'
    - key: 'TERRAFORM_VERSION'
      value: '0.14.7'
    - key: 'ELASTIC_APM_ENABLED'
      value: 'false'
