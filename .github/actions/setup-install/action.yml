name: Setup and Install
description: Setup and Install Leaf packages action composition

runs:
  using: 'composite'
  steps:
    # Corepack lets you use pnpm without having to install it.
    # It will respect the "packageManager" in the package.json
    - run: |
        npm install -g corepack@latest
        corepack enable
      shell: bash

    - name: Set up Node.js and pnpm
      uses: actions/setup-node@v4
      with:
        node-version: 20.15.1
        cache: 'pnpm'
        cache-dependency-path: |
          pnpm-lock.yaml

    - name: Install dependencies
      shell: bash
      run: |
        npm install -g pnpm@8.7.4
        pnpm install
