<!--- Uncomment to use template (it is not required, but a guide to help write good PRs)
# Checklist

- [ ] The PR title includes the ticket number (or "[No Ticket]") and description.
      Example: "[FSB-XX] Short Description"
- [ ] The PR body includes a description and links to the Jira ticket and designs if applicable.
- [ ] component, sub-components, component types and sub component types exported from component file and src/index
- [ ] Branch is deployed on dev and Design QA is complete?
- [ ] Component and Component types have been tested in Flurosense UI?
- [ ] Branch includes a version bump?

# Summary
Include a link to the Jira ticket and any background useful for reviewers.
Summary of what you did, the approach you took, libraries used, and any other meaningful information related to the changes made.

# Screenshot / Video

Include a screenshot/gif of the UI/UX changes made.

--->
