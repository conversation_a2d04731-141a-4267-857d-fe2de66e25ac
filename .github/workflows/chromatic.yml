name: 'Chromatic - Publish to Figma'

on:
  push:
    branches:
      - main

jobs:
  chromatic-build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - name: Install pnpm
        run: npm install -g pnpm@8.7.4
      - name: Install dependencies
        run: pnpm install
      - name: Production build
        run: pnpm build
      - name: Publish to Chromatic
        uses: chromaui/action@v1
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          autoAcceptChanges: true
          exitZeroOnChanges: true
          forceRebuild: true
