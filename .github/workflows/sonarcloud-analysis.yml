on:
  # Trigger analysis when pushing in main or pull requests, and when creating
  # a pull request.
  push:
    branches:
    - main
  pull_request:
    types: [opened, synchronize, reopened]
name: SonarCloud Quality Gate
jobs:
  sonarcloud:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        # Disabling shallow clone is recommended for improving relevancy of reporting
        fetch-depth: 0
    - name: SonarCloud Scan
      uses: sonarsource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
