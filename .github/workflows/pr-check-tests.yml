name: PR Repository Checks and Tests

on:
  workflow_dispatch:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  unit_test:
    runs-on: [ubuntu-22.04]
    steps:
      # Setup and install packages
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - uses: ./.github/actions/setup-install
        with:
          CICD_GCP_SERVICE_ACCOUNT: ${{ secrets.CICD_GCP_SERVICE_ACCOUNT }}

      # Run Unit Tests
      - id: unit_test
        name: Run Unit Tests
        run: |
          pnpm test:unit

      - uses: LouisBrunner/checks-action@v2.0.0
        if: always() && steps.unit_test.outcome != 'success'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          name: Run Unit Tests
          conclusion: failure

      - uses: LouisBrunner/checks-action@v2.0.0
        if: always() && steps.unit_test.outcome == 'success'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          name: Run Unit Tests
          conclusion: success

  code-coverage:
    runs-on: [ubuntu-22.04]
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-install
      - uses: ArtiomTr/jest-coverage-report-action@v2
        with:
          package-manager: pnpm
          test-script: pnpm jest --config ./jest.config.ts --silent --ci --runInBand --json --coverage --testLocationInResults --outputFile=report.json
          annotations: none
          custom-title: Leaf coverage report
