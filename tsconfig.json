{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"src/*": ["./src/*"]}}, "include": ["src", "jest-dom.d.ts", ".storybook"], "references": [{"path": "./tsconfig.node.json"}], "paths": {"@mui/styled-engine": ["./node_modules/@mui/styled-engine-sc"]}}