import React from 'react';
import {Box, styled} from '../../../src';

export const FeedbackWrapper = styled(Box)`
  background-color: ${({theme}) => theme.palette.semanticPalette.surface.warning};
  padding: ${({theme}) => theme.spacing(5)};
  border-radius: ${({theme}) => theme.shape.borderRadius * 2}px;
  h2 {
    border-bottom: 0;
    margin-bottom: ${({theme}) => theme.spacing(4)};
  }
`;
export default function Feedback() {
  return (
    <Box>
      Help us improve this pattern by providing feedback, asking questions, and leaving any other
      comments in our slack channel{' '}
      <a href="https://regrowag.slack.com/archives/C03TY104QSX" target="_blank">
        #design-system
      </a>
      .
    </Box>
  );
}
