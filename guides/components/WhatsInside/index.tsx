import React from 'react';
import {Box} from '../../../src';
import {Link, styled} from '@mui/material';

const whatsInsideArray = [
  {
    title: 'Getting Started',
    description: 'Learn how to install and use Regrow Design System',
    link: 'https://github.com/regrowag/design-system',
  },
  {
    title: 'Contribution',
    description: 'Learn how to contribute to Regrow Design System',
    link: '/?path=/docs/contributing--docs',
  },
  // {
  //   title: 'Foundations',
  //   description: '[Coming Soon]',
  //   // link: '/getting-started',
  // },
  // {
  //   title: 'Components',
  //   description: '[Coming Soon]',
  //   // link: '/getting-started',
  // },
];

const WhatsInsideBoxStyled = styled(Box)`
  && {
    background-color: ${({theme}) => theme.palette.semanticPalette.surface.main};
    width: 270px;
    padding: ${({theme}) => theme.spacing(4)};
    margin: ${({theme}) => theme.spacing(2)} 0;
    border: 2px solid ${({theme}) => theme.palette.semanticPalette.stroke.main};
    border-radius: ${({theme}) => theme.shape.borderRadius * 2}px;
    a {
      text-transform: lowercase;
    }
  }
`;

export const DocsBox = styled(Box)`
  && {
    background-color: ${({theme}) => theme.palette.semanticPalette.surface.main};
    padding: ${({theme}) => theme.spacing(2)};
    margin: ${({theme}) => theme.spacing(2)} 0;
    a {
      text-transform: lowercase;
    }
  }
`;

export const WhatsInsideWrapper = styled(Box)`
  && {
    background-color: ${({theme}) => theme.palette.semanticPalette.surface.success};
    padding: ${({theme}) => theme.spacing(5)};
    border-radius: ${({theme}) => theme.shape.borderRadius * 2}px;
    flex-wrap: wrap;
    h2 {
      border-bottom: 0;
      margin-bottom: ${({theme}) => theme.spacing(4)};
    }
  }
`;

export default function WhatsInside() {
  return (
    <Box display="flex" justifyContent="start" gap={6}>
      {whatsInsideArray.map((item, index) => (
        <WhatsInsideBoxStyled key={index}>
          <DocsBox>
            <h3>{item.title}</h3>
          </DocsBox>
          <DocsBox>{item.description}</DocsBox>
          {item.link && (
            <DocsBox>
              <Link href={item.link} color="primary">
                Learn More
              </Link>
            </DocsBox>
          )}
        </WhatsInsideBoxStyled>
      ))}
    </Box>
  );
}
