import React from 'react';
import {Box, styled} from '../../../src';
import {Link} from '@mui/material';

import {DocsBox} from '../WhatsInside';

const contributorsArray = [
  {
    name: '<PERSON>',
    description: 'Developer',
    link: '/getting-started',
  },
  {
    name: '<PERSON>',
    description: '<PERSON><PERSON><PERSON>',
    link: '/getting-started',
  },
  {
    name: '<PERSON><PERSON>',
    description: 'Developer',
    link: '/getting-started',
  },
  {
    name: '<PERSON><PERSON>',
    description: 'Dev<PERSON>per',
    link: '/getting-started',
  },
  {
    name: '<PERSON>',
    description: 'Developer',
    link: '/getting-started',
  },
  {
    name: '<PERSON>',
    description: 'Designer',
    link: '/getting-started',
  },
  {
    name: '<PERSON>',
    description: 'Designer',
    link: '/getting-started',
  },
];

const ContributorsBoxStyled = styled(Box)`
  && {
    background-color: ${({theme}) => theme.palette.semanticPalette.surface.main};
    width: 265px;
    padding: ${({theme}) => theme.spacing(4)};
    margin: ${({theme}) => theme.spacing(3)} 0;
    border: 2px solid ${({theme}) => theme.palette.semanticPalette.stroke.main};
    border-radius: ${({theme}) => theme.shape.borderRadius * 2}px;
    a {
      text-transform: lowercase;
    }
  }
`;

export const ContributorsWrapper = styled(Box)`
  && {
    background-color: ${({theme}) => theme.palette.semanticPalette.surface.secondary};
    padding: ${({theme}) => theme.spacing(5)};
    border-radius: ${({theme}) => theme.shape.borderRadius * 2}px;
    h2 {
      border-bottom: 0;
      margin-bottom: ${({theme}) => theme.spacing(4)};
    }
  }
`;

export default function Contributors() {
  return (
    <Box display="flex" flexWrap="wrap" justifyContent="space-between">
      Here is a list of the current contributors to the design system. As the design system becomes
      more established, the goal is to have other designers and engineers cycle in. If you have any
      specific questions or feedback, feel free to reach out to any one of us!
      {contributorsArray.map((item, index) => (
        <ContributorsBoxStyled key={index}>
          <DocsBox>
            <h3>{item.name}</h3>
          </DocsBox>
          <DocsBox>{item.description}</DocsBox>
          <DocsBox>
            <Link href="/slack" color="primary">
              contact
            </Link>
          </DocsBox>
        </ContributorsBoxStyled>
      ))}
    </Box>
  );
}
