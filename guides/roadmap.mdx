import { Meta } from '@storybook/blocks'

<Meta title="Roadmap" />

# Roadmap

Checkout our [JIRA Kanban board](https://regrow.atlassian.net/jira/software/c/projects/DES/boards/93) for inflight work and our [JIRA backlog](https://regrow.atlassian.net/jira/software/c/projects/DES/boards/93/backlog) for slated work. <br />

### What's Next?

You can find our [2024 Q1 Backlog](https://regrow.atlassian.net/jira/software/c/projects/DES/boards/93/backlog?label=2024-Q1) here <br />


### Want to add something to the queue?

- The best way is to come to our Design System Syncs scheduled weekly on Monday. This is where we usually do our roadmapping and scope changes.
The times rotate to accommodate for varying timezones! <br />
You can also add to our [weekly agenda](https://regrow.atlassian.net/wiki/spaces/DES/pages/2606465166/Design+System+Sync+Notes) here. Make sure you tag your name next to any added items! <br />

- If you can't make our meetings, post in our [#design-system slack channel](https://regrowag.slack.com/archives/C03TY104QSX) with your request. <br />

- And of course, you can help us build it! Let's still talk and make sure we're in alignment on needs and implementation, but we'd love assistance!
You can comb through our contributing docs and the Github Readme.
And then, we'll be happy to onboard and pair with you to get your component built!

- Otherwise, we do our best to work cross team and cross functionally to slate delivery of the components needed for upcoming feature work.

### What's available and what's not?

- **Components** - Ready for usage
- **Lab** - These are still under construction
- **Refactor** - These are legacy components that need to be refactored using MUI structure and shouldn't be used
- **Tokens** - These should not be used directly. Instead you can access these tokens via our theme. <br />
Note, we have both a Regrow and Flurosense theme


### What do I do if I need a component immediately that does not exist in Leaf?
- In the interest of deprecating the usage of `react-md` and legacy flurosense components you should import the component you need from `material-ui` directily.
- Make a design system [PLACEHOLDER] ticket in the [Design System backlog](https://regrow.atlassian.net/jira/software/c/projects/DES/boards/93/backlog) and include:
    1. Where this component was used
    2. All use cases this component should cover (screenshots and direct links to the application are helpful)
    3. And any additional notes you think are appropriate to include
- Post the ticket in our [#design-system slack channel](https://regrowag.slack.com/archives/C03TY104QSX) with your request    

There's more details on component implementation process in the Contributing docs.
