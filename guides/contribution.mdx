import { Meta, <PERSON><PERSON>, Story, Controls, Title, Source } from "@storybook/blocks";

<Meta title="Contributing"/>

# Contributing

We've described our process for scoping and developing components below.

## Scoping

1. Sync with Design system designers or engineers to ensure:
    - an existing component is not already available 
    - to align on all use cases needed for your new component
    - ensure the component should be included within the design system 
    
1. A JIRA [AUDIT] ticket is created if a broader investigation of use cases is required.
1. A JIRA [DESIGN] ticket is created if a design is not already present within the [Design System Figma](https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=1808%3A17050&mode=dev) to log design work. Ensure AC includes a design for all variants and states.
1. A JIRA [BUILD] ticket with AC and any supporting work is created to log developer work.
1. A JIRA [INTEGRATE] ticket is created, if there is an immediate use case to replace a specific component in Flurosense UI with the new Design System Component.
1. Prior to building a component, a sync with at least one design system SI engineer and one design system MRV engineer is scheduled to ensure alignment on implementation approach. 

More details on [JIRA ticket conventions](https://regrow.atlassian.net/wiki/x/LAB_qQ) and ticket templates can be found in Confluence.

## Developer Implementation

Note, instructions for local setup, local testing, common pnpm commands and deploying to production and devlopment environments can be found in the [github readme](https://github.com/regrowag/design-system#readme).

1. Follow readme instructions linked above to get your local environment set up
1. Develop your component following our the Storybook file structure and [Component Guidelines](https://regrow.atlassian.net/wiki/spaces/DES/pages/2798126250/Architecture+Code+Guidelines) <br />
    (Note, a Template folder with files are provided within `TEMPLATE/`. These can be copied and provide additional requirements for development.)
1. Ensure you export your component, component types and any necessary helpers that need surfacing from `src/index`
1. For any new components, ensure you provide Docs.mdx, Stories, and Story controls and defaults for all component props
1. Follow the readme instructions to test your DS package updates locally within your consuming UI application
1. Submit a Pull Request with a title using the [Jira](https://regrow.atlassian.net/jira/software/c/projects/DES/boards/93/backlog) Ticket number, ex `[DES-XXX] - Build Filled Input Component` <br />
    (Note, a PR template is provided with additional requirements)
1. Post a message in [#ui-pull-requests](https://regrowag.slack.com/archives/C042U6HK5BR) notifying a PR is ready for review
1. Follow readme instructions to deploy your changes to a dev environment for QA and Design QA approval
1. When an Engineer, Designer and optional QA have all approved, merge your changes.
1. If you've included a version bump, publish a new version of the design system (or ask a developer with permissions to do so)
1. Post in design system channel about new version and updates

## Theme Architecture, Component Development and Usage Guidelines

Additional information about our project architecture, MUI conventions and our code style guide can be found in [Confluence](https://regrow.atlassian.net/wiki/spaces/DES/pages/2798126250/Architecture+Code+Style+Guide).
This includes:
- Theme Architecture and Token definitions
- [Component File Structure](https://regrow.atlassian.net/wiki/spaces/DES/pages/2798126250/Architecture+Code+Style+Guide#Component-File-Structure) and [Template files](https://github.com/regrowag/design-system/tree/main/TEMPLATE)
- Steps to define a Design system [MUI-based Component](https://regrow.atlassian.net/wiki/spaces/DES/pages/2798126250/Architecture+Code+Style+Guide#Design-System-Component-Definitions), including:
  - Style Overrides and Default Props Usage
  - Component Type modifications and Component composing
  - Component Theme overrides
  - Component Module Augmentation
  - Story creation
- Our [code guidelines](https://regrow.atlassian.net/wiki/spaces/DES/pages/2798126250/Architecture+Code+Style+Guide#DS-Component-Usage-In-Flurosense-UI) for expected usage of Design System components within Flurosense UI
