import { Meta } from '@storybook/blocks'
import WhatsInside, { WhatsInsideWrapper} from './components/WhatsInside'
import Contributors, { ContributorsWrapper} from './components/Contributors'
import Feedback, { FeedbackWrapper} from './components/Feedback'
import WelcomeHeader from './components/WelcomeHeader'

import { DesignSystemProvider} from '../src/design-system-provider'

<Meta title="Welcome" />

<DesignSystemProvider muiThemeKey={0}>

    <WelcomeHeader/>

    <br />
    <br />

    <WhatsInsideWrapper>
        ## What's inside:
        <WhatsInside />
    </WhatsInsideWrapper>

    <br />
 
    <ContributorsWrapper>
        ## Contributors
        <Contributors />
    </ContributorsWrapper>

    <br/>

    <FeedbackWrapper>
        ## Feedback
        <Feedback/>
    </FeedbackWrapper> 

</DesignSystemProvider>

<br/>
<br/>

## Additional Resources
- For our design source of truth, review our [Figma](https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?mode=dev) Design File
- For more information on how we got started, architecture and organization principles, current meeting notes, review our [Confluence space](https://regrow.atlassian.net/wiki/spaces/DES/pages/**********/Design+System)
- For visibility into our roadmap and current work, review our [JIRA](https://regrow.atlassian.net/jira/software/c/projects/DES/boards/93/backlog) backlog and kanban board
- For technical setup instructions and to review our code, checkout our [Github](https://github.com/regrowag/design-system) Code repository and [Readme](https://github.com/regrowag/design-system#readme)
- Our Design System is built using MUI Core, specifically the [Material UI](https://mui.com/material-ui/getting-started/), MUI System and [MUI X](https://mui.com/x/whats-new/) packages. We utilize [styled-components](https://styled-components.com/) for our style engine.

