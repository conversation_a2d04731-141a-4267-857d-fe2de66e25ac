import { Meta, <PERSON><PERSON>, Story, Controls, Title, Source } from "@storybook/blocks";

<Meta title="Migration"/>

# Migrating a component from `react-md` to `design-system`

Please read through entire document before beginning.

1. **Example**: I want to replace the top level `Text` and `FluroButton` components in the `ProgramsList` view.
2. Find the desired component file in the `flurosense-ui` and identify the sections of the page you want to update.

**Example: ProgramList header area**

```jsx
<Text bold variant="h1" className="margin-top-20">
    MRV Programs ({totalPrograms})
</Text>
<Flex justifyContent="space-between" className="mb-1" nowrap>
    <MRVAdminSearch
        isLoading={isLoadingPrograms}
        value={search}
        onChange={setSearch}
        placeholder="Search Programs"
    />
    {isAdmin && (
        <FluroButton
            primary
            raised
            onClick={() => setCreateProgramPopupVisible(true)}
            data-testid="program-admin-program-create--MRV">
            Create a program
        </FluroButton>
    )}
</Flex>
```

3. Identify all `fluro` components and `react-md` components
```jsx
// Replace Text component
<Text bold variant="h1" className="margin-top-20">
    MRV Programs ({totalPrograms})
</Text>
<Flex justifyContent="space-between" className="mb-1" nowrap>
    <MRVAdminSearch
        isLoading={isLoadingPrograms}
        value={search}
        onChange={setSearch}
        placeholder="Search Programs"
    />
    {isAdmin && (
        // Replace FluroButton Component
        <FluroButton
            primary
            raised
            onClick={() => setCreateProgramPopupVisible(true)}
            data-testid="program-admin-program-create--MRV">
            Create a program
        </FluroButton>
    )}
</Flex>
```
        
4. You will need to determine the nested `react-md` components and make sure you are ready to replace all children `react-md` components before moving on.
5. Place the `ScopedCSSBaseline` which is exported from the `design-system` at the root of the component you want to replace.
```jsx
<ScopedCssBaseline>
    <Text bold variant="h1">
        MRV Programs ({totalPrograms})
    </Text>
    <Flex justifyContent="space-between" className="mb-1" nowrap>
        <MRVAdminSearch
                isLoading={isLoadingPrograms}
                value={search}
                onChange={setSearch}
                placeholder="Search Programs"
        />
        {isAdmin && (
            <FluroButton
                primary
                raised
                onClick={() => setCreateProgramPopupVisible(true)}
                data-testid="program-admin-program-create--MRV">
                Create a program
            </FluroButton>
        )}
    </Flex>
</ScopedCssBaseline>
```        
6. Next, replace all children `react-md` components with their `design-system` equivalent.

    If you are using a component that has a `styled` component integration, you will need to replace the styled component with the `design-system` `styled` instance
    when you use a styled component you need to import it from the design system.
        - **correct** `import {styled} from '@regrow-internal/design-system`
        - **incorrect**: `import {styled} from '@mui/material'`
        
```jsx
<ScopedCssBaseline>
    <Typography variant="h1" color="primary">
        MRV Programs ({totalPrograms})
    </Typography>
    <Flex justifyContent="space-between" className="mb-1" nowrap>
        <MRVAdminSearch
                isLoading={isLoadingPrograms}
                value={search}
                onChange={setSearch}
                placeholder="Search Programs"
        />
        {isAdmin && (
            <Button
                color="primary"
                variant="contained"
                onClick={() => setCreateProgramPopupVisible(true)}
                data-testid="program-admin-program-create--MRV">
                Create a program
            </Button>
        )}
    </Flex>
</ScopedCssBaseline>
  ```
        
7. If you updated a component that has children components which are wrapped in a `ScopedCSSBaseline` component, please remove the lower level child `ScopedCSSBaseline`.
8. QA your local work against the dev environment to ensure no visual or functional regressions have been made.
9. Pull Request your work and
10. Merge!