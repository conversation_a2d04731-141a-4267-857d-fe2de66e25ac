import {useMemo} from 'react';
import type {ReactNode} from 'react';
import {THEME_KEYS} from 'src/tokens/constants';
import {theme as legacyTheme} from 'src/tokens/LegacyTheme';
import {getMUITheme} from 'src/tokens/muiTheme';
import {ThemeProvider as MUIThemeProvider, StyledEngineProvider} from '@mui/material';
import {createTheme} from '@mui/material/styles';

import {ThemeProvider} from 'styled-components';

// When using TypeScript 4.x and above
import type {} from '@mui/lab/themeAugmentation';
// When using TypeScript 3.x and below
import '@mui/lab/themeAugmentation';

type DesignSystemProviderProps = {
  muiThemeKey?: THEME_KEYS;
  children: ReactNode;
};

export function DesignSystemProvider({muiThemeKey, children}: DesignSystemProviderProps) {
  const muiTheme = useMemo(
    () => createTheme(getMUITheme(muiThemeKey ?? THEME_KEYS.FLUROSENSE_LEGACY)),
    [muiThemeKey]
  );

  return (
    <StyledEngineProvider injectFirst>
      <MUIThemeProvider theme={muiTheme}>
        <ThemeProvider theme={legacyTheme}>{children}</ThemeProvider>
      </MUIThemeProvider>
    </StyledEngineProvider>
  );
}
