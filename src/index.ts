import {setMUIxLicense} from './utils/mui';

setMUIxLicense();

// export is required to make augmented mui theme and chartjs types available to library consumers
export type {} from 'src/muiTheme';
export type {} from 'src/components/Charts/plugins';

// TODO: FSB-14135 remove deprecated theme
export {theme, type LegacyTheme} from 'src/tokens/LegacyTheme';

export {DesignSystemProvider} from 'src/design-system-provider';

export {THEME_KEYS} from 'src/tokens/constants';
export type {CategoryPalette} from 'src/tokens/themeTypes';

// MUI
export {
  type Theme,
  useTheme,
  useMediaQuery,
  styled,
  CssBaseline,
  ScopedCssBaseline,
  Avatar,
  type AvatarProps,
  InputAdornment,
  type InputAdornmentProps,
  FormControl,
  type FormControlProps,
  FormControlLabel,
  type FormControlLabelProps,
  FormLabel,
  type FormLabelProps,
  FormGroup,
  type FormGroupProps,
  List,
  type ListProps,
  ListItem,
  type ListItemProps,
  ListItemAvatar,
  type ListItemAvatarProps,
  ListItemButton,
  type ListItemButtonProps,
  ListItemIcon,
  type ListItemIconProps,
  ListItemText,
  type ListItemTextProps,
} from '@mui/material';

export {lighten, darken} from '@mui/system';

export {FormHelperText, type FormHelperTextProps} from 'src/components/Inputs/FormHelperText';

export {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
} from 'src/components/Accordion';
export type {
  AccordionProps,
  AccordionActionsProps,
  AccordionDetailsProps,
  AccordionSummaryProps,
} from 'src/components/Accordion';
export {Button, IconButton, LoadingButton} from 'src/components/Button';
export type {ButtonProps, IconButtonProps, LoadingButtonProps} from 'src/components/Button';
export {ButtonGroup, type ButtonGroupProps} from 'src/components/ButtonGroup';
export {Chip, type ChipProps} from 'src/components/Chip';
export {Box, type BoxProps} from 'src/components/Box';
export {Checkbox, type CheckboxProps} from 'src/components/Checkbox';
export {Container, type ContainerProps} from 'src/components/Container';
export {Divider, type DividerProps} from 'src/components/Divider';
export {FilledInput, type FilledInputProps} from 'src/components/Inputs/FilledInput';
export {MenuItem, type MenuItemProps, Menu, type MenuProps} from 'src/components/Menu';
export {OutlinedInput, type OutlinedInputProps} from 'src/components/Inputs/OutlinedInput';
export {Paper, type PaperProps} from 'src/components/Paper';
export {Select, type SelectProps, type SelectChangeEvent} from 'src/components/Inputs/Select';
export {
  SelectField,
  type SelectFieldProps,
  SelectFieldSelectDeselectAllButtons,
  SelectFieldClearAdornment,
  useSelectFieldRenderValueHelper,
} from 'src/components/Inputs/SelectField';
export {Stack, type StackProps} from 'src/components/Stack';
export {Switch, type SwitchProps} from 'src/components/Switch';
export {
  Stepper,
  type StepperProps,
  Step,
  type StepProps,
  StepLabel,
  type StepLabelProps,
  StepButton,
  type StepButtonProps,
  StepIcon,
  type StepIconProps,
  StepContent,
  type StepContentProps,
  StepConnector,
  type StepConnectorProps,
} from 'src/components/Stepper';
export {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
} from 'src/components/Table';
export type {
  TableProps,
  TableBodyProps,
  TableCellProps,
  TableContainerProps,
  TableFooterProps,
  TableHeadProps,
  TablePaginationProps,
  TableRowProps,
  TableSortLabelProps,
} from 'src/components/Table';
export {TextField, type TextFieldProps} from 'src/components/Inputs/TextField';
export {ToggleButton, type ToggleButtonProps} from 'src/components/ToggleButton';
export {ToggleButtonGroup, type ToggleButtonGroupProps} from 'src/components/ToggleButtonGroup';
export {Tooltip, type TooltipProps} from 'src/components/Tooltip';
export {Typography, type TypographyProps} from 'src/components/Typography';
export {TypographyOverflow, type TypographyOverflowProps} from 'src/components/TypographyOverflow';
export {SvgIcon, type SvgIconProps, Icon, type IconType} from 'src/components/Icon';
export {
  CombinedInputWithSelect,
  type CombinedInputWithSelectProps,
} from 'src/components/Inputs/CombinedInputWithSelect';
export {
  SimpleDialog,
  DialogContent,
  DialogActions,
  type SimpleDialogProps,
  type DialogContentProps,
  type DialogActionsProps,
  type DialogCloseReason,
} from 'src/components/SimpleDialog';
export {Skeleton, type SkeletonProps} from 'src/components/Skeleton';
export {Alert, AlertTitle, type AlertProps, type AlertTitleProps} from 'src/components/Alert';
export {
  Tab,
  Tabs,
  TabContext,
  TabList,
  TabPanel,
  type TabProps,
  type TabsProps,
  type TabListProps,
  type TabContextProps,
  type TabPanelProps,
} from 'src/components/Navigation/Tabs';
export {
  Autocomplete,
  sortOptionsBySelected,
  sortOptionsByGroup,
  type AutocompleteProps,
} from 'src/components/Autocomplete';
export type {AutocompleteOptionType} from 'src/components/Autocomplete/types';
export {Label, type LabelProps} from 'src/components/Inputs/inputHelpers';
export {Pagination, type PaginationProps} from 'src/components/Pagination';
export {Radio, type RadioProps} from 'src/components/Radio';
export {RadioGroup, type RadioGroupProps} from 'src/components/RadioGroup';
export {
  Link,
  type LinkProps,
  type LinkBaseProps,
  type LinkClassKey,
  type LinkTypeMap,
  type LinkClasses,
  linkClasses,
} from 'src/components/Link';
export {CircularProgress} from 'src/components/CircularProgress';
export {
  ResizableFlexContainer,
  type ResizableFlexContainerProps,
} from 'src/components/ResizeableBox/index';

// Non-MUI libraries
export {DatePicker, type DatePickerProps} from 'src/components/DatePicker';
export * from 'src/components/Charts';

// Needs Refactor
export {Timeline} from 'src/components/Timeline';
export * from 'src/components/Timeline/types';
export * from 'src/components/Dialogs';

// General
export type {FunctionTestCase} from 'src/types';
export * from 'src/hooks';
export * from 'src/utils/color';
export * from 'src/lab';
