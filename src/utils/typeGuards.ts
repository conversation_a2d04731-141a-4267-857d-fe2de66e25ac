import {CATEGORY_KEYS} from 'src/tokens/constants';
import {ICON_MAP} from 'src/tokens/icons';
import type {CategoryPalette, IconMapType, IconType} from 'src/tokens/themeTypes';

type Nil = null | undefined;
type Falsy = false | '' | 0 | null | undefined;
type Truthy<T> = T extends Falsy ? never : T;

/**
 * Tests whether or not an argument is null (type guard)
 */
export const isNull = (x: unknown): x is null => x === null;

/**
 * Tests whether or not an argument is undefined (type guard)
 */
export const isUndefined = (x: unknown): x is undefined => x === undefined;

/**
 * Tests whether or not an argument is null or undefined (type guard)
 */
export const isNil = (x: unknown): x is Nil => isNull(x) || isUndefined(x);

/**
 * Tests whether or not an argument is null or undefined (type guard)
 */
export const isDefined = <T>(x: T | Nil): x is NonNullable<T> => !isNil(x);

/**
 * Type guard for the `false` literal of the `boolean` primitive
 */
const isFalse = (x: unknown): x is false => typeof x === 'boolean' && x === false;

/**
 * Type guard for the `0` literal of the `number` primitive
 */
const isZero = (x: unknown): x is 0 => isNumber(x) && x === 0;

/**
 * Type guard for the Falsy type
 */
export const isFalsy = (x: unknown): x is Falsy =>
  isNil(x) || isFalse(x) || isEmptyString(x) || isZero(x) || (isNumber(x) && Number.isNaN(x));

/**
 * Type guard for the Truthy type
 */
export const isTruthy = <T>(x: T): x is Truthy<T> => !isFalsy(x);

/**
 * Type guard for the `string` primitive
 */
export const isString = (x: unknown): x is string => typeof x === 'string';

/**
 * Type guard for the `''` literal of the `string` primitive
 */
export const isEmptyString = (x: unknown): x is '' => isString(x) && x === '';

/**
 * Type guard for `string` primitives that are not `''`
 */
export const isNonEmptyString = (x: unknown): x is string => isString(x) && !isEmptyString(x);

/**
 * Type guard for the `number` primitive
 */
export const isNumber = (x: unknown): x is number => typeof x === 'number';

/**
 * The opposite of isNaN (built-in)
 */
export const isNotNaN = (x: number): x is number => isNumber(x) && !isNaN(x);

/**
 * Type guard for the `object` type
 */
export const isObject = (x: unknown): x is object =>
  !isNil(x) && typeof x === 'object' && x instanceof Object;

/**
 * Type guard for the `object` type with `Object.keys().length === 0`
 */
export const isEmptyObject = (x: unknown): x is Record<string, never> =>
  isObject(x) && isEmptyArray(Object.keys(x));

/**
 * Type guard for the `Array` type
 */
export const isArray = <T>(as: Array<T> | unknown): as is Array<T> => Array.isArray(as);

/**
 * Type guard for the `Array` type with `.length === 0`
 */
export const isEmptyArray = <T>(as: Array<T> | unknown): as is [] => isArray(as) && as.length === 0;

/**
 * Type guard for the `Array` type with `.length > 0`
 */
export const isNonEmptyArray = <T>(as: Array<T> | unknown): as is Array<T> =>
  isArray(as) && as.length > 0;

/**
 * Type guard for checking a value is an enum value
 */
export const isStringEnumOption = <T extends string, EnumValue extends string>(
  value: string,
  enumVariable: {
    [key in T]: EnumValue;
  }
): value is EnumValue => Object.values(enumVariable).includes(value);

export const isCategoryColorKey = (x: unknown): x is keyof CategoryPalette =>
  CATEGORY_KEYS.includes(x as keyof CategoryPalette);

/**
 * Type guard for checking a value is a React Element
 */

export const isReactElement = (value: any): value is JSX.Element => {
  return typeof value === 'object' && value !== null && 'props' in value;
};

// Note, MUI is missing checkbox size classes and color classes so alternative selectors were used
// size classes are fixed in version 5.14.8
// https://github.com/mui/material-ui/pull/38401
const STATE_COLORS = ['error', 'info'] as const;
type StateColor = typeof STATE_COLORS[number];

export const isStateColor = (color: string): color is StateColor =>
  (STATE_COLORS as ReadonlyArray<string>).includes(color);

export const isIconType = (x: unknown): x is IconType => {
  const iconMap = ICON_MAP as Record<string, IconMapType[IconType]>;

  return typeof x === 'string' && !!iconMap[x];
};
