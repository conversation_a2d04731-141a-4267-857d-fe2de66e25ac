/**
 * Returns a hex value that is a tint of the provided hex value.
 * @param opacity A number between 0 and 1.
 */
export function getTint(hexValue: string, opacity: number): string {
  // Remove the # symbol if it exists
  hexValue = hexValue.replace('#', '');

  // Parse the hex value into RGB components
  const red = parseInt(hexValue.slice(0, 2), 16);
  const green = parseInt(hexValue.slice(2, 4), 16);
  const blue = parseInt(hexValue.slice(4, 6), 16);

  // Calculate the opacity values for each RGB component based on the percentage
  const newRed = Math.round((1 - opacity) * 255 + opacity * red);
  const newGreen = Math.round((1 - opacity) * 255 + opacity * green);
  const newBlue = Math.round((1 - opacity) * 255 + opacity * blue);

  // Convert the new RGB values back to hex
  return `#${newRed.toString(16)}${newGreen.toString(16)}${newBlue.toString(16)}`;
}

/**
 * Returns a rgba value of the provided hex value and.
 * @param opacity A number between 0 and 1.
 */
export function getOpacity(hexValue: string, opacity: number): string {
  // Remove the # symbol if it exists
  hexValue = hexValue.replace('#', '');

  // Parse the hex value into RGB components
  const red = parseInt(hexValue.slice(0, 2), 16);
  const green = parseInt(hexValue.slice(2, 4), 16);
  const blue = parseInt(hexValue.slice(4, 6), 16);

  // Convert the new RGB values to RGBA
  return `rgba(${red}, ${green}, ${blue}, ${opacity})`;
}
