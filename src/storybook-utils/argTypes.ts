/**
 * Because there are issues with auto generating our story controls from our MUI component prop type definitions,
 * this file provides a lists of hard-coded argtypes that are categorized for shared usage.
 * Details of argType definition below
 * https://storybook.js.org/docs/react/api/arg-types#argtypes
 * Details of available control properties below
 * https://storybook.js.org/docs/react/essentials/controls#annotation
 */

import {type ArgTypes} from '@storybook/react';

export const ACCESSIBILITY_ARGTYPES = {
  'aria-describedby': {
    description: 'The id(s) of the element(s) that describe the dialog',
    control: 'text',
    table: {
      category: 'Accessibility',
    },
  },
  'aria-labelledby': {
    description: 'The id(s) of the element(s) that label the dialog.',
    control: 'text',
    table: {
      category: 'Accessibility',
    },
  },
};

export const POPPER_ARGTYPES = {
  disablePortal: {
    description:
      'If true, the Popper content will be under the DOM hierarchy of the parent component.',
    control: 'boolean',
    table: {
      defaultValue: {summary: 'false'},
    },
  },
};

/**
 * Argtype definitions for MUI System Props (CSS utility props)
 * https://mui.com/system/properties/
 */
export const SYSTEM_ARGTYPES = {
  children: {
    description: 'The content of the component',
    control: {
      type: 'node',
    },
    table: {
      category: 'Nested Components',
    },
  },
  classes: {
    description: 'Override or extend the styles applied to the component.',
    control: {
      type: 'object',
    },
    table: {
      category: 'system',
    },
  },
  component: {
    description:
      'The component used for the root node. Either a string to use a HTML element or a component.',
    table: {
      category: 'system',
      type: {summary: 'elementType'},
    },
  },
  components: {
    description:
      'This prop is an alias for the slots prop. It is recommended to use the slots prop instead.      ',
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
      defaultValue: {summary: '{}'},
    },
    control: 'object',
  },
  componentsProps: {
    description:
      'This prop is an alias for the slots prop. It is recommended to use the slots prop instead.      ',
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
      defaultValue: {summary: '{}'},
    },
    control: 'object',
  },
  slotProps: {
    table: {
      category: 'Nested Components',
      type: {
        summary: '{}',
      },
    },
    description:
      'The extra props for the slot components. You can override the existing props or add new ones. This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.',
  },
  slots: {
    table: {
      category: 'Nested Components',
      type: {
        summary: '{}',
      },
    },
    description:
      'The components used for each slot inside. This prop is an alias for the `components` prop, which will be deprecated in the future.',
  },
  sx: {
    table: {
      category: 'system',
      type: {
        summary: 'SxProps<Theme>',
      },
    },
    control: 'object',
    description:
      'The system prop that allows defining system overrides as well as additional CSS styles.',
  },
} as const;

export const ANIMATION_ARGTYPES = {
  TransitionComponent: {
    table: {
      category: 'Nested Components',
    },
    description: `The component used for the transition. Follow <a href="https://mui.com/material-ui/transitions/#transitioncomponent-prop" target="_blank">this guide</a> to learn more about the requirements for this component.`,
    control: {type: 'elementType'},
  },
  TransitionProps: {
    table: {
      category: 'Nested Components',
    },
    description:
      'Props applied to the transition element. By default, the element is based on this <a href="https://reactcommunity.org/react-transition-group/transition" target="_blank">Transition</a> component.',
    control: {type: 'object'},
  },
  transitionDuration: {
    description:
      'The duration for the transition, in milliseconds. You may specify a single timeout for all transitions, or individually with an object.',
    control: {type: 'object'},
  },
} as const;

/**
 * Argtype definitions for typical MUI form control component props
 * ex. checked, disabled, required, onChange
 */
export const FORM_COMPONENT_ARGTYPES = {
  autoComplete: {
    description:
      'This prop helps users to fill forms faster, especially on mobile devices. The name can be confusing, as its more like an autofill. You can learn more about it following the specification',
    control: {
      type: 'text',
    },
  },
  autoFocus: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If `true`, the input element is focused during the first mount.',
  },
  checked: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If `true`, the component is checked.',
  },
  hiddenLabel: {
    description:
      'If `true`, the input label will be hidden. This is used to increase density for a filled input.',
    table: {
      type: {summary: 'boolean'},
      defaultValue: {
        summary: false,
      },
    },
  },
  defaultChecked: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description:
      'The default checked state. Use when the component is not controlled. If `true`, the component is checked.',
  },
  defaultOpen: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description:
      'If `true`, the component is initially open. Use when the component open state is not controlled (i.e. the `open` prop is not defined). You can only use it when the `native` prop is `false` (default).',
  },
  defaultValue: {
    description: 'The default input element value. Use when the component is not controlled.',
    table: {
      type: {summary: 'any'},
      defaultValue: {
        summary: '',
      },
    },
    control: 'text',
  },
  disabled: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If `true`, the component is disabled.',
  },
  edge: {
    table: {
      type: {summary: `end | start | false`},
      defaultValue: {
        summary: false,
      },
    },
    description:
      'If given, uses a negative margin to counteract the padding on one side (this is often helpful for aligning the left or right side of the icon with content above or below, without ruining the border size and shape).',
    control: 'inline-radio',
    options: ['end', 'start', false],
  },
  endAdornment: {
    description: 'End InputAdornment for this component.',
    table: {
      type: {summary: 'node'},
      defaultValue: {
        summary: '',
      },
    },
    control: 'node',
  },
  error: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description:
      'If `true`, the component will indicate an error. The prop defaults to the value (false) inherited from the parent `FormControl` component.',
  },
  focused: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If `true`, the component is focused.',
  },
  FormHelperTextProps: {
    description: 'Props applied to the `FormHelperText` element.',
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
  },
  fullWidth: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If true, the input will take up the full width of its container.',
  },
  id: {
    description:
      'The id of the `input` element. Use this prop to make `label` and `helperText` accessible for screen readers.',
  },
  indeterminate: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description:
      'If `true`, the component appears indeterminate. This does not set the native input element to indeterminate due to inconsistent behavior across browsers. However, we set a `data-indeterminate` attribute on the `input`.',
  },
  input: {
    description: 'An `Input` element; does not have to be a material-ui specific `Input`.',
    table: {
      category: 'Nested Components',
      type: {summary: 'input'},
    },
  },
  inputComponent: {
    description:
      'The component used for the input element. Either a string to use a HTML element or a component.',
    table: {
      category: 'Nested Components',
      type: {summary: 'elementType'},
      defaultValue: {summary: 'input'},
    },
    control: 'text',
  },
  InputLabelProps: {
    description:
      'Props applied to the InputLabel element. Pointer events like onClick are enabled if and only if shrink is true.',
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
  },
  InputProps: {
    description:
      'Props applied to the `Input` element. It will be a `FilledInput`, `OutlinedInput` component depending on the variant prop value.',
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
  },
  inputProps: {
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
    description: 'Attributes applied to the `input` element.',
  },
  inputRef: {
    table: {
      category: 'Nested Components',
      type: {summary: 'ref'},
    },
    description: 'Pass a ref to the `input` element.',
  },
  label: {
    description:
      'The label of the `input`. It is only used for layout. The actual labelling is handled by `InputLabel` or `FormLabel`.',
  },
  labelId: {
    description:
      'The ID of an element that acts as an additional label. The Select will be labelled by the additional label and the selected value.',
    table: {
      type: {summary: 'string'},
    },
  },
  multiline: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If `true`, a `textarea` element will be rendered instead of an `input`.',
  },
  maxRows: {
    description: 'Maximum number of rows to display when multiline option is set to true.',
  },
  minRows: {
    description: 'Minimum number of rows to display when multiline option is set to true.',
  },
  name: {
    control: 'text',
    description: 'Name attribute of the `input` element.',
  },
  onChange: {
    table: {
      type: {
        summary: 'func',
      },
    },
    description:
      'Callback fired when the value is changed. <br /> `function(event: React.ChangeEvent) => void`<br />`event` The event source of the callback. You can pull out the new value by accessing <br />`event.target.value`',
  },
  onClose: {
    table: {
      type: {
        summary: 'func',
      },
    },
    description:
      'Callback fired when the component requests to be closed. Use it in either controlled (see the `open` prop), or uncontrolled mode (to detect when the Select collapses). <br />`function(event: object) => void`',
  },
  onOpen: {
    table: {
      type: {
        summary: 'func',
      },
    },
    description:
      'Callback fired when the component requests to be opened. Use it in either controlled (see the `open` prop), or uncontrolled mode (to detect when the Select expands). <br />`function(event: object) => void`',
  },
  open: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description:
      'If `true`, the component is shown. You can only use it when the native prop is `false` (default).',
  },
  placeholder: {
    description: 'The short hint displayed in the input before the user enters a value.',
    table: {
      type: {
        summary: 'string',
      },
    },
    control: 'string',
  },
  readOnly: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description:
      'If `true`, the component is in read only state which prevents the user from changing the value of the field (not from interacting with the field).',
  },
  required: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If `true`, the component is required.',
  },
  rows: {
    description: 'Number of rows to display when multiline option is set to true.',
    table: {
      type: {summary: 'number | string'},
    },
  },
  select: {
    description:
      'Render a `Select` element while passing the Input element to Select as input parameter. If this option is set you must pass the options of the select as children.',
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
  },
  SelectProps: {
    description: 'Props applied to the `Select` element.',
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
  },
  startAdornment: {
    description: 'Start InputAdornment for this component.',
    table: {
      type: {summary: 'node'},
    },
  },
  type: {
    description: 'The type of `input` element to render. It should be a valid HTML5 input type.',
    table: {
      type: {summary: 'string'},
      defaultValue: {
        summary: 'text',
      },
    },
    control: 'select',
    options: ['text', 'search', 'number', 'date', 'password'],
  },
  value: {
    table: {
      type: {
        summary: 'any',
      },
    },
    control: 'text',
  },
  variant: {
    description: 'The variant to use.',
    control: 'inline-radio',
    options: ['outlined', 'filled'],
  },
} as const;

/**
 * Utility method for picking Argtypes given a list of argType keys and keyed object definition of Argtypes
 */
export const getArgTypes = (keys: Array<string>, argTypes: ArgTypes, sortKeys = true) =>
  (sortKeys ? keys.toSorted() : keys).reduce(
    (filteredArgTypes: ArgTypes, key) => ({
      ...filteredArgTypes,
      ...(argTypes[key] ? {[key]: argTypes[key]} : {[key]: {}}),
    }),
    {}
  );
/**
 * ONLY used as excludes while our auto generation incorrectly generates these
 */
export const controlsExclude = [
  'centerRipple',
  'disableRipple',
  'disableTouchRipple',
  'disableFocusRipple',
  'focusRipple',
  'TouchRippleProps',
  'touchRippleRef',
  'focusVisibleClassName',
  'onFocusVisible',
  'action',
  'component',
  'LinkComponent',
];
