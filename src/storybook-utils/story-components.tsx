import type {PropsWithChildren, ReactNode} from 'react';
import {Box, styled, Typography, useTheme} from '@mui/material';
import type {BoxProps} from '@mui/material';

export const StoryGrid = styled('div')`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  grid-auto-rows: 1fr;
  gap: 40px 20px;
`;

type StoryWrapperProps = {
  gap?: number;
};
export const StoryWrapper = styled('div')<StoryWrapperProps>`
  display: flex;
  flex-direction: column;
  align-items: center;
  ${({gap}) => gap && `gap: ${gap}px;`}
`;

type ClickToCopyProps = {
  children: string;
};
export function ClickToCopy({children}: ClickToCopyProps) {
  return (
    <CodeToCopy
      onClick={() => {
        navigator.clipboard.writeText(children);
      }}
      title="Click to copy"
    >
      {children}
    </CodeToCopy>
  );
}
const CodeToCopy = styled('code')`
  display: inline-block;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;

  :hover {
    background-color: #dddddd;
  }
`;

type ExternalLinkProps = {
  href: string;
};
export const ExternalLink = ({href, children}: PropsWithChildren<ExternalLinkProps>) => (
  <a href={href} target="_blank" rel="noreferrer">
    {children}
  </a>
);

type DocBlockProps = {
  children: ReactNode;
  subtext?: ReactNode;
};
export const DocBlock = ({children, subtext}: PropsWithChildren<DocBlockProps>) => (
  <Box mb={4}>
    <Typography variant="body1" gutterBottom>
      {children}
    </Typography>
    {!!subtext && <Typography variant="body2">{subtext}</Typography>}
  </Box>
);

type DocCardProps = BoxProps & {
  title?: string;
  subtitle?: string;
  clickToCopy?: string;
};
export const DocCard = ({title, subtitle, clickToCopy, ...rest}: DocCardProps) => {
  const theme = useTheme();
  return (
    <Box
      alignItems="center"
      border={1}
      borderColor="semanticPalette.stroke.main"
      borderRadius={theme.borderRadii.md}
      display="flex"
      flexDirection="column"
      flexShrink="0"
      flex="0 0 300"
      height={300}
      justifyContent="center"
      m={theme.spacing(4)}
      p={theme.spacing(4)}
      width={300}
      {...rest}
    >
      {!!title && (
        <Typography variant="h4" align="center">
          {title}
        </Typography>
      )}
      {!!subtitle && (
        <Typography variant="h5" align="center">
          {subtitle}
        </Typography>
      )}
      {!!clickToCopy && <ClickToCopy>{clickToCopy}</ClickToCopy>}
    </Box>
  );
};
