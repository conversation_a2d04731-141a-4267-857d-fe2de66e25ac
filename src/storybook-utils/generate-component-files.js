/* eslint-disable no-console */
import fs from 'fs';
import path from 'path';

fs.mkdir(path.join('./src/components', process.argv[2]), {}, err => {
  if (err) {
    return console.error(err);
  }
  console.log(process.argv[2], 'Directory created successfully!');
});

fs.writeFile(path.join(`./src/components/${process.argv[2]}/index.tsx`), '', {}, err => {
  if (err) {
    return console.error(err);
  }
  console.log(process.argv[2], 'Index created successfully!');
});
fs.writeFile(path.join(`./src/components/${process.argv[2]}/index.stories.tsx`), '', {}, err => {
  if (err) {
    return console.error(err);
  }
  console.log(process.argv[2], `Stories file for ${process.argv[2]} created successfully!`);
});

fs.writeFile(path.join(`./src/components/${process.argv[2]}/Docs.mdx`), '', {}, err => {
  if (err) {
    return console.error(err);
  }
  console.log(process.argv[2], `Docs MDX file for ${process.argv[2]} created successfully!`);
});
