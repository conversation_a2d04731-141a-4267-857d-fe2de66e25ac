/**
 * Removes properties that Storybook added to an imported object which had JSDocs.
 * Created because of this issue https://github.com/storybookjs/storybook/issues/9832
 */
export function removeStorybookInternalProperties<T>(
  obj: T & {__docgenInfo?: any; displayName?: string}
): Omit<T, '__docgenInfo' | 'displayName'> {
  // remove "__docgenInfo" and "displayName" properties
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const {__docgenInfo, displayName, ...rest} = obj;

  return rest;
}

/**
 * Categorizes the provided argTypes into the provided category to create an accordion in the Storybook docs.
 * @param argTypes The argTypes to categorize.
 * @param category The category to add to the argTypes.
 * @returns An object with the categorized argTypes.
 */

export const categorizeArgTypes = (argTypes: string[], category: string) => {
  if (!argTypes.length) return {};
  const argTypesMap: Record<string, any> = {};
  argTypes.forEach((argType: string) => {
    argTypesMap[argType] = {
      table: {
        category,
      },
    };
  });
  return argTypesMap;
};
