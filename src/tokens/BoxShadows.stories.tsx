import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {Box, Divider, Typography, useTheme} from 'src/index';
import type {BoxShadows as BoxShadowsType} from 'src/tokens/themeTypes';

import {ClickToCopy, DocBlock, DocCard, ExternalLink} from 'src/storybook-utils/story-components';

export default {
  title: 'Tokens/BoxShadow',
} as Meta;

export const BoxShadow: StoryObj = {
  render: () => {
    return (
      <>
        <BoxShadowCard boxShadowKey="boxShadows" />
        <Box my={4}>
          <Divider textAlign="left">
            <Typography variant="body2">Additional Information</Typography>
          </Divider>
        </Box>
        <BoxShadowCard boxShadowKey="shadows" />
      </>
    );
  },
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1778-16533',
    },
  },
};

const BoxShadowCard = ({boxShadowKey}: {boxShadowKey: 'shadows' | 'boxShadows'}) => {
  const theme = useTheme();

  let docTitle, docSubtitle, shadowKeys: string[];
  switch (boxShadowKey) {
    case 'shadows':
      docTitle = (
        <>
          These shadow options should not be referenced directly, and are defined as is required by
          the MUI theme. Please use <code>theme.boxShadows</code>.
        </>
      );
      // only used to render story to demonstrate MUI required definitions
      // not to be used directly in Design System components
      // eslint-disable-next-line no-restricted-properties
      shadowKeys = Object.keys(theme.shadows);
      break;
    case 'boxShadows':
    default:
      docTitle = (
        <>
          Theme boxShadow options can be referenced via <ClickToCopy>theme.boxShadows</ClickToCopy>
        </>
      );
      docSubtitle = (
        <>
          Note, these values are a reference to the base shadow definitions in{' '}
          <code>theme.shadows</code> and not actual CSS box-shadow definitions. This is because the
          boxShadow MUI prop is theme-aware. For example, the <code>boxShadow</code> property
          accepts <code>theme.shadows</code> keys (0 through 24). As such, you would specify a box
          shadow using a value from <code>theme.boxShadows</code> similar to{' '}
          <code>boxShadow=&#123;(theme) =&gt; theme.boxShadows.sm&#125;</code>.{' '}
          <ExternalLink href="https://mui.com/system/getting-started/the-sx-prop/#shadows">
            Read More
          </ExternalLink>{' '}
          about boxShadow usage.
        </>
      );
      shadowKeys = Object.keys(theme.boxShadows);
      break;
  }

  return (
    <>
      <DocBlock subtext={docSubtitle}>{docTitle}</DocBlock>
      <Box display="flex" mb={8}>
        {shadowKeys.map(key => {
          const shadow =
            boxShadowKey === 'boxShadows' ? theme.boxShadows[key as keyof BoxShadowsType] : key;
          return (
            <DocCard
              key={key}
              border={0}
              boxShadow={shadow}
              title={key}
              clickToCopy={`theme.${boxShadowKey}.${key}`}
              width="auto"
            />
          );
        })}
      </Box>
    </>
  );
};
