import {lighten} from '@mui/system/colorManipulator';

import {INPUT_PLACEHOLDER_OPACITY, PLACEHOLDER_COLOR} from '../constants';
import type {
  CategoryPalette,
  HeatMapCategoryPalette,
  HeatMapNegToPosPalette,
  SemanticPalette,
} from '../themeTypes';

const paletteFS = {
  soil: {
    '0': '#FFFFFF',
    '100': '#F7F7F7',
    '200': '#F1F1F1',
    '300': '#E0E0E0',
    '400': '#909090',
    '500': '#7A7A7A',
    '600': '#636363',
    '700': '#433F3B',
    '800': '#323232',
    '900': '#1B1B1B',
  },
  green: {
    '100': '#E7F5E6',
    '200': '#D7F0D4',
    '300': '#C0E8BA',
    '400': '#A2D29B',
    '500': '#399D2B',
    '600': '#005C00',
    '700': '#014601',
    '800': '#043504',
  },
  orange: {
    '100': '#FDF3D9',
    '200': '#FBF1D2',
    '300': '#FCEBB6',
    '400': '#FCE498',
    '500': '#F9D76D',
    '600': '#604A06',
    '700': '#453503',
    '800': '#3A2E06',
  },
  red: {
    '100': '#FFE6E0',
    '200': '#FFBEC1',
    '300': '#FF9398',
    '400': '#FF7A80',
    '500': '#FF0611',
    '600': '#C8040D',
    '700': '#5F0308',
    '800': '#45070A',
  },
  sprout: {
    '100': '#E7F5E6',
    '200': '#D6F1D3',
    '300': '#AADDA4',
    '400': '#85C97D',
    '500': '#5FB854',
    '600': '#2A7221',
    '700': '#0D3F07',
    '800': '#1A3417',
  },
  rain: {
    '100': '#EDF7F8',
    '200': '#BAE8EB',
    '300': '#9FDDE1',
    '400': '#75CACF',
    '500': '#44BAC1',
    '600': '#1A797E',
    '700': '#064246',
    '800': '#122543',
  },
  ocean: {
    '100': '#F2F6FB',
    '200': '#C8DBF9',
    '300': '#90B2E9',
    '400': '#6391DC',
    '500': '#4988EF',
    '600': '#2759AB',
    '700': '#0A2B63',
    '800': '#122543',
  },
  lavender: {
    '100': '#F9F3FD',
    '200': '#E5CBF8',
    '300': '#CCA1EB',
    '400': '#A571CB',
    '500': '#934BCC',
    '600': '#69299A',
    '700': '#3D0965',
    '800': '#270D3C',
  },
  cherry: {
    '100': '#FCF4F8',
    '200': '#EEC1D9',
    '300': '#E58EBD',
    '400': '#CC639C',
    '500': '#C52C7E',
    '600': '#9B1C61',
    '700': '#620638',
    '800': '#3C0623',
  },
  clay: {
    '100': '#FFF8F8',
    '200': '#F3C6C6',
    '300': '#E99494',
    '400': '#D05D5D',
    '500': '#DC2828',
    '600': '#9C1515',
    '700': '#630808',
    '800': '#340808',
  },
  coffee: {
    '100': '#FEF9F2',
    '200': '#FDE8C9',
    '300': '#EDC891',
    '400': '#E1AA58',
    '500': '#E09018',
    '600': '#794D09',
    '700': '#543505',
    '800': '#3E290A',
  },
  mustard: {
    '100': '#FCFBF1',
    '200': '#FDF9D2',
    '300': '#F4EB9A',
    '400': '#ECE071',
    '500': '#F0DD2C',
    '600': '#675E0C',
    '700': '#4E4705',
    '800': '#423C07',
  },
} as const;

/**
 * @deprecated - Please use the MUI provider `theme.palette`
 * Flurosense legacy palette colors
 */
export const palette = {
  fs_main: {
    black: '#1B1B1B',
    gray_800: '#464646',
    gray_600: '#7A7A7A',
    gray_400: '#909090',
    gray_300: '#E0E0E0',
    gray_200: '#F1F1F1',
    gray_100: '#F7F7F7',
    white: '#FFFFFF',
    green_800: '#005C00',
    green_600: '#399D2B',
    green_400: '#A2D29B',
    green_200: '#E7F5E6',
    orange_800: '#3E290A',
    orange_600: '#E09018',
    orange_400: '#E1AA58',
    orange_200: '#FEF9F2',
    red_800: '#340808',
    red_600: '#FF0611',
    red_400: '#FF8A8A',
    red_200: '#FFE6E0',
  },
  fs_category: {
    olive_800: '#1A3417',
    olive_600: '#5FB854',
    olive_400: '#85C97D',
    olive_200: '#EDF1E5',
    teal_800: '#173133',
    teal_600: '#44BAC1',
    teal_400: '#75CACF',
    teal_200: '#EDF7F8',
    blue_800: '#122543',
    blue_600: '#4988EF',
    blue_400: '#6391DC',
    blue_200: '#F2F6FB',
    purple_800: '#270D3C',
    purple_600: '#934BCC',
    purple_400: '#A571CB',
    purple_200: '#F9F3FD',
    magenta_800: '#3C0623',
    magenta_600: '#C52C7E',
    magenta_400: '#CC639C',
    magenta_200: '#FCF4F8',
    red_800: '#340808',
    red_600: '#DC2828',
    red_400: '#D05D5D',
    red_200: '#FFF8F8',
    orange_800: '#3E290A',
    orange_600: '#E09018',
    orange_400: '#E1AA58',
    orange_200: '#FEF9F2',
    yellow_800: '#423C07',
    yellow_600: '#F0DD2C',
    yellow_400: '#ECE071',
    yellow_200: '#FCFBF1',
  },
} as const;

/**
 * @deprecated - Please use the MUI provider `theme.palette.semanticPalette`
 */
export const semanticPalette = {
  /**
   * These colors are used for text and icons.
   */
  text: {
    /**
     * our black
     */
    main: palette.fs_main.black,
    /**
     * white
     */
    mainInverted: palette.fs_main.white,
    /**
     * gray_600
     */
    secondary: palette.fs_main.gray_600,
    /**
     * gray_400
     */
    secondaryInverted: palette.fs_main.gray_400,
    /**
     * green_600
     */
    brand: palette.fs_main.green_600,
    /**
     * red_600
     */
    error: palette.fs_main.red_600,
    /**
     * green_600
     */
    success: palette.fs_main.green_600,
    /**
     * orange_600
     */
    warning: palette.fs_main.orange_600,
    /**
     * blue_600
     */
    info: palette.fs_category.blue_600,
  },
  textInverted: {
    main: palette.fs_main.white,
    secondary: palette.fs_main.gray_400,
    brand: PLACEHOLDER_COLOR,
    error: PLACEHOLDER_COLOR,
    success: PLACEHOLDER_COLOR,
    warning: PLACEHOLDER_COLOR,
    info: PLACEHOLDER_COLOR,
  },
  /**
   * These colors are used for backgrounds.
   */
  surface: {
    /**
     * white
     */
    main: palette.fs_main.white,
    /**
     * our black
     */
    mainInverted: palette.fs_main.black,
    /**
     * gray_100
     */
    secondary: palette.fs_main.gray_100,
    /**
     * gray_800
     */
    secondaryInverted: palette.fs_main.gray_800,
    /**
     * green_200
     */
    brand: palette.fs_main.green_200,
    /**
     * red_200
     */
    error: palette.fs_main.red_200,
    /**
     * green_200
     */
    success: palette.fs_main.green_200,
    /**
     * orange_200
     */
    warning: palette.fs_main.orange_200,
    /**
     * blue_200
     */
    info: palette.fs_category.blue_200,
  },
  surfaceInverted: {
    main: palette.fs_main.black,
    mainInverted: palette.fs_main.white,
    secondary: palette.fs_main.gray_800,
    secondaryInverted: palette.fs_main.gray_100,
    brand: palette.fs_main.green_800,
    error: palette.fs_main.red_800,
    success: palette.fs_main.green_800,
    warning: palette.fs_main.orange_800,
    info: palette.fs_category.blue_800,
  },
  /**
   * These colors are used for borders and dividers.
   */
  stroke: {
    /**
     * gray_200
     */
    subtle: palette.fs_main.gray_200,
    /**
     * gray_800
     */
    subtleInverted: palette.fs_main.gray_800,
    /**
     * gray_300
     */
    strong: palette.fs_main.gray_300,
    /**
     * gray_400
     */
    strongInverted: palette.fs_main.gray_400,
    /**
     * green_600
     */
    brand: palette.fs_main.green_600,
    /**
     * red_600
     */
    error: palette.fs_main.red_600,
    /**
     * green_600
     */
    success: palette.fs_main.green_600,
    /**
     * orange_600
     */
    warning: palette.fs_main.orange_600,
    /**
     * blue_600
     */
    info: palette.fs_category.blue_600,
  },
  strokeInverted: {
    main: palette.fs_main.gray_400,
    secondary: palette.fs_main.gray_800,
    brand: PLACEHOLDER_COLOR,
    error: PLACEHOLDER_COLOR,
    success: PLACEHOLDER_COLOR,
    warning: PLACEHOLDER_COLOR,
    info: PLACEHOLDER_COLOR,
  },
  highlight: {
    main: palette.fs_main.gray_300,
    secondary: palette.fs_main.gray_300,
    mainInverted: palette.fs_main.gray_300,
    secondaryInverted: palette.fs_main.gray_300,
    brand: palette.fs_main.green_400,
    error: palette.fs_main.red_400,
    success: palette.fs_main.green_400,
    warning: palette.fs_main.orange_400,
    info: palette.fs_category.blue_400,
  },
} as const;

/**
 * @deprecated - Please use the MUI provider `theme.palette.categoryPalette`
 */
export const categoryPalette = {
  green: {
    chart: palette.fs_main.green_600,
    text: palette.fs_main.green_800,
    stroke: palette.fs_main.green_800,
    foreground: palette.fs_main.green_400,
    background: palette.fs_main.green_200,
    surfaceInverted: palette.fs_main.green_800,
  },
  olive: {
    chart: palette.fs_category.olive_600,
    text: palette.fs_category.olive_800,
    stroke: palette.fs_category.olive_800,
    foreground: palette.fs_category.olive_400,
    background: palette.fs_category.olive_200,
    surfaceInverted: palette.fs_category.olive_800,
  },
  teal: {
    chart: palette.fs_category.teal_600,
    text: palette.fs_category.teal_800,
    stroke: palette.fs_category.teal_800,
    foreground: palette.fs_category.teal_400,
    background: palette.fs_category.teal_200,
    surfaceInverted: palette.fs_category.teal_800,
  },
  blue: {
    chart: palette.fs_category.blue_600,
    text: palette.fs_category.blue_800,
    stroke: palette.fs_category.blue_800,
    foreground: palette.fs_category.blue_400,
    background: palette.fs_category.blue_200,
    surfaceInverted: palette.fs_category.blue_800,
  },
  purple: {
    chart: palette.fs_category.purple_600,
    text: palette.fs_category.purple_800,
    stroke: palette.fs_category.purple_800,
    foreground: palette.fs_category.purple_400,
    background: palette.fs_category.purple_200,
    surfaceInverted: palette.fs_category.purple_800,
  },
  magenta: {
    chart: palette.fs_category.magenta_600,
    text: palette.fs_category.magenta_800,
    stroke: palette.fs_category.magenta_800,
    foreground: palette.fs_category.magenta_400,
    background: palette.fs_category.magenta_200,
    surfaceInverted: palette.fs_category.magenta_800,
  },
  red: {
    chart: palette.fs_category.red_600,
    text: palette.fs_category.red_800,
    stroke: palette.fs_category.red_800,
    foreground: palette.fs_category.red_400,
    background: palette.fs_category.red_200,
    surfaceInverted: palette.fs_category.red_800,
  },
  orange: {
    chart: palette.fs_category.orange_600,
    text: palette.fs_category.orange_800,
    stroke: palette.fs_category.orange_800,
    foreground: palette.fs_category.orange_400,
    background: palette.fs_category.orange_200,
    surfaceInverted: palette.fs_category.orange_800,
  },
  yellow: {
    chart: palette.fs_category.yellow_600,
    text: palette.fs_category.yellow_800,
    stroke: palette.fs_category.yellow_800,
    foreground: palette.fs_category.yellow_400,
    background: palette.fs_category.yellow_200,
    surfaceInverted: palette.fs_category.yellow_800,
  },
} as const;

export const heatMapCategoryPalette: HeatMapCategoryPalette = {
  '1': {
    '1': paletteFS.sprout[100],
    '2': paletteFS.sprout[200],
    '3': paletteFS.sprout[300],
    '4': paletteFS.sprout[400],
    '5': paletteFS.sprout[500],
    '6': paletteFS.sprout[600],
    '7': paletteFS.sprout[700],
    '8': paletteFS.sprout[800],
  },
  '2': {
    '1': paletteFS.rain[100],
    '2': paletteFS.rain[200],
    '3': paletteFS.rain[300],
    '4': paletteFS.rain[400],
    '5': paletteFS.rain[500],
    '6': paletteFS.rain[600],
    '7': paletteFS.rain[700],
    '8': paletteFS.rain[800],
  },
  '3': {
    '1': paletteFS.ocean[100],
    '2': paletteFS.ocean[200],
    '3': paletteFS.ocean[300],
    '4': paletteFS.ocean[400],
    '5': paletteFS.ocean[500],
    '6': paletteFS.ocean[600],
    '7': paletteFS.ocean[700],
    '8': paletteFS.ocean[800],
  },
  '4': {
    '1': paletteFS.lavender[100],
    '2': paletteFS.lavender[200],
    '3': paletteFS.lavender[300],
    '4': paletteFS.lavender[400],
    '5': paletteFS.lavender[500],
    '6': paletteFS.lavender[600],
    '7': paletteFS.lavender[700],
    '8': paletteFS.lavender[800],
  },
  '5': {
    '1': paletteFS.cherry[100],
    '2': paletteFS.cherry[200],
    '3': paletteFS.cherry[300],
    '4': paletteFS.cherry[400],
    '5': paletteFS.cherry[500],
    '6': paletteFS.cherry[600],
    '7': paletteFS.cherry[700],
    '8': paletteFS.cherry[800],
  },
  '6': {
    '1': paletteFS.clay[100],
    '2': paletteFS.clay[200],
    '3': paletteFS.clay[300],
    '4': paletteFS.clay[400],
    '5': paletteFS.clay[500],
    '6': paletteFS.clay[600],
    '7': paletteFS.clay[700],
    '8': paletteFS.clay[800],
  },
  '7': {
    '1': paletteFS.coffee[100],
    '2': paletteFS.coffee[200],
    '3': paletteFS.coffee[300],
    '4': paletteFS.coffee[400],
    '5': paletteFS.coffee[500],
    '6': paletteFS.coffee[600],
    '7': paletteFS.coffee[700],
    '8': paletteFS.coffee[800],
  },
  '8': {
    '1': paletteFS.mustard[100],
    '2': paletteFS.mustard[200],
    '3': paletteFS.mustard[300],
    '4': paletteFS.mustard[400],
    '5': paletteFS.mustard[500],
    '6': paletteFS.mustard[600],
    '7': paletteFS.mustard[700],
    '8': paletteFS.mustard[800],
  },
};
export const heatMapNegToPosPalette: HeatMapNegToPosPalette = {
  'neg-to-pos-1': {
    '-6': paletteFS.clay[600],
    '-5': paletteFS.clay[500],
    '-4': paletteFS.clay[400],
    '-3': paletteFS.clay[300],
    '-2': paletteFS.clay[200],
    '-1': paletteFS.clay[100],
    '0': paletteFS.soil[100],
    '1': paletteFS.sprout[100],
    '2': paletteFS.sprout[200],
    '3': paletteFS.sprout[300],
    '4': paletteFS.sprout[400],
    '5': paletteFS.sprout[500],
    '6': paletteFS.sprout[600],
  },
  'neg-to-pos-2': {
    '-6': paletteFS.mustard[600],
    '-5': paletteFS.mustard[500],
    '-4': paletteFS.mustard[400],
    '-3': paletteFS.mustard[300],
    '-2': paletteFS.mustard[200],
    '-1': paletteFS.mustard[100],
    '0': paletteFS.soil[100],
    '1': paletteFS.ocean[100],
    '2': paletteFS.ocean[200],
    '3': paletteFS.ocean[300],
    '4': paletteFS.ocean[400],
    '5': paletteFS.ocean[500],
    '6': paletteFS.ocean[600],
  },
  'neg-to-pos-3': {
    '-6': paletteFS.coffee[600],
    '-5': paletteFS.coffee[500],
    '-4': paletteFS.coffee[400],
    '-3': paletteFS.coffee[300],
    '-2': paletteFS.coffee[200],
    '-1': paletteFS.coffee[100],
    '0': paletteFS.soil[100],
    '1': paletteFS.rain[100],
    '2': paletteFS.rain[200],
    '3': paletteFS.rain[300],
    '4': paletteFS.rain[400],
    '5': paletteFS.rain[500],
    '6': paletteFS.rain[600],
  },
};

// modfies color group keys to CATEGORY_KEYS ('0', '1', ... '8') and
// updates color value keys to CATEGORY_ELEMENT_KEYS ('surface', 'chart', ...)
const transformedCategoryPalettetoMUICategoryPalette = (() =>
  Object.values(categoryPalette).reduce(
    (acc, {background, surfaceInverted, chart, foreground, stroke, text}, i) => {
      const transformedCategoryPaletteItem: CategoryPalette[keyof CategoryPalette] = {
        surface: background,
        surfaceInverted,
        chart,
        highlight: foreground,
        stroke,
        text,
      };

      return {
        ...acc,
        [String(i)]: transformedCategoryPaletteItem,
      };
    },
    {}
  ))();

const transformedSemanticPalette: SemanticPalette = {
  text: {...semanticPalette.text},
  textInverted: semanticPalette.textInverted,
  surface: {
    ...semanticPalette.surface,
  },
  surfaceInverted: semanticPalette.surfaceInverted,
  stroke: {
    main: semanticPalette.stroke.strong,
    secondary: semanticPalette.stroke.subtle,
    error: semanticPalette.stroke.error,
    success: semanticPalette.stroke.success,
    warning: semanticPalette.stroke.warning,
    info: semanticPalette.stroke.info,
    brand: semanticPalette.stroke.brand,
  },
  strokeInverted: semanticPalette.strokeInverted,
  highlight: {
    ...semanticPalette.surface,
  },
};

export const muiPalette = {
  common: {
    black: palette.fs_main.black,
    white: palette.fs_main.white,
  },
  grey: {
    100: palette.fs_main.gray_100,
    200: palette.fs_main.gray_200,
    300: palette.fs_main.gray_300,
    400: palette.fs_main.gray_400,
    600: palette.fs_main.gray_600,
    800: palette.fs_main.gray_800,
    900: palette.fs_main.black,
    A100: palette.fs_main.gray_100,
    A200: palette.fs_main.gray_200,
    A400: palette.fs_main.gray_400,
    A700: palette.fs_main.gray_600,
  },
  primary: {
    main: semanticPalette.text.brand,
    // light: '',
    // dark: '',
    // contrastText: '',
  },
  secondary: {
    main: semanticPalette.text.secondary,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  error: {
    main: semanticPalette.text.error,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  warning: {
    main: semanticPalette.text.warning,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  success: {
    main: semanticPalette.text.success,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  info: {
    main: semanticPalette.text.info,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  text: {
    primary: semanticPalette.text.main,
    secondary: semanticPalette.text.secondary,
    disabled: palette.fs_main.gray_300,
    placeholder: lighten(palette.fs_main.gray_800, INPUT_PLACEHOLDER_OPACITY),
  },
  background: {
    paper: semanticPalette.surface.main,
    default: semanticPalette.surface.main,
    shadow: palette.fs_main.gray_800,
  },
  divider: semanticPalette.stroke.subtle,
  /**
   * These colors are intended to customize components beyond MUIs
   * base theme color keys and MUI automatic color generation.
   */
  semanticPalette: transformedSemanticPalette,
  /**
   * These colors are intended to differentiate items by category.
   * Generally used to style elements in dashboards and charts.
   */
  categoryPalette: transformedCategoryPalettetoMUICategoryPalette,

  /**
   * These colors are intended to apply visual representation of numeric bucketing
   */
  heatMapCategoryPalette,

  /**
   * These colors are intended to apply visual representation of numeric bucketing
   */
  heatMapNegToPosPalette,
};

/**
 * DO NOT REFERENCE DIRECTLY - only for rendering palette primitives story
 */
export const colorPrimitives = (() => {
  const transformed = Object.values(palette).reduce(
    (paletteAcc: Record<string, Record<string, string>>, colorValues) => ({
      ...paletteAcc,
      ...Object.entries(colorValues).reduce(
        (colorFamilyAcc: Record<string, Record<string, string>>, [colorKey, colorHex]) => {
          const [colorFamily, colorTone] = colorKey.split('_');
          if (!colorFamily || !colorTone) return colorFamilyAcc;
          return {
            ...colorFamilyAcc,
            [colorFamily]: {
              ...(colorFamilyAcc[colorFamily] ?? {}),
              [colorTone]: colorHex,
            },
          };
        },
        {}
      ),
    }),
    {}
  );
  transformed['gray']['0'] = palette.fs_main.white;
  transformed['gray']['900'] = palette.fs_main.black;

  return transformed;
  // the above code groups palette by color family and then indexes by TONE_KEYS
  // {gray: {100: a, 200: b, 300: c}, green: {100: x, 200: y, 300: z} ...}
})();
