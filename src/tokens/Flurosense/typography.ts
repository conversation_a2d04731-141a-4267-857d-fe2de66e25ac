import type {FontWeight} from 'src/tokens/themeTypes';
import type {TypographyOptions} from '@mui/material/styles/createTypography';

const fontWeights: FontWeight = {
  fontWeightLight: 400,
  fontWeightRegular: 400,
  fontWeightMedium: 700,
  fontWeightBold: 700,
};

export const typography: TypographyOptions = {
  fontFamily: `"Roboto", sans-serif`,
  fontSize: 14,
  ...fontWeights,
  h1: {
    fontSize: 48,
    fontWeight: fontWeights.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '64px',
  },
  h2: {
    fontSize: 36,
    fontWeight: fontWeights.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '48px',
  },
  h3: {
    fontSize: 24,
    fontWeight: fontWeights.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '32px',
  },
  h4: {
    fontSize: 16,
    fontWeight: fontWeights.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '24px',
  },
  h5: {
    fontSize: 14,
    fontWeight: fontWeights.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '20px',
  },
  h6: {
    fontSize: 12,
    fontWeight: fontWeights.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '16px',
  },
  body1: {
    fontSize: 14,
    fontWeight: fontWeights.fontWeightRegular,
    letterSpacing: 0,
    lineHeight: '20px',
  },
  body2: {
    fontSize: 12,
    fontWeight: fontWeights.fontWeightRegular,
    letterSpacing: 0,
    lineHeight: '16px',
  },
};
