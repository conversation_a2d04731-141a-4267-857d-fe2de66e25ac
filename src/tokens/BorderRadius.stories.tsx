import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {Box, useTheme} from 'src/index';
import type {BorderRadii} from 'src/tokens/themeTypes';

import {ClickToCopy, DocBlock, DocCard, ExternalLink} from 'src/storybook-utils/story-components';

export default {
  title: 'Tokens/BorderRadius',
} as Meta;

export const BorderRadius: StoryObj = {
  render: () => (
    <>
      <DocBlock
        subtext={
          <>
            Note, these values are factors of the base border radius (
            <code>theme.shape.borderRadius</code>) of 4px and not actual pixel values. This is
            because the borderRadius MUI prop is theme-aware. For example, the{' '}
            <code>borderRadius</code> property accepts numeric values. MUI multiplies the value it
            receives by the value of <code>theme.shape.borderRadius</code>. In this case, you would
            specify a value from <code>theme.borderRadii</code> similar to{' '}
            <code>borderRadius=&#123;(theme) =&gt; theme.borderRadii.sm&#125;</code>.{' '}
            <ExternalLink href="https://mui.com/system/getting-started/the-sx-prop/#borders">
              Read More
            </ExternalLink>{' '}
            about borderRadius usage.
          </>
        }
      >
        Border radius options can be referenced via <ClickToCopy>theme.borderRadii</ClickToCopy>
      </DocBlock>
      <Box display="flex">
        <BorderCard borderRadiusKey="sm" />
        <BorderCard borderRadiusKey="md" />
        <BorderCard borderRadiusKey="pill" />
      </Box>
    </>
  ),
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1778-16693&t=xRnADh4tURig20eJ-0',
    },
  },
};

interface BorderCardProps {
  borderRadiusKey: keyof BorderRadii;
}

const BorderCard = ({borderRadiusKey}: BorderCardProps) => {
  const theme = useTheme();
  let docTitle, docSubtitle;
  switch (borderRadiusKey) {
    case 'pill':
      docTitle = 'For pills';
      break;
    case 'md':
      docTitle = 'For cards, menus, alerts, dialogs';
      docSubtitle = `${theme.borderRadii[borderRadiusKey] * theme.shape.borderRadius}px`;
      break;
    case 'sm':
    default:
      docTitle = 'For buttons, inputs';
      docSubtitle = `${theme.borderRadii[borderRadiusKey] * theme.shape.borderRadius}px`;
      break;
  }
  return (
    <DocCard
      borderRadius={theme.borderRadii[borderRadiusKey]}
      title={docTitle}
      subtitle={docSubtitle}
      clickToCopy={`theme.borderRadii.${borderRadiusKey}`}
    />
  );
};
