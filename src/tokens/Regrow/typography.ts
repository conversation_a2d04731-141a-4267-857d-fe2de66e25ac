import type {FontWeight} from 'src/tokens/themeTypes';
import type {TypographyOptions} from '@mui/material/styles/createTypography';

const FONT_FAMILIES = {
  main: `"Inter", sans-serif`,
  secondary: `"DM Sans", sans-serif`,
} as const;

const FONT_WEIGHTS: FontWeight = {
  fontWeightLight: 400,
  fontWeightRegular: 400,
  fontWeightMedium: 600,
  fontWeightBold: 700,
} as const;

export const typography: TypographyOptions = {
  fontFamily: FONT_FAMILIES.main,
  fontSize: 14,
  ...FONT_WEIGHTS,
  h1: {
    fontFamily: FONT_FAMILIES.secondary,
    fontSize: 48,
    fontWeight: FONT_WEIGHTS.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '64px',
  },
  h2: {
    fontFamily: FONT_FAMILIES.secondary,
    fontSize: 36,
    fontWeight: FONT_WEIGHTS.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '48px',
  },
  h3: {
    fontFamily: FONT_FAMILIES.secondary,
    fontSize: 24,
    fontWeight: FONT_WEIGHTS.fontWeightBold,
    letterSpacing: 0,
    lineHeight: '32px',
  },
  h4: {
    fontFamily: FONT_FAMILIES.main,
    fontSize: 16,
    fontWeight: FONT_WEIGHTS.fontWeightMedium,
    letterSpacing: 0,
    lineHeight: '24px',
  },
  h5: {
    fontFamily: FONT_FAMILIES.main,
    fontSize: 14,
    fontWeight: FONT_WEIGHTS.fontWeightMedium,
    letterSpacing: 0,
    lineHeight: '20px',
  },
  h6: {
    fontFamily: FONT_FAMILIES.main,
    fontSize: 12,
    fontWeight: FONT_WEIGHTS.fontWeightMedium,
    letterSpacing: 0,
    lineHeight: '16px',
  },
  body1: {
    fontFamily: FONT_FAMILIES.main,
    fontSize: 14,
    fontWeight: FONT_WEIGHTS.fontWeightRegular,
    letterSpacing: 0,
    lineHeight: '20px',
  },
  body2: {
    fontFamily: FONT_FAMILIES.main,
    fontSize: 12,
    fontWeight: FONT_WEIGHTS.fontWeightRegular,
    letterSpacing: 0,
    lineHeight: '16px',
  },
};
