import {lighten} from '@mui/system/colorManipulator';

import {INPUT_PLACEHOLDER_OPACITY, PLACEHOLDER_COLOR} from '../constants';
import type {
  CategoryPalette,
  HeatMapCategoryPalette,
  HeatMapNegToPosPalette,
  SemanticPalette,
} from '../themeTypes';

/**
 * Regrow palette colors
 */
const palette = {
  soil: {
    '0': '#FFFFFF',
    '100': '#F8F8F3',
    '200': '#F1F1EC',
    '300': '#D5D2CB',
    '400': '#ABA5A0',
    '500': '#8C857F',
    '600': '#66605C',
    '800': '#363331',
    '700': '#433F3B',
    '900': '#191411',
  },
  green: {
    '100': '#F4FBF5',
    '200': '#BEECC6',
    '300': '#8CD998',
    '400': '#67B874',
    '500': '#30883E',
    '600': '#096117',
    '700': '#074812',
    '800': '#063A21',
  },
  orange: {
    '100': '#FFF4E9',
    '200': '#FCDBBD',
    '300': '#F9C493',
    '400': '#F7A559',
    '500': '#D67D2B',
    '600': '#9A4B03',
    '700': '#663101',
    '800': '#49290B',
  },
  red: {
    '100': '#FFF3F3',
    '200': '#F9C7C7',
    '300': '#F88787',
    '400': '#F05757',
    '500': '#CA3232',
    '600': '#AF0707',
    '700': '#810404',
    '800': '#4E0808',
  },
  sprout: {
    '100': '#F7FAF0',
    '200': '#E0ECC3',
    '300': '#C0D989',
    '400': '#9DC04D',
    '500': '#82A632',
    '600': '#49650C',
    '700': '#334904',
    '800': '#243304',
  },
  rain: {
    '100': '#EAF5F5',
    '200': '#D2E8E6',
    '300': '#BAD6D4',
    '400': '#93BFBC',
    '500': '#54948F',
    '600': '#11615B',
    '700': '#023C38',
    '800': '#042624',
  },
  ocean: {
    '100': '#ECF0F5',
    '200': '#CBD9EF',
    '300': '#B4CAEC',
    '400': '#8BA6CF',
    '500': '#506D99',
    '600': '#193B6C',
    '700': '#0C2B56',
    '800': '#0D0937',
  },
  lavender: {
    '100': '#F3F2F9',
    '200': '#DEDDEF',
    '300': '#C2BFE4',
    '400': '#9F9BCF',
    '500': '#655FA4',
    '600': '#1D1769',
    '700': '#181262',
    '800': '#0B073F',
  },
  cherry: {
    '100': '#F8ECF4',
    '200': '#F6D9EC',
    '300': '#EABBDA',
    '400': '#CC8FB7',
    '500': '#B66299',
    '600': '#4E1039',
    '700': '#43042D',
    '800': '#3A0729',
  },
  clay: {
    '100': '#F7ECEB',
    '200': '#F0DECE',
    '300': '#E4C3A5',
    '400': '#D28783',
    '500': '#B65853',
    '600': '#591713',
    '700': '#472506',
    '800': '#390A07',
  },
  coffee: {
    '100': '#F3EAE2',
    '200': '#F0DECE',
    '300': '#E4C3A5',
    '400': '#CEA27A',
    '500': '#B47C49',
    '600': '#543213',
    '700': '#472506',
    '800': '#3A2007',
  },
  mustard: {
    '100': '#F3EFE3',
    '200': '#F3E7C8',
    '300': '#EAD6A2',
    '400': '#DCBF76',
    '500': '#B49034',
    '600': '#5C460D',
    '700': '#423103',
    '800': '#322605',
  },
} as const;

const semanticPalette: SemanticPalette = {
  /**
   * These colors are used for text and icons.
   */
  text: {
    main: palette.soil['900'],
    secondary: palette.soil['600'],
    brand: palette.green['600'],
    error: palette.red['600'],
    success: palette.green['600'],
    warning: palette.orange['600'],
    info: palette.rain['600'],
  },
  textInverted: {
    main: palette.soil['0'],
    secondary: palette.soil['400'],
    brand: PLACEHOLDER_COLOR,
    error: PLACEHOLDER_COLOR,
    success: PLACEHOLDER_COLOR,
    warning: PLACEHOLDER_COLOR,
    info: PLACEHOLDER_COLOR,
  },
  /**
   * These colors are used for backgrounds.
   */
  surface: {
    main: palette.soil['0'],
    secondary: palette.soil['100'],
    brand: palette.green['100'],
    error: palette.red['100'],
    success: palette.green['100'],
    warning: palette.orange['100'],
    info: palette.rain['100'],
  },
  surfaceInverted: {
    main: palette.soil['900'],
    secondary: palette.soil['800'],
    brand: palette.green['800'],
    error: palette.red['800'],
    success: palette.green['800'],
    warning: palette.orange['800'],
    info: palette.rain['800'],
  },
  /**
   * These colors are used for borders and dividers.
   */
  stroke: {
    main: palette.soil['300'],
    secondary: palette.soil['200'],
    brand: palette.green['600'],
    error: palette.red['600'],
    success: palette.green['600'],
    warning: palette.orange['600'],
    info: palette.rain['600'],
  },
  strokeInverted: {
    main: palette.soil['400'],
    secondary: palette.soil['800'],
    brand: PLACEHOLDER_COLOR,
    error: PLACEHOLDER_COLOR,
    success: PLACEHOLDER_COLOR,
    warning: PLACEHOLDER_COLOR,
    info: PLACEHOLDER_COLOR,
  },
  /**
   * These colors are used as a highlight color on backgrounds
   */
  highlight: {
    main: palette.soil['300'],
    secondary: palette.soil['300'],
    brand: palette.green['400'],
    error: palette.red['400'],
    success: palette.green['400'],
    warning: palette.orange['400'],
    info: palette.rain['400'],
  },
} as const;

const categoryPalette: CategoryPalette = {
  '0': {
    surface: palette.green['100'],
    chart: palette.green['500'],
    highlight: palette.green['400'],
    stroke: palette.green['600'],
    text: palette.green['600'],
    surfaceInverted: palette.green['800'],
  },
  '1': {
    surface: palette.sprout['100'],
    chart: palette.sprout['500'],
    highlight: palette.sprout['400'],
    stroke: palette.sprout['600'],
    text: palette.sprout['600'],
    surfaceInverted: palette.sprout['800'],
  },
  '2': {
    surface: palette.rain['100'],
    chart: palette.rain['500'],
    highlight: palette.rain['400'],
    stroke: palette.rain['600'],
    text: palette.rain['600'],
    surfaceInverted: palette.rain['800'],
  },
  '3': {
    surface: palette.ocean['100'],
    chart: palette.ocean['500'],
    highlight: palette.ocean['400'],
    stroke: palette.ocean['600'],
    text: palette.ocean['600'],
    surfaceInverted: palette.ocean['800'],
  },
  '4': {
    surface: palette.lavender['100'],
    chart: palette.lavender['500'],
    highlight: palette.lavender['400'],
    stroke: palette.lavender['600'],
    text: palette.lavender['600'],
    surfaceInverted: palette.ocean['800'],
  },
  '5': {
    surface: palette.cherry['100'],
    chart: palette.cherry['500'],
    highlight: palette.cherry['400'],
    stroke: palette.cherry['600'],
    text: palette.cherry['600'],
    surfaceInverted: palette.cherry['800'],
  },
  '6': {
    surface: palette.clay['100'],
    chart: palette.clay['500'],
    highlight: palette.clay['400'],
    stroke: palette.clay['600'],
    text: palette.clay['600'],
    surfaceInverted: palette.clay['800'],
  },
  '7': {
    surface: palette.coffee['100'],
    chart: palette.coffee['500'],
    highlight: palette.coffee['400'],
    stroke: palette.coffee['600'],
    text: palette.coffee['600'],
    surfaceInverted: palette.coffee['800'],
  },
  '8': {
    surface: palette.mustard['100'],
    chart: palette.mustard['500'],
    highlight: palette.mustard['400'],
    stroke: palette.mustard['600'],
    text: palette.mustard['600'],
    surfaceInverted: palette.mustard['800'],
  },
  '9': {
    surface: palette.soil['100'],
    chart: palette.soil['500'],
    highlight: palette.soil['300'],
    stroke: palette.soil['600'],
    text: palette.soil['600'],
    surfaceInverted: palette.soil['800'],
  },
};

export const heatMapCategoryPalette: HeatMapCategoryPalette = {
  '1': {
    '1': palette.sprout[100],
    '2': palette.sprout[200],
    '3': palette.sprout[300],
    '4': palette.sprout[400],
    '5': palette.sprout[500],
    '6': palette.sprout[600],
    '7': palette.sprout[700],
    '8': palette.sprout[800],
  },
  '2': {
    '1': palette.rain[100],
    '2': palette.rain[200],
    '3': palette.rain[300],
    '4': palette.rain[400],
    '5': palette.rain[500],
    '6': palette.rain[600],
    '7': palette.rain[700],
    '8': palette.rain[800],
  },
  '3': {
    '1': palette.ocean[100],
    '2': palette.ocean[200],
    '3': palette.ocean[300],
    '4': palette.ocean[400],
    '5': palette.ocean[500],
    '6': palette.ocean[600],
    '7': palette.ocean[700],
    '8': palette.ocean[800],
  },
  '4': {
    '1': palette.lavender[100],
    '2': palette.lavender[200],
    '3': palette.lavender[300],
    '4': palette.lavender[400],
    '5': palette.lavender[500],
    '6': palette.lavender[600],
    '7': palette.lavender[700],
    '8': palette.lavender[800],
  },
  '5': {
    '1': palette.cherry[100],
    '2': palette.cherry[200],
    '3': palette.cherry[300],
    '4': palette.cherry[400],
    '5': palette.cherry[500],
    '6': palette.cherry[600],
    '7': palette.cherry[700],
    '8': palette.cherry[800],
  },
  '6': {
    '1': palette.clay[100],
    '2': palette.clay[200],
    '3': palette.clay[300],
    '4': palette.clay[400],
    '5': palette.clay[500],
    '6': palette.clay[600],
    '7': palette.clay[700],
    '8': palette.clay[800],
  },
  '7': {
    '1': palette.coffee[100],
    '2': palette.coffee[200],
    '3': palette.coffee[300],
    '4': palette.coffee[400],
    '5': palette.coffee[500],
    '6': palette.coffee[600],
    '7': palette.coffee[700],
    '8': palette.coffee[800],
  },
  '8': {
    '1': palette.mustard[100],
    '2': palette.mustard[200],
    '3': palette.mustard[300],
    '4': palette.mustard[400],
    '5': palette.mustard[500],
    '6': palette.mustard[600],
    '7': palette.mustard[700],
    '8': palette.mustard[800],
  },
};
export const heatMapNegToPosPalette: HeatMapNegToPosPalette = {
  'neg-to-pos-1': {
    '-6': palette.clay[600],
    '-5': palette.clay[500],
    '-4': palette.clay[400],
    '-3': palette.clay[300],
    '-2': palette.clay[200],
    '-1': palette.clay[100],
    '0': palette.soil[100],
    '1': palette.sprout[100],
    '2': palette.sprout[200],
    '3': palette.sprout[300],
    '4': palette.sprout[400],
    '5': palette.sprout[500],
    '6': palette.sprout[600],
  },
  'neg-to-pos-2': {
    '-6': palette.mustard[600],
    '-5': palette.mustard[500],
    '-4': palette.mustard[400],
    '-3': palette.mustard[300],
    '-2': palette.mustard[200],
    '-1': palette.mustard[100],
    '0': palette.soil[100],
    '1': palette.ocean[100],
    '2': palette.ocean[200],
    '3': palette.ocean[300],
    '4': palette.ocean[400],
    '5': palette.ocean[500],
    '6': palette.ocean[600],
  },
  'neg-to-pos-3': {
    '-6': palette.coffee[600],
    '-5': palette.coffee[500],
    '-4': palette.coffee[400],
    '-3': palette.coffee[300],
    '-2': palette.coffee[200],
    '-1': palette.coffee[100],
    '0': palette.soil[100],
    '1': palette.rain[100],
    '2': palette.rain[200],
    '3': palette.rain[300],
    '4': palette.rain[400],
    '5': palette.rain[500],
    '6': palette.rain[600],
  },
};

export const muiPalette = {
  common: {
    black: palette.soil['900'],
    white: palette.soil['0'],
  },
  grey: {
    100: palette.soil[100],
    200: palette.soil[200],
    300: palette.soil[300],
    400: palette.soil[400],
    600: palette.soil[600],
    800: palette.soil[800],
    900: palette.soil[900],
    A100: palette.soil[100],
    A200: palette.soil[200],
    A400: palette.soil[400],
    A700: palette.soil[600],
  },
  primary: {
    main: semanticPalette.text.brand,
    //   light: '',
    //   dark: '',
    // contrastText: '',
  },
  secondary: {
    main: semanticPalette.text.secondary,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  error: {
    main: semanticPalette.text.error,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  warning: {
    main: semanticPalette.text.warning,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  success: {
    main: semanticPalette.text.success,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  info: {
    main: semanticPalette.text.info,
    //   light: '',
    //   dark: '',
    //   contrastText: '',
  },
  text: {
    primary: semanticPalette.text.main,
    secondary: semanticPalette.text.secondary,
    disabled: palette.soil['400'],
    placeholder: lighten(palette.soil['600'], INPUT_PLACEHOLDER_OPACITY),
  },
  background: {
    paper: semanticPalette.surface.main,
    default: semanticPalette.surface.main,
    shadow: palette.soil['800'],
  },
  divider: semanticPalette.stroke.secondary,
  /**
   * These colors are intended to customize components beyond MUIs
   * base theme color keys and MUI automatic color generation.
   */
  semanticPalette,
  /**
   * These colors are intended to differentiate items by category.
   * Generally used to style elements in dashboards and charts.
   */
  categoryPalette,
  ...categoryPalette,

  /**
   * These colors are intended to apply visual representation of numeric bucketing
   */
  heatMapCategoryPalette,

  /**
   * These colors are intended to apply visual representation of numeric bucketing
   */
  heatMapNegToPosPalette,
};

/**
 * DO NOT REFERENCE DIRECTLY - only for rendering palette primitives story
 */
export const colorPrimitives = palette;
