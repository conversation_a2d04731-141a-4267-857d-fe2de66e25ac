/**
 * Available Themes
 */
export const enum THEME_KEYS {
  'REGROW_DEFAULT',
  'FLUROSENSE_LEGACY',
}

/**
 * Typography Variant keys
 */
export const TYPOGRAPHY_VARIANTS = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'body1', 'body2'] as const;

/**
 * Required palettes for any given theme
 */
export const PALETTE_KEYS = [
  'semanticPalette',
  'categoryPalette',
  'heatMapCategoryPalette',
  'heatMapNegToPosPalette',
] as const;

/**
 * Available keys for any custom color in a theme's base palette
 * all are optional
 */
export const TONE_KEYS = [
  '0',
  '100',
  '200',
  '300',
  '400',
  '500',
  '600',
  '700',
  '800',
  '900',
] as const;

/**
 * Available style elements for which a `semanticPalette` can define a set of colors per state
 * all are required
 */
export const SEMANTIC_ELEMENT_KEYS = [
  'text',
  'textInverted',
  'surface',
  'surfaceInverted',
  'stroke',
  'strokeInverted',
  'highlight',
] as const;

/**
 * Available states for each semantic element
 * all are required
 */
export const SEMANTIC_STATE_KEYS = [
  'main',
  'secondary',
  'brand',
  'error',
  'success',
  'warning',
  'info',
] as const;

/**
 * Available categories for which a `categoryPalette` can define a set of colors per element
 * all are required
 */
export const CATEGORY_KEYS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'] as const;

/**
 * Available elements for each category
 * all are required
 */
export const CATEGORY_ELEMENT_KEYS = [
  'chart',
  'text',
  'surface',
  'highlight',
  'stroke',
  'surfaceInverted',
] as const;

/**
 * Available for `heatMapNegToPosPalette`
 */
export const HEATMAP_NEG_TO_POS_KEYS = ['neg-to-pos-1', 'neg-to-pos-2', 'neg-to-pos-3'] as const;

export const HEATMAP_NEG_TO_POS_VALUE_KEYS = [
  '6',
  '5',
  '4',
  '3',
  '2',
  '1',
  '0',
  '-1',
  '-2',
  '-3',
  '-4',
  '-5',
  '-6',
] as const;

/**
 * Available for `heatMapCategoryPalette`
 */
export const HEATMAP_CATEGORY_KEYS = ['1', '2', '3', '4', '5', '6', '7', '8'] as const;

export const SIZE_KEYS = ['xs', 'sm', 'md', 'lg', 'xl'] as const;

/**
 * Color meant to be so ugly that it warns against using color keys that do not exist yet.
 */
export const PLACEHOLDER_COLOR = '#FF0EF3';

export const SPACING_CONSTANT = 4;
/**
 * Keyed spacing values
 *
 * xs = 4px, sm = 8px, md = 12px, lg = 16px, xl = 20px, xxl = 24px,
 */
export const KEYED_SPACING = {
  /** xs = 4px */
  xs: '4px' as const,
  /** sm = 8px */
  sm: '8px' as const,
  /** md = 12px */
  md: '12px' as const,
  /** lg = 16px */
  lg: '16px' as const,
  /** xl = 20px */
  xl: '20px' as const,
  /** xxl = 24px */
  xxl: '24px' as const,
};

/**
 * Constants for input placeholder opacity lightening function
 */
export const INPUT_PLACEHOLDER_OPACITY = 0.45;

/**
 * Constants for input readOnly opacity lightening function
 */
export const INPUT_READ_ONLY_OPACITY = 0.35;

/**
 * Constants for input disabled opacity
 */
export const INPUT_DISABLED_OPACITY = 0.65;
