import type {Meta, StoryObj} from '@storybook/react';
import {Stack, useTheme} from 'src/index';
import {SIZE_KEYS} from 'src/tokens/constants';
import type {FixedWidths} from 'src/tokens/themeTypes';

import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DocBlock, DocCard, ExternalLink} from 'src/storybook-utils/story-components';

export default {
  title: 'Tokens/FixedWidth',
} as Meta;

export const FixedWidth: StoryObj = {
  render: () => (
    <>
      <DocBlock
        subtext={
          <>
            Note, these are unitless pixel values as MUI converts sizing properties larger than 1 to
            pixel values. If the value is between (0, 1], it's converted to percent.
            <ExternalLink href="https://mui.com/system/sizing/#width">Read More</ExternalLink> about
            sizing properties.
          </>
        }
      >
        Fixed Width options can be referenced via <ClickToCopy>theme.fixedWidths</ClickToCopy>
      </DocBlock>
      <Stack>
        {SIZE_KEYS.map(size => (
          <FixedWidthCard widthKey={size} />
        ))}
      </Stack>
    </>
  ),
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=8225-18300&mode=design&t=FYOLteJvPm3EF3zM-0',
    },
  },
};

interface FixedWidthCardProps {
  widthKey: keyof FixedWidths;
}

const FixedWidthCard = ({widthKey}: FixedWidthCardProps) => {
  const theme = useTheme();
  return (
    <DocCard
      borderRadius={2}
      title={widthKey}
      subtitle={`${theme.fixedWidths[widthKey]}px`}
      clickToCopy={`theme.fixedWidth.${widthKey}`}
      width={theme.fixedWidths[widthKey]}
      height={100}
    />
  );
};
