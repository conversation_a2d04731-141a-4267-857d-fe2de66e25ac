import type {
  CATEGORY_ELEMENT_KEYS,
  CATEGORY_KEYS,
  HEATMAP_CATEGORY_KEYS,
  HEATMAP_NEG_TO_POS_KEYS,
  HEATMAP_NEG_TO_POS_VALUE_KEYS,
  KEYED_SPACING,
  PALETTE_KEYS,
  SEMANTIC_ELEMENT_KEYS,
  SEMANTIC_STATE_KEYS,
  SIZE_KEYS,
  THEME_KEYS,
} from 'src/tokens/constants';
import type {ICON_MAP} from 'src/tokens/icons';
import type {TypeBackground, TypeText} from '@mui/material';
import type {TypographyVariantsOptions} from '@mui/material/styles';

export type ThemeKeys = THEME_KEYS;

export type PaletteKeys = typeof PALETTE_KEYS[number];

export type ExtendTypeText = TypeText & {
  placeholder: string;
};

export type ExtendedBackgroundPalette = TypeBackground & {
  shadow: string;
};

export type GreyPalette = {
  100: string;
  200: string;
  300: string;
  400: string;
  600: string;
  800: string;
  900: string;
  A100: string;
  A200: string;
  A400: string;
  A700: string;
};

export type SemanticPalette = Record<
  typeof SEMANTIC_ELEMENT_KEYS[number],
  Record<typeof SEMANTIC_STATE_KEYS[number], string>
>;

export type CategoryPalette = Record<
  typeof CATEGORY_KEYS[number],
  Record<typeof CATEGORY_ELEMENT_KEYS[number], string>
>;

export type HeatMapCategoryPalette = Record<
  typeof HEATMAP_CATEGORY_KEYS[number],
  Record<typeof HEATMAP_CATEGORY_KEYS[number], string>
>;

export type HeatMapNegToPosPalette = Record<
  typeof HEATMAP_NEG_TO_POS_KEYS[number],
  Record<typeof HEATMAP_NEG_TO_POS_VALUE_KEYS[number], string>
>;

export type BorderRadii = {
  sm: number;
  md: number;
  pill: 9999;
};

export type BoxShadows = {
  sm: BoxShadowKey;
  md: BoxShadowKey;
  lg: BoxShadowKey;
};

type BoxShadowKey =
  | 0
  | 1
  | 2
  | 3
  | 4
  | 5
  | 6
  | 7
  | 8
  | 9
  | 10
  | 11
  | 12
  | 13
  | 14
  | 15
  | 16
  | 17
  | 18
  | 19
  | 20
  | 21
  | 22
  | 23
  | 24;

export type FontWeight = {
  fontWeightLight: TypographyVariantsOptions['fontWeightLight'];
  fontWeightRegular: TypographyVariantsOptions['fontWeightLight'];
  fontWeightMedium: TypographyVariantsOptions['fontWeightMedium'];
  fontWeightBold: TypographyVariantsOptions['fontWeightBold'];
};

type SizeKeys = typeof SIZE_KEYS[number];

export type FixedWidths = Record<SizeKeys, number>;

export type IconType = keyof typeof ICON_MAP;

export type IconMapType = typeof ICON_MAP;

export type KeyedSpacing = typeof KEYED_SPACING;
