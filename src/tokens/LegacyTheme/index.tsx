import {categoryPalette, palette, semanticPalette} from 'src/tokens/Flurosense/colors';

/**
 * @deprecated - Please use the MUI provider `theme`
 * Used to maintain Flurosense legacy theme and components
 * New feature development should rely on properties exposed in MUI theme
 */
export const theme = {
  /**
   * @deprecated - Please use the MUI provider `theme.palette.semanticPalette` object
   * Use semantic `color` instead of `colorPalette` when styling components.
   * If the color you need isn't in `color`, chat with the design team.
   */
  color: semanticPalette,
  /**
   * @deprecated - Please use the MUI provider `theme.palette.categoryPalette` object
   * These colors are intended to differentiate items by category.
   * Generally used to style elements in dashboards and charts.
   */
  categoryColor: categoryPalette,
  /**
   * @deprecated - Please use the MUI provider `theme.palette.semanticPalette` or `theme.palette.categoryPalette` objects
   * Use semantic `color` instead of `colorPalette` when styling components.
   * If the color you need isn't in `color`, chat with the design team.
   */
  colorPalette: palette,
  /**
   * @deprecated - Please use the MUI provider `theme.shape.borderRadius` and `theme.borderRadii`
   */
  borderRadius: {
    /**
     * 4px
     */
    small: '4px',
    /**
     * 8px
     */
    medium: '8px',
    /**
     * 16px
     */
    large: '16px',
  },
  /**
   * @deprecated - Please use the MUI provider `theme.shadows` array
   */
  elevation: {
    low: 'drop-shadow(0px 0px 1px rgba(79, 79, 79, 0.4))',
    medium: 'drop-shadow(0px 0px 2.5px rgba(79, 79, 79, 0.35))',
    high: 'drop-shadow(0px 0px 5px rgba(79, 79, 79, 0.3))',
  },
  /**
   * @deprecated - Please use the MUI provider `theme.typography` fontWeight definitions
   */
  fontWeight: {
    bold: 700,
    normal: 400,
  },
};

export type LegacyTheme = typeof theme;
