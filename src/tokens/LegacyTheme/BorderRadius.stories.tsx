import type {Meta, StoryObj} from '@storybook/react';

import {StoryWrapper} from 'src/storybook-utils/story-components';
// TODO - file will be removed when legacy theme is deprecated
// eslint-disable-next-line @typescript-eslint/no-restricted-imports
import {useTheme} from 'styled-components';

export default {
  title: 'Tokens/_Deprecated/Border Radius',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16693&t=LIcEGm0o9FzqNPdy-4',
    },
  },
} as Meta;

export const BorderRadius: StoryObj = {
  render: () => <ExampleWithTheme />,
};

function ExampleWithTheme() {
  const theme = useTheme();

  return (
    <div
      style={{
        display: 'flex',
        gap: '68px',
      }}
    >
      <StoryWrapper gap={16}>
        <div
          style={{
            width: '300px',
            height: '300px',
            backgroundColor: theme.color.surface.main,
            borderRadius: theme.borderRadius.small,
            filter: theme.elevation.medium,
          }}
        />
        <div>Small ({theme.borderRadius.small})</div>
        <div>For buttons and inputs</div>
      </StoryWrapper>
      <StoryWrapper gap={16}>
        <div
          style={{
            width: '300px',
            height: '300px',
            backgroundColor: theme.color.surface.main,
            borderRadius: theme.borderRadius.medium,
            filter: theme.elevation.medium,
          }}
        />
        <div>Medium ({theme.borderRadius.medium})</div>
        <div>For cards, menus, alerts</div>
      </StoryWrapper>
    </div>
  );
}
