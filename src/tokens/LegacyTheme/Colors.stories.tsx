import type {Meta, StoryObj} from '@storybook/react';
import {Fragment} from 'react';
import {removeStorybookInternalProperties} from 'src/storybook-utils/storybook';
import {getTypedEntries, getTypedKeys} from 'src/utils/object';
import {upperCaseFirstLetter} from 'src/utils/string';

import {ClickToCopy, StoryGrid, StoryWrapper} from 'src/storybook-utils/story-components';
// eslint-disable-next-line @typescript-eslint/no-restricted-imports
import styled, {css, useTheme} from 'styled-components';

export default {
  title: 'Tokens/_Deprecated/Colors',
} as Meta;

export const SemanticColors: StoryObj = {
  render: () => <ColorList type="semantic" />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1777%3A17350&viewport=778%2C433%2C0.21&t=qJLyuO8ZnjmABZ56-0',
    },
    layout: 'centered',
  },
};

export const CategoryColors: StoryObj = {
  render: () => <ColorList type="category" />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=2164%3A17758&viewport=778%2C433%2C0.21&t=VHV3482bLNly3ZKK-0',
    },
    layout: 'centered',
  },
};

export const ColorPalette: StoryObj = {
  render: () => <ColorPaletteWithTheme />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16594&viewport=778%2C433%2C0.21&t=qJLyuO8ZnjmABZ56-0',
    },
  },
};

function ColorPaletteWithTheme() {
  const {colorPalette} = useTheme();
  const colors = getTypedEntries(removeStorybookInternalProperties(colorPalette))
    .map(([category, colorRecord]) => {
      return getTypedEntries(colorRecord).map(([colorName, color]) => {
        return {
          label: `${category} ${colorName}`,
          color,
        };
      });
    })
    .flat();

  return (
    <>
      <StoryGrid>
        {colors.map(({label, color}) => (
          <ColorWrapper key={label} color={color} label={label} />
        ))}
      </StoryGrid>
    </>
  );
}

type ColorListProps = {
  type: 'semantic' | 'category';
};
function ColorList({type}: ColorListProps) {
  const theme = useTheme();

  const colorVariantMap =
    type === 'semantic' ? theme.colorPalette.fs_main : theme.colorPalette.fs_category;

  const colorEntries =
    type === 'semantic' ? getTypedEntries(theme.color) : getTypedEntries(theme.categoryColor);

  const colorCategories = colorEntries.map(([category, variantMap]) => {
    const colors = getTypedEntries(variantMap).map(([variant, color]) => {
      const label = upperCaseFirstLetter(`${category} ${variant}`);

      return {
        label,
        color,
      };
    });

    return {
      category,
      colors,
    };
  });

  return (
    <Table>
      <thead>
        <tr>
          <th>{`${upperCaseFirstLetter(type)} name`}</th>
          <th>Color</th>
        </tr>
      </thead>
      <tbody>
        {colorCategories.map(({category, colors}, index) => {
          const lastRowInSectionIndex = colors.length - 1;
          const lastRow = index === colorCategories.length - 1;

          function getStyle(rowIndex: number) {
            if (!lastRow && rowIndex === lastRowInSectionIndex) {
              return {borderBottom: '1px solid black'};
            }
          }

          return (
            <Fragment key={category}>
              {colors.map(({label, color}, rowIndex) => (
                <tr key={label} style={getStyle(rowIndex)}>
                  <td>{label}</td>
                  <td>
                    <ColorWrapper color={color} colorVariantMap={colorVariantMap} inline />
                  </td>
                </tr>
              ))}
            </Fragment>
          );
        })}
      </tbody>
    </Table>
  );
}

type ColorWrapperProps = {
  color: string;
  colorVariantMap?: ColorVariantMap;
  label?: string;
  inline?: boolean;
};
function ColorWrapper({color, colorVariantMap, label, inline}: ColorWrapperProps) {
  return (
    <ColorWrapperFlex $inline={inline}>
      <ColorCircle color={color} $inline={inline} />
      <ClickToCopy>{color}</ClickToCopy>
      {colorVariantMap ? getColorName(color, colorVariantMap) : label}
    </ColorWrapperFlex>
  );
}

type ColorCircleProps = {
  color: string;
  $inline?: boolean;
};
const ColorCircle = styled.div<ColorCircleProps>`
  width: 50px;
  height: 50px;
  border-radius: 100px;
  background-color: ${(props: ColorCircleProps) => props.color};

  ${(props: ColorCircleProps) =>
    props.$inline &&
    css`
      width: 32px;
      height: 32px;
    `};
`;

const Table = styled.table`
  border-collapse: collapse;

  th {
    text-align: left;
  }

  td,
  th {
    padding: 12px;
  }
`;

type ColorWrapperFlexProps = {
  $inline?: boolean;
};
const ColorWrapperFlex = styled(StoryWrapper)<ColorWrapperFlexProps>`
  gap: 8px;

  ${(props: ColorWrapperFlexProps) =>
    props.$inline &&
    css`
      flex-direction: row;
      align-items: center;
      gap: 16px;
    `}
`;

type ColorVariantMap = Record<string, string>;

function getColorName(hex: string, colorVariantMap: ColorVariantMap): string | undefined {
  return getTypedKeys(colorVariantMap).find(key => colorVariantMap[key] === hex);
}
