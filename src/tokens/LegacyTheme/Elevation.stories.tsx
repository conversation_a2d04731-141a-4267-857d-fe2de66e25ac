import type {Meta, StoryObj} from '@storybook/react';

import {StoryWrapper} from 'src/storybook-utils/story-components';
// TODO - file will be removed when legacy theme is deprecated
// eslint-disable-next-line @typescript-eslint/no-restricted-imports
import {useTheme} from 'styled-components';

export default {
  title: 'Tokens/_Deprecated/Elevation',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16533&t=LIcEGm0o9FzqNPdy-4',
    },
  },
} as Meta;

export const Elevation: StoryObj = {
  render: () => <ExampleWithTheme />,
};

function ExampleWithTheme() {
  const theme = useTheme();

  return (
    <div
      style={{
        display: 'flex',
        gap: '68px',
      }}
    >
      <StoryWrapper gap={16}>
        <div
          style={{
            width: '300px',
            height: '300px',
            backgroundColor: theme.color.surface.main,
            borderRadius: theme.borderRadius.medium,
            filter: theme.elevation.low,
          }}
        />
        <div>Low</div>
      </StoryWrapper>
      <StoryWrapper gap={16}>
        <div
          style={{
            width: '300px',
            height: '300px',
            backgroundColor: theme.color.surface.main,
            borderRadius: theme.borderRadius.medium,
            filter: theme.elevation.medium,
          }}
        />
        <div>Medium</div>
      </StoryWrapper>
      <StoryWrapper gap={16}>
        <div
          style={{
            width: '300px',
            height: '300px',
            backgroundColor: theme.color.surface.main,
            borderRadius: theme.borderRadius.medium,
            filter: theme.elevation.high,
          }}
        />
        <div>High</div>
      </StoryWrapper>
    </div>
  );
}
