import type {Meta, StoryObj} from '@storybook/react';
import {Box, Stack, useTheme} from 'src/index';
import {KEYED_SPACING} from 'src/tokens/constants';
import {getTypedKeys} from 'src/utils/object';

import {<PERSON>lickT<PERSON>Copy, DocBlock, DocCard} from 'src/storybook-utils/story-components';

export default {
  title: 'Tokens/KeyedSpacing',
} as Meta;

export const KeyedSpacing: StoryObj = {
  render: () => (
    <>
      <DocBlock>
        Keyed spacing can be referenced via the theme by{' '}
        <ClickToCopy>theme.keyedSpacing</ClickToCopy>
      </DocBlock>
      <Stack gap={4}>
        {getTypedKeys(KEYED_SPACING).map(spacingKey => (
          <KeyedSpacingCard key={spacingKey} spacingKey={spacingKey} />
        ))}
      </Stack>
    </>
  ),
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=8225-18300&mode=design&t=FYOLteJvPm3EF3zM-0',
    },
  },
};

interface KeyedSpacingCardProps {
  spacingKey: keyof typeof KEYED_SPACING;
}

const KeyedSpacingCard = ({spacingKey}: KeyedSpacingCardProps) => {
  const theme = useTheme();
  return (
    <Stack direction="row" gap={2}>
      <Box width={theme.keyedSpacing[spacingKey]} bgcolor="semanticPalette.stroke.main"></Box>
      <DocCard
        title={spacingKey}
        subtitle={`${theme.keyedSpacing[spacingKey]}`}
        clickToCopy={`theme.keyedSpacing.${spacingKey}`}
        margin={0}
      />
    </Stack>
  );
};
