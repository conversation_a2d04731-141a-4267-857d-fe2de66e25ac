import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {FC} from 'react';
import {Box, useTheme} from 'src/index';
import {
  CATEGORY_ELEMENT_KEYS,
  CATEGORY_KEYS,
  HEATMAP_CATEGORY_KEYS,
  HEATMAP_NEG_TO_POS_KEYS,
  HEATMAP_NEG_TO_POS_VALUE_KEYS,
  PLACEHOLDER_COLOR,
  SEMANTIC_ELEMENT_KEYS,
  SEMANTIC_STATE_KEYS,
  THEME_KEYS,
  TONE_KEYS,
} from 'src/tokens/constants';
// direct import of tokens only allowed for ColorPrimitives story
// eslint-disable-next-line @typescript-eslint/no-restricted-imports
import {getColorPrimitives} from 'src/tokens/muiTheme';
import type {PaletteKeys} from 'src/tokens/themeTypes';
import type {Theme} from '@mui/material';

import {ClickToCopy, <PERSON>Block, ExternalLink} from 'src/storybook-utils/story-components';

export default {
  title: 'Tokens/Palette',
} as Meta;

export const MUIPalette: StoryObj = {
  render: () => <MUIPaletteStory />,
};

const MUIPaletteStory = () => {
  const theme = useTheme();
  const DocFragment = (
    <>
      These palette options can be referenced via
      <ClickToCopy>{`theme.palette.`}</ClickToCopy>
      <code>&lt;&lt;reference here&gt;&gt;</code>
    </>
  );
  const subDocFragment = (
    <>
      Note, that some MUI props are theme-aware. For example, the <code>sx</code> prop includes{' '}
      <code>color</code> and <code>bgcolor</code> which are aware of <code>theme.palette</code>. In
      this case, you would specify <code>color='semanticPalette.text.main'</code>
      <br />
      <ExternalLink href="https://mui.com/system/getting-started/the-sx-prop/#palette">
        Read More
      </ExternalLink>
    </>
  );
  const muiPaletteKeys = [
    [['common'], ['black', 'white']],
    [
      ['primary', 'secondary', 'info', 'success', 'warning', 'error'],
      ['main', 'light', 'dark', 'contrastText'],
    ],
    [['grey'], ['100', '200', '300', '400', '600', '800', '900', 'A100', 'A200', 'A400', 'A700']],
    [['text'], ['primary', 'secondary', 'disabled', 'placeholder']],
    [['background'], ['paper', 'default', 'shadow']],
  ];

  const colorNameTemplate: ColorTableProps['colorNameTemplate'] = (columnHeading, rowHeading) =>
    `${columnHeading}.${rowHeading}`;

  return (
    <>
      <DocBlock subtext={subDocFragment}>{DocFragment}</DocBlock>
      {muiPaletteKeys.map(([columnHeadings, rowHeadings], index) => {
        const palette = columnHeadings.reduce(
          (acc, paletteKey) => ({
            ...acc,
            [paletteKey]: {...theme.palette}[paletteKey],
          }),
          {}
        );

        return (
          <Box mb={8} key={`palette-${index}`}>
            <ColorTable {...{columnHeadings, rowHeadings, palette, colorNameTemplate}} />
          </Box>
        );
      })}
    </>
  );
};

export const SemanticPalette: StoryObj = {
  render: () => <PaletteStory paletteKey="semanticPalette" />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1777-17350&t=IjpqVv1rVyaOy7co-0',
    },
  },
};

export const CategoryPalette: StoryObj = {
  render: () => <PaletteStory paletteKey="categoryPalette" />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=2164-17758&t=IjpqVv1rVyaOy7co-0',
    },
  },
};

export const HeatMapCategoryPalette: StoryObj = {
  render: () => <PaletteStory paletteKey="heatMapCategoryPalette" />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=13710-14009&m=dev',
    },
  },
};

export const HeatMapNegToPosPalette: StoryObj = {
  render: () => <PaletteStory paletteKey="heatMapNegToPosPalette" />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=13710-14009&m=dev',
    },
  },
};

export const ColorPrimitives: StoryObj = {
  render: (_, {globals: {muiThemeKey}}) => <PaletteStory muiThemeKey={muiThemeKey} />,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=3792-28768&t=aJMYRSxAO18Wpd4l-0',
    },
  },
};

interface PaletteStoryProps {
  paletteKey?: PaletteKeys | 'primitives';
  muiThemeKey?: THEME_KEYS;
}

const PaletteStory = ({
  paletteKey = 'primitives',
  muiThemeKey = THEME_KEYS.FLUROSENSE_LEGACY,
}: PaletteStoryProps) => {
  const theme = useTheme();

  let palette: ColorTableProps['palette'],
    columnHeadings: ColorTableProps['columnHeadings'],
    rowHeadings: ColorTableProps['rowHeadings'],
    colorNameTemplate: ColorTableProps['colorNameTemplate'],
    DocFragment: React.ReactNode,
    docCode: string;

  switch (paletteKey) {
    case 'semanticPalette':
      palette = SEMANTIC_STATE_KEYS.reduce((semanticPalette, state) => {
        const group = SEMANTIC_ELEMENT_KEYS.reduce((semanticElementColors, element) => {
          return {
            ...semanticElementColors,
            [element]: theme.palette[paletteKey][element][state],
          };
        }, {});
        return {...semanticPalette, [state]: group};
      }, {});
      columnHeadings = [...SEMANTIC_STATE_KEYS];
      rowHeadings = [...SEMANTIC_ELEMENT_KEYS];
      colorNameTemplate = (columnHeading, rowHeading) => `${rowHeading}.${columnHeading}`;
      DocFragment = (
        <>
          These palette options can be referenced via
          <ClickToCopy>{`theme.palette.${paletteKey}.`}</ClickToCopy>
          <code>&lt;&lt;reference here&gt;&gt;</code>
          <p>
            Some values may use a{' '}
            <span style={{backgroundColor: PLACEHOLDER_COLOR}}>PLACEHOLDER_COLOR</span>. This color
            should indicate a color key that should not be used yet.
          </p>
        </>
      );
      docCode = 'semanticPalette.surface.brand';
      break;
    case 'categoryPalette':
      palette = {...theme.palette[paletteKey]};
      columnHeadings = [...CATEGORY_KEYS];
      rowHeadings = [...CATEGORY_ELEMENT_KEYS];
      colorNameTemplate = (columnHeading, rowHeading) => `${columnHeading}.${rowHeading}`;
      DocFragment = (
        <>
          These palette options can be referenced via
          <ClickToCopy>{`theme.palette.${paletteKey}.`}</ClickToCopy>
          <code>&lt;&lt;reference here&gt;&gt;</code>
        </>
      );
      docCode = 'categoryPalette.1.surface';
      break;
    case 'heatMapCategoryPalette':
      palette = {...theme.palette.heatMapCategoryPalette};
      columnHeadings = [...HEATMAP_CATEGORY_KEYS];
      rowHeadings = [...HEATMAP_CATEGORY_KEYS];
      colorNameTemplate = (columnHeading, rowHeading) => `${columnHeading}.${rowHeading}`;
      DocFragment = (
        <>
          These palette options can be referenced via
          <ClickToCopy>{`theme.palette.${paletteKey}.`}</ClickToCopy>
          <code>&lt;&lt;reference here&gt;&gt;</code>
        </>
      );
      docCode = 'heatMapCategoryPalette.1.8';
      break;
    case 'heatMapNegToPosPalette':
      palette = {...theme.palette.heatMapNegToPosPalette};
      columnHeadings = [...HEATMAP_NEG_TO_POS_KEYS];
      rowHeadings = [...HEATMAP_NEG_TO_POS_VALUE_KEYS];
      colorNameTemplate = (columnHeading, rowHeading) => `${columnHeading}.${rowHeading}`;
      DocFragment = (
        <>
          These palette options can be referenced via
          <ClickToCopy>{`theme.palette.${paletteKey}.`}</ClickToCopy>
          <code>&lt;&lt;reference here&gt;&gt;</code>
        </>
      );
      docCode = 'heatMapNegToPosPalette["neg-to-pos-1"][-5]';
      break;
    case 'primitives':
    default:
      palette = {...getColorPrimitives(muiThemeKey)};
      columnHeadings = Object.keys(palette);
      rowHeadings = [...TONE_KEYS];
      colorNameTemplate = (columnHeading, rowHeading) => `${columnHeading}.${rowHeading}`;
      DocFragment = (
        <>
          These color primitives should not be referenced directly. Please use{' '}
          <ClickToCopy>theme.palette.semanticPalette</ClickToCopy>,
          <ClickToCopy>theme.palette.categoryPalette</ClickToCopy>, or{' '}
          <ClickToCopy>theme.palette.heatMapCategoryPalette</ClickToCopy>
          <ClickToCopy>theme.palette.heatMapNegToPosPalette</ClickToCopy>
        </>
      );
      docCode = '';
      break;
  }

  const subDocFragment = docCode ? (
    <>
      Note, that some MUI props are theme-aware. For example, <code>color</code> and{' '}
      <code>bgcolor</code> which are aware of <code>theme.palette</code>. In this case, you would
      specify <code>bgcolor='{docCode}'</code>
      <br />
      <ExternalLink href="https://mui.com/system/getting-started/the-sx-prop/#palette">
        Read More
      </ExternalLink>
    </>
  ) : undefined;

  return (
    <>
      {DocFragment && <DocBlock subtext={subDocFragment}>{DocFragment}</DocBlock>}
      <ColorTable {...{columnHeadings, rowHeadings, palette, colorNameTemplate}} />
    </>
  );
};

interface ColorTableProps {
  columnHeadings: string[];
  rowHeadings: string[];
  palette: Record<string, Record<string, string>>;
  colorNameTemplate: (columnHeading: string, rowHeading: string) => string;
}

const ColorTable: FC<ColorTableProps> = ({
  columnHeadings,
  rowHeadings,
  palette,
  colorNameTemplate,
}) => (
  <table>
    <thead>
      <tr>
        <th></th>
        {columnHeadings.map(columnHeading => (
          <th key={columnHeading} align="left">
            <Box pl={3}>{columnHeading}</Box>
          </th>
        ))}
      </tr>
    </thead>
    <tbody>
      {rowHeadings.map(rowHeading => (
        <tr key={rowHeading}>
          <td align="right" style={{height: '65px', width: '90px'}}>
            {rowHeading}
          </td>
          {columnHeadings.map(columnHeading => (
            <td key={`${rowHeading}-${columnHeading}`} style={{height: '65px'}}>
              {palette[columnHeading][rowHeading] && (
                <ColorCard
                  colorHex={palette[columnHeading][rowHeading]}
                  colorName={colorNameTemplate(columnHeading, rowHeading)}
                />
              )}
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  </table>
);

interface ColorCardProps {
  colorHex: string;
  colorName: string;
}

const ColorCard: FC<ColorCardProps> = ({colorHex, colorName}) => (
  <Box
    alignItems="center"
    display="flex"
    flexDirection="row"
    gap={1}
    justifyContent="flex-start"
    margin={2}
  >
    <Box>
      <Box
        bgcolor={colorHex}
        border={1}
        borderColor="semanticPalette.stroke.main"
        borderRadius={(theme: Theme) => theme.borderRadii.pill}
        boxSizing="border-box"
        height={50}
        width={50}
      />
    </Box>
    <Box>
      <ClickToCopy>{colorName}</ClickToCopy>
      <br />
      <code>{colorHex}</code>
    </Box>
  </Box>
);
