import {KEYED_SPACING, SPACING_CONSTANT, THEME_KEYS} from 'src/tokens/constants';
import {
  colorPrimitives as flurosenseColorPrimitives,
  muiPalette as flurosensePalette,
} from 'src/tokens/Flurosense/colors';
import {typography as flurosenseTypography} from 'src/tokens/Flurosense/typography';
import {
  colorPrimitives as regrowColorPrimitives,
  muiPalette as regrowPalette,
} from 'src/tokens/Regrow/colors';
import {typography as regrowTypography} from 'src/tokens/Regrow/typography';
import type {Shadows, ThemeOptions} from '@mui/material/styles';

import {componentThemeOverrides} from 'src/components/themeOverrides';

const COLOR_PRIMITIVES = {
  [THEME_KEYS.REGROW_DEFAULT]: regrowColorPrimitives,
  [THEME_KEYS.FLUROSENSE_LEGACY]: flurosenseColorPrimitives,
};

export const getColorPrimitives = (themeKey: THEME_KEYS) => COLOR_PRIMITIVES[themeKey];

export const getCssFontFile = (themeKey: THEME_KEYS) => REGROW_THEMES[themeKey].cssFontFile;

export const REGROW_THEMES = {
  [THEME_KEYS.REGROW_DEFAULT]: {
    cssFontFile: 'regrowFonts.css',
    palette: regrowPalette,
    typography: regrowTypography,
    name: THEME_KEYS.REGROW_DEFAULT,
  },
  [THEME_KEYS.FLUROSENSE_LEGACY]: {
    cssFontFile: 'flurosenseFonts.css',
    palette: flurosensePalette,
    typography: flurosenseTypography,
    name: THEME_KEYS.FLUROSENSE_LEGACY,
  },
};

export const getMUITheme = (themeKey: THEME_KEYS): ThemeOptions => ({
  palette: REGROW_THEMES[themeKey].palette,
  spacing: SPACING_CONSTANT,
  keyedSpacing: KEYED_SPACING,
  name: themeKey,
  shape: {
    borderRadius: 4,
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 720,
      md: 1024,
      lg: 1280,
      xl: 1920,
    },
  },
  fixedWidths: {
    xs: 280,
    sm: 400,
    md: 600,
    lg: 1000,
    xl: 1200,
  },
  shadows: [
    'none',
    `0px 0px 1px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}61`,
    `0px 0px 2px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}61`,
    `0px 0px 3px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}4D`,
    `0px 0px 4px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}4D`,
    `0px 0px 5px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 6px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 7px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 8px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 9px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 10px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 11px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 12px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 13px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 14px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 15px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 16px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 17px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 18px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 19px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 20px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 21px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 22px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 23px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
    `0px 0px 24px 0px ${REGROW_THEMES[themeKey].palette.background.shadow}33`,
  ] as Shadows,
  borderRadii: {
    sm: 1,
    md: 2,
    pill: 9999,
  },
  boxShadows: {
    sm: 2,
    md: 4,
    lg: 8,
  },
  typography: REGROW_THEMES[themeKey].typography,
  components: componentThemeOverrides,
});
