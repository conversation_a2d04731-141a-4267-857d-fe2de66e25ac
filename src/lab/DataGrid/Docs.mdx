import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as DataGridStories from './index.stories'

<Meta of={DataGridStories}/>

## Overview
The DataGrid presents information in a structured format of rows and columns. The data is displayed in a user-friendly, quick-to-scan and interactive way, enabling users to efficiently identify patterns, edit data, and gather insights.

You can learn more here: [https://mui.com/x/react-data-grid/](https://mui.com/x/react-data-grid/)

<Canvas of={DataGridStories.Basic} />
<Controls of={DataGridStories.Basic}/>

## Column Grouping
<Canvas of={DataGridStories.ColumnGrouping} />

## Elevated 
To create a box-shadow around the table, wrap it into the [Paper component.](/?path=/story/components-paper--docs)
<Canvas  of={DataGridStories.Elevated} />
