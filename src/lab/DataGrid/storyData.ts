import type {GridColDef} from '@mui/x-data-grid-pro';

export const columns: GridColDef[] = [
  {field: 'id', headerName: 'ID', minWidth: 90, flex: 1, resizable: false},
  {
    field: 'firstName',
    headerName: 'First name',
    minWidth: 150,
    flex: 1,
  },
  {
    field: 'lastName',
    headerName: 'Last name',
    minWidth: 150,
    flex: 1,
  },
  {
    field: 'age',
    headerName: 'Age',
    type: 'number',
    minWidth: 150,
    flex: 1,
  },
];

export const rows = [
  {id: 1, lastName: 'Snow', firstName: 'Jon', age: 35},
  {id: 2, lastName: 'Lannister', firstName: '<PERSON><PERSON><PERSON>', age: 42},
  {id: 3, lastName: 'Lannister', firstName: '<PERSON>', age: 45},
  {id: 4, lastName: 'Stark', firstName: 'Arya', age: 16},
  {id: 5, lastName: 'Targa<PERSON><PERSON>', firstName: 'Da<PERSON><PERSON>', age: null},
  {id: 6, lastName: 'Melisandre', firstName: null, age: 150},
  {id: 7, lastName: '<PERSON>', firstName: '<PERSON><PERSON><PERSON>', age: 44},
  {id: 8, lastName: '<PERSON>', firstName: '<PERSON><PERSON>', age: 36},
  {id: 9, lastName: 'Roxie', firstName: '<PERSON>', age: 65},
];
