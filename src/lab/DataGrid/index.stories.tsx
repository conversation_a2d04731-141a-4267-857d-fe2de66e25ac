import type {Meta, StoryObj} from '@storybook/react';
import {useTheme} from '@mui/material';

import {Box} from 'src/components/Box';
import {Paper} from 'src/components/Paper';
import {DocBlock} from 'src/storybook-utils/story-components';

import {DataGrid} from '.';
import type {GridColumnGroupingModel} from '.';
import {columns, rows} from './storyData';

const meta: Meta<typeof DataGrid> = {
  component: DataGrid,
  title: 'components/Data Display/DataGrid',
};

export default meta;
type Story = StoryObj<typeof DataGrid>;

export const Basic: Story = {
  args: {columns, rows},
  render: args => <DataGrid {...args} />,
};

export const ColumnGrouping: Story = {
  render: () =>
    (() => {
      const theme = useTheme();
      const columnGroupingModel: GridColumnGroupingModel = [
        {
          groupId: 'human',
          headerName: 'Human',

          children: [
            {
              groupId: 'Internal',
              description: '',
              headerName: 'Internal',
              headerClassName: 'red-head',
              children: [
                {
                  groupId: 'spacer-1',

                  headerName: '',
                  headerClassName: 'red-head',
                  children: [{field: 'id'}],
                },
              ],
            },

            {
              groupId: 'Basic info',
              headerClassName: 'green-head',
              children: [
                {
                  groupId: 'Full name',
                  children: [{field: 'lastName'}, {field: 'firstName'}],
                },
                {field: 'age'},
              ],
            },
          ],
        },
      ];

      return (
        <Box
          sx={{
            width: '100%',
            '& .red-head': {
              color: theme.palette.semanticPalette.textInverted.main,
              backgroundColor: theme.palette.categoryPalette[6].chart,
            },
            '& .green-head': {
              color: theme.palette.semanticPalette.textInverted.main,
              backgroundColor: theme.palette.categoryPalette[1].chart,
            },
          }}
        >
          <DataGrid columns={columns} rows={rows} columnGroupingModel={columnGroupingModel} />
        </Box>
      );
    })(),
};

export const Elevated: Story = {
  args: {columns, rows},
  render: args => (
    <Paper elevation={2}>
      <DataGrid {...args} />
    </Paper>
  ),
};

export const WithPagination: Story = {
  args: {columns, rows},
  render: args => (
    <>
      <DocBlock
        subtext={
          <>
            {
              'You can enable the default pagination slot for client-side pagination by setting the `pagination` prop to `true`, and the `hideFooterPagination` and `hideFooter` props to `false`. You can use the `slots: {pagination: MyOverriddenPagination}` to replace the default pagination component. Use `slotProps: {pagination: {...myPaginationProps}` to override pagination props. Note that only some will take effect in the default slot. See code. '
            }
            <pre>{`
            <DataGrid
              {...args}
              pagination
              hideFooterPagination={false}
              hideFooter={false}
              hideFooterRowCount={false}
              pageSizeOptions={[5, 10, 15]}
              slotProps={{
                pagination: {
                  showFirstButton: true,
                  showLastButton: true,
                  showPages: true,
                  showDisplayedRowsLabel: true,
                },
              }}
              initialState={{pagination: {paginationModel: {pageSize: 5}}}}
            />`}</pre>
          </>
        }
      >
        <DataGrid
          {...args}
          pagination
          hideFooterPagination={false}
          hideFooter={false}
          hideFooterRowCount={false}
          pageSizeOptions={[5, 10, 15]}
          slotProps={{
            pagination: {
              showFirstButton: true,
              showLastButton: true,
              showPages: true,
              showDisplayedRowsLabel: true,
            },
          }}
          initialState={{pagination: {paginationModel: {pageSize: 5}}}}
        />
      </DocBlock>
    </>
  ),
};
