import {useMemo} from 'react';
import {isDefined} from 'src/utils/typeGuards';
import {Box, type Components, type Theme} from '@mui/material';
import {
  gridClasses,
  gridPageSelector,
  gridPageSizeSelector,
  gridRowCountSelector,
  DataGridPro as MUIDataGrid,
  GridColumnMenu as MUIGridColumnMenu,
  useGridApiContext,
  useGridRootProps,
  useGridSelector,
} from '@mui/x-data-grid-pro';
import type {GridColumnMenuProps, DataGridProProps as MUIDataGridProps} from '@mui/x-data-grid-pro';

import {Pagination} from 'src/components/Pagination';

const DefaultGridColumnMenu = (props: GridColumnMenuProps) => (
  <MUIGridColumnMenu
    {...props}
    slots={{
      columnMenuColumnsItem: null,
      ...props.slots,
    }}
  />
);

export const DataGridEnabledPagination = () => {
  const rootProps = useGridRootProps();
  const tableProps = rootProps.slotProps?.pagination;

  const apiRef = useGridApiContext();
  const rowCount = useGridSelector(apiRef, gridRowCountSelector);
  const page = useGridSelector(apiRef, gridPageSelector);
  const pageSize = useGridSelector(apiRef, gridPageSizeSelector);

  const pageSizeOptions = useMemo(
    () =>
      (tableProps?.rowsPerPageOptions ?? rootProps.pageSizeOptions)?.map(value =>
        typeof value === 'number' ? {label: String(value), value} : value
      ),
    [rootProps.pageSizeOptions, tableProps?.rowsPerPageOptions]
  );

  return (
    <Box paddingRight={3} {...(tableProps?.containerStylesOverride ?? {})}>
      <Pagination
        size={tableProps?.size}
        page={page}
        rowsPerPage={pageSize}
        onSetPage={newPage => apiRef.current.setPage(newPage)}
        onSetRowsPerPage={rowsPerPage => apiRef.current.setPageSize(rowsPerPage)}
        count={rowCount}
        showFirstButton={tableProps?.showFirstButton}
        showLastButton={tableProps?.showLastButton}
        showPages={tableProps?.showPages}
        labelRowsPerPage={tableProps?.labelRowsPerPage}
        showDisplayedRowsLabel={
          (isDefined(tableProps?.labelRowsPerPage) &&
            tableProps?.showDisplayedRowsLabel !== false) ||
          tableProps?.showDisplayedRowsLabel === true
        }
        rowsPerPageOptions={pageSizeOptions}
      />
    </Box>
  );
};

export const DataGridOverrides: Components<Theme>['MuiDataGrid'] = {
  defaultProps: {
    slots: {
      columnMenu: DefaultGridColumnMenu,
      pagination: DataGridEnabledPagination,
    },
    disableColumnMenu: true,
    hideFooter: true,
    hideFooterPagination: true,
    rowSpacingType: 'border',
    pageSizeOptions: undefined,
  },
  styleOverrides: {
    root: ({theme}) => ({
      backgroundColor: theme.palette.semanticPalette.surface.main,
      boxShadow: 'none', // use Paper component for elevation
      border: 'none',
      fontSize: theme.typography.body1.fontSize,
      borderRadius: theme.spacing(theme.borderRadii.md),
    }),
    footerContainer: ({theme}) => ({
      borderBottom: `1px solid ${theme.palette.semanticPalette.stroke.main}`,
      borderTop: 'none',
    }),
    withBorderColor: ({theme}) => ({
      borderColor: theme.palette.semanticPalette.stroke.secondary,
    }),
    columnHeaderTitleContainer: ({theme}) => ({
      borderBottom: 'none !important',
      // eslint-disable-next-line no-restricted-syntax
      fontWeight: theme.typography.fontWeightBold,

      [`& .${gridClasses.columnHeaderTitleContainerContent}`]: {
        // Justify the Column Header checkbox to the center when checkboxSelection is true.
        // Without affecting the other column headers.
        justifyContent: 'inherit',
      },
    }),
    columnHeaders: ({theme}) => ({
      borderBottom: `2px solid ${theme.palette.semanticPalette.stroke.secondary}`,
    }),
    pinnedColumnHeaders: () => ({
      // Found this boxShadow value already used in the MUI DataGrid source code.
      boxShadow: '2px 0px 4px -2px rgba(27,27,27,0.21)',
    }),
    columnHeader: ({theme}) => ({
      '&:focus, &:focus-within': {
        outline: 'none',
      },
      padding: theme.spacing(0, 3),
    }),
    columnHeaderDraggableContainer: {
      '&:focus, &:focus-within': {
        outline: 'none',
      },
    },
    cell: ({theme}) => ({
      '&:focus-within': {
        outline: `2px solid ${theme.palette.primary}`,
      },
      padding: theme.spacing(0, 3),
    }),
    row: ({theme}) => ({
      '&:hover': {
        backgroundColor: theme.palette.grey[200],
      },
    }),
  },
};

export * from '@mui/x-data-grid-pro';

export const DataGrid: typeof MUIDataGrid = (props: MUIDataGridProps) => <MUIDataGrid {...props} />;
