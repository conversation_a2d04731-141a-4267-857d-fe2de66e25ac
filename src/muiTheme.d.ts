import type {} from '@mui/material/styles';
import type {} from '@mui/material/Typography';
import type {} from '@mui/material/Button';
import type {} from '@mui/material/Button/ButtonGroup';
import type {} from '@mui/material/IconButton';
import type {} from '@mui/material/ToggleButton/ToggleButton';
import type {} from '@mui/material/ToggleButtonGroup';
import type {} from '@mui/material/Checkbox';
import type {} from '@mui/material/Chip';
import type {} from '@mui/material/TextField';
import type {} from '@mui/material/InputBase';
import type {} from '@mui/x-data-grid-pro/themeAugmentation';
import type {} from '@mui/material/SvgIcon';
import type {} from '@mui/x-data-grid-pro';

import type {THEME_KEYS} from 'src/tokens/constants';

import type {BoxProps} from './components/Box';
import type {PaginationProps as LeafPaginationProps} from './components/Pagination';

import type {
  BorderRadii,
  BoxShadows,
  CategoryPalette,
  ExtendedBackgroundPalette,
  ExtendTypeText,
  FixedWidths,
  GreyPalette,
  HeatMapCategoryPalette,
  HeatMapNegToPosPalette,
  KeyedSpacing,
  SemanticPalette,
} from './tokens/themeTypes';
import type {DeepPartial} from './types';

declare module '@mui/material/styles' {
  interface Palette {
    background: ExtendedBackgroundPalette;
    grey: GreyPalette;
    semanticPalette: SemanticPalette;
    categoryPalette: CategoryPalette;
    heatMapNegToPosPalette: HeatMapNegToPosPalette;
    heatMapCategoryPalette: HeatMapCategoryPalette;
    text: ExtendTypeText;
  }

  interface PaletteOptions {
    background?: Partial<ExtendedBackgroundPalette>;
    grey?: Partial<GreyPalette>;
    semanticPalette?: DeepPartial<SemanticPalette>;
    categoryPalette?: DeepPartial<CategoryPalette>;
    heatMapNegToPosPalette?: DeepPartial<HeatMapNegToPosPalette>;
    heatMapCategoryPalette?: DeepPartial<HeatMapCategoryPalette>;
    text: Partial<ExtendTypeText>;
  }

  interface Theme {
    name: THEME_KEYS;
    borderRadii: BorderRadii;
    boxShadows: BoxShadows;
    fixedWidths: FixedWidths;
    keyedSpacing: KeyedSpacing;
  }

  interface ThemeOptions {
    name?: THEME_KEYS;
    borderRadii?: Partial<BorderRadii>;
    boxShadows?: Partial<BoxShadows>;
    fixedWidths?: Partial<FixedWidths>;
    keyedSpacing?: KeyedSpacing;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    button: false;
    caption: false;
    overline: false;
    subtitle1: false;
    subtitle2: false;
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsSizeOverrides {
    small: true;
    medium: true;
    large: false;
  }

  interface ButtonPropsColorOverrides {
    primary: true;
    secondary: true;
    error: true;
    info: false;
    warning: false;
    success: false;
    inherit: false;
  }
}

declare module '@mui/material/IconButton' {
  interface IconButtonPropsSizeOverrides {
    small: true;
    medium: true;
    large: false;
  }

  interface IconButtonPropsColorOverrides {
    primary: true;
    secondary: true;
    error: true;
    info: false;
    warning: false;
    success: true;
    inherit: false;
    default: false;
  }
}

declare module '@mui/material/ButtonGroup' {
  interface ButtonGroupSizePropsOverrides {
    small: true;
    medium: true;
    large: false;
  }

  interface ButtonGroupPropsVariantOverrides {
    outlined: true;
    text: false;
    contained: false;
  }

  interface ButtonGroupPropsColorOverrides {
    secondary: true;
    primary: false;
    error: false;
    success: false;
    info: false;
    inherit: false;
    warning: false;
  }
}

declare module '@mui/material/ToggleButton' {
  interface ToggleButtonPropsSizeOverrides {
    small: true;
    medium: true;
    large: false;
  }

  interface ToggleButtonPropsColorOverrides {
    standard: true;
    primary: true;
    secondary: false;
    error: false;
    success: false;
    info: false;
    warning: false;
  }
}

declare module '@mui/material/ToggleButtonGroup' {
  interface ToggleButtonGroupSizePropsOverrides {
    small: true;
    medium: true;
    large: false;
  }

  interface ToggleButtonGroupPropsColorOverrides {
    standard: true;
    primary: true;
    secondary: false;
    error: false;
    success: false;
    info: false;
    warning: false;
  }
}

declare module '@mui/material/Checkbox' {
  interface CheckboxPropsColorOverrides {
    default: true;
    primary: true;
    error: true;
    secondary: false;
    success: true;
    info: true;
    warning: false;
  }
}

declare module '@mui/material/Chip' {
  interface ChipPropsSizeOverrides {
    small: true;
    medium: true;
    large: false;
  }
  interface ChipPropsColorOverrides {
    secondary: true;
    primary: true;
    error: true;
    success: true;
    default: true;
    info: true;
    inherit: true;
    warning: true;
  }

  interface ChipPropsVariantOverrides {
    outlined: true;
    default: false;
    filled: true;
  }
}

declare module '@mui/material/InputBase' {
  interface InputBasePropsColorOverrides {
    primary: false;
    secondary: false;
    info: false;
    success: false;
  }

  interface InputBasePropsSizeOverrides {
    small: false;
    medium: true;
  }
}

declare module '@mui/material/TextField' {
  interface TextFieldPropsColorOverrides {
    primary: false;
    secondary: false;
    info: false;
    success: false;
  }

  interface TextFieldPropsSizeOverrides {
    small: false;
    medium: true;
  }
}

declare module '@mui/material/Switch' {
  interface SwitchPropsColorOverrides {
    default: true;
    primary: false;
    secondary: false;
    error: false;
    info: false;
    success: false;
    warning: false;
  }

  interface SwitchPropsSizeOverrides {
    small: false;
    medium: true;
  }
}

declare module '@mui/material/SvgIcon' {
  interface SvgIconPropsSizeOverrides {
    h1: true;
    h2: true;
    h3: true;
    h4: true;
    h5: true;
    h6: true;
    body1: true;
    body2: true;
    small: true;
    medium: true;
    large: true;
    inherit: true;
  }

  interface SvgIconPropsColorOverrides {
    primary: true;
    secondary: true;
    disabled: true;
    error: true;
    main: true;
    success: false;
    warning: false;
    info: false;
    action: false;
  }
}

declare module '@mui/x-data-grid-pro' {
  interface PaginationPropsOverrides {
    showPages?: LeafPaginationProps['showPages'];
    showDisplayedRowsLabel?: LeafPaginationProps['showDisplayedRowsLabel'];
    containerStylesOverride?: BoxProps;
  }
}
