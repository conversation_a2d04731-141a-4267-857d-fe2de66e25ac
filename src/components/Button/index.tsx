import {LoadingButton, type LoadingButtonProps} from '@mui/lab';
import type {ButtonProps, Components, IconButtonProps, Theme} from '@mui/material';
import {Button, buttonClasses, darken, IconButton, iconButtonClasses, lighten} from '@mui/material';

const isSupportedThemeColor = (color: unknown, theme: Theme): color is keyof Theme['palette'] =>
  theme.palette[color as keyof Theme['palette']] !== undefined;

type ButtonColors =
  | 'secondary'
  | 'error'
  | 'success'
  | 'warning'
  | 'info'
  | 'inherit'
  | 'primary'
  | 'default';

const getThemeColor = (color: ButtonColors, theme: Theme) =>
  isSupportedThemeColor(color, theme) ? theme.palette[color] : theme.palette.primary;

const defaultOverrides: Components<Theme>['MuiButton'] = {
  defaultProps: {
    variant: 'contained',
    disableRipple: true,
    disableFocusRipple: true,
    disableTouchRipple: true,
    disableElevation: true,
  },
  styleOverrides: {
    root: ({theme}) => ({
      [`&.${buttonClasses.disabled}`]: {
        color: theme.palette.semanticPalette.textInverted.secondary,
      },
    }),
    sizeSmall: ({theme}) => ({
      [`&.${buttonClasses.sizeSmall}`]: {
        padding: theme.spacing(1, 2),
        fontSize: `${theme.typography.body2.fontSize}px`,
        lineHeight: theme.spacing(4),
        '&:has(> svg)': {
          padding: '4px 8px',
        },
        '& svg': {
          minHeight: theme.spacing(4),
          height: theme.spacing(4),
        },
      },
    }),
    contained: ({ownerState: {color = 'primary'}, theme}) => ({
      '&:active, &:hover': {backgroundColor: darken(getThemeColor(color, theme).dark, 0.08)},
      [`&.${buttonClasses.disabled}`]: {
        backgroundColor: theme.palette.semanticPalette.stroke.secondary,
      },
    }),
    containedSecondary: ({theme}) => ({
      backgroundColor: theme.palette.semanticPalette.highlight.secondary,
      color: theme.palette.semanticPalette.text.main,
      '&:active, &:hover': {
        backgroundColor: darken(theme.palette.semanticPalette.highlight.secondary, 0.3),
      },
    }),
    outlined: ({ownerState: {color = 'primary'}, theme}) => ({
      borderColor: getThemeColor(color, theme).main,
      backgroundColor: theme.palette.semanticPalette.surface.main,
      [`&.${buttonClasses.outlined}`]: {
        // -1px to accomodate for border
        padding: '7px 11px',
        '&:has(> svg)': {
          padding: '7px',
        },
      },
      '&:active, &:hover': {backgroundColor: lighten(getThemeColor(color, theme).light, 0.95)},
      [`&.${buttonClasses.disabled}`]: {
        borderColor: theme.palette.semanticPalette.stroke.secondary,
      },
    }),
    outlinedSecondary: ({theme}) => ({
      borderColor: theme.palette.semanticPalette.stroke.main,
      color: theme.palette.semanticPalette.text.main,
      '&:active, &:hover': {
        backgroundColor: theme.palette.semanticPalette.surface.secondary,
        borderColor: theme.palette.semanticPalette.stroke.main,
      },
    }),
    outlinedSizeSmall: () => ({
      [`&.${buttonClasses.outlinedSizeSmall}`]: {
        // -1px to accomodate for border
        padding: '3px 7px',
        '&:has(> svg)': {
          padding: '3px 7px',
        },
      },
    }),
    text: ({ownerState: {color = 'primary'}, theme}) => ({
      color: getThemeColor(color, theme).main,
      '&:active, &:hover': {backgroundColor: lighten(getThemeColor(color, theme).light, 0.95)},
    }),
    textSecondary: ({theme}) => ({
      color: theme.palette.semanticPalette.text.main,
    }),
  },
};

// Note: This component has augmented props. See src/muiTheme.d.ts for prop overrides
export const ButtonOverrides = defaultOverrides;

export const LoadingButtonOverrides = defaultOverrides;

export const IconButtonOverrides: Components<Theme>['MuiIconButton'] = {
  defaultProps: {
    color: 'secondary',
  },
  styleOverrides: {
    root: ({theme}) => ({
      [`&&.${iconButtonClasses.sizeSmall}`]: {
        minWidth: 'unset',
        padding: theme.spacing(0.5),
      },
      [`&&.${iconButtonClasses.sizeMedium}`]: {
        minWidth: 'unset',
        padding: theme.spacing(2),
      },
    }),
    colorPrimary: ({theme}) => ({
      ':hover': {
        backgroundColor: theme.palette.grey[200],
      },
      color: theme.palette.semanticPalette.text.main,
    }),
  },
};

export {Button, LoadingButton, IconButton};

export type {ButtonProps, IconButtonProps, LoadingButtonProps};

export function StoryComponent<C extends React.ElementType>(props: ButtonProps<C>) {
  return <Button {...props} />;
}
StoryComponent.displayName = 'Button';
