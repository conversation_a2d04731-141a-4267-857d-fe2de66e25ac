import { <PERSON>a, <PERSON>vas, <PERSON>s, Story } from "@storybook/blocks";
import * as ButtonStories from './index.stories.tsx'

<Meta of={ButtonStories} />

 ## Overview
MUI Button extends MUI ButtonBase. Please review the MUI docs linked below to see the full props list.

[<PERSON><PERSON> Button](https://mui.com/material-ui/react-button/) <br />
[MUI ButtonBase](https://mui.com/material-ui/api/button-base/) <br />
[MUI LoadingButton](https://mui.com/material-ui/react-button/#loading-button)

<Canvas of={ButtonStories.Basic} />
<Controls of={ButtonStories.Basic}/>

## Variants
<Canvas of={ButtonStories.Variants} />

## Colors
<Canvas of={ButtonStories.Colors} />

## Sizes
<Canvas of={ButtonStories.Sizes} />

## Disabled
<Canvas of={ButtonStories.Disabled} />

## Button with Loader
<Canvas of={ButtonStories.ButtonWithLoader} />

## Button with Tooltip
<Canvas of={ButtonStories.ButtonWithTooltip} />

## Button with Link
<Canvas of={ButtonStories.ButtonWithLink} />

## Button with Icons
<Canvas of={ButtonStories.ButtonWithIcons} />

## Icon Button
Icon buttons are commonly found in app bars and toolbars.

Icons are also appropriate for toggle buttons that allow a single choice to be selected or deselected, such as adding or removing a star to an item.

You must use the `IconButton` component

<Canvas of={ButtonStories.IconButtonBasic} />

