import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {<PERSON>, MemoryRouter} from 'react-router-dom';
import {
  Box,
  Button,
  IconButton,
  LoadingButton,
  Stack,
  SvgIcon,
  Tooltip,
  Typography,
} from 'src/index';
import {controlsExclude} from 'src/storybook-utils/argTypes';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';

const BUTTON_VARIANTS = ['contained', 'outlined', 'text'] as const;
const BUTTON_SIZES = ['medium', 'small'] as const;
const BUTTON_COLORS = ['primary', 'secondary', 'error'] as const;

const argTypeCategories = categorizeArgTypes(['children', 'classes', 'sx'], 'System');

const meta: Meta<typeof Button> = {
  component: Button,
  title: 'components/Inputs/Button',
  argTypes: argTypeCategories,
  parameters: {
    controls: {
      exclude: [...controlsExclude, 'disableElevation'],
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1898-17506',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Basic: Story = {
  render: args => <Button {...args}>Button</Button>,
};

export const Variants: Story = {
  render: () => (
    <>
      <Box mb={8}>
        <Typography variant="body1">
          Only primary contained (default), secondary outlined, error outlined, secondary text, and
          error text should be used.
        </Typography>
      </Box>
      {BUTTON_COLORS.map(color => {
        return (
          <Box display="grid" gap={1} gridTemplateColumns="repeat(4, 1fr)" width="260px" mb={4}>
            <Box width="100px">{color}</Box>
            {BUTTON_VARIANTS.map(variant => (
              <Box key={variant} mr={4} display="inline">
                <Button color={color} variant={variant}>
                  {variant}
                </Button>
              </Box>
            ))}
          </Box>
        );
      })}
      <Box display="grid" gap={1} gridTemplateColumns="repeat(4, 1fr)" width="260px" mb={4}>
        <Box width="100px">disabled</Box>
        {BUTTON_VARIANTS.map(variant => (
          <Box key={variant} mr={4} display="inline">
            <Button disabled variant={variant}>
              {variant}
            </Button>
          </Box>
        ))}
      </Box>
    </>
  ),
};

export const Colors: Story = {
  render: () => (
    <>
      <Box mb={4}>
        <Box mb={2}>
          <Typography variant="h3">Contained (default) Colors</Typography>
        </Box>
        <Box mb={4}>
          <Typography>{`(Only Primary state should be used)`}</Typography>
        </Box>
        {BUTTON_COLORS.map(color => (
          <Box key={color} mr={4} display="inline">
            <Button color={color}>{color}</Button>
          </Box>
        ))}
      </Box>
      <Box display="block" mb={4}>
        <Box mb={2}>
          <Typography variant="h3">Outlined Colors</Typography>
        </Box>
        <Box mb={4}>
          <Typography>{`(Only Secondary and Error states should be used)`}</Typography>
        </Box>
        {BUTTON_COLORS.map(color => (
          <Box key={color} mr={4} display="inline">
            <Button color={color} variant="outlined">
              {color}
            </Button>
          </Box>
        ))}
      </Box>
      <Box>
        <Box mb={2}>
          <Typography variant="h3">Text Colors</Typography>
        </Box>
        <Box mb={4}>
          <Typography>{`(Only Secondary and Error states should be used)`}</Typography>
        </Box>
        {BUTTON_COLORS.map(color => (
          <Box key={color} mr={4} display="inline">
            <Button color={color} variant="text">
              {color}
            </Button>
          </Box>
        ))}
      </Box>
    </>
  ),
};

export const Sizes: Story = {
  render: () => (
    <>
      {BUTTON_SIZES.map(size => (
        <Box key={size} mb={4} display="flex" gap={2} alignItems="center">
          <Box>
            <Button size={size}>{size}</Button>
          </Box>
          <Box>
            <Button size={size} variant="outlined">
              {size}
            </Button>
          </Box>
          <Box>
            <IconButton size={size} color="primary">
              <SvgIcon type="plus" />
            </IconButton>
          </Box>
          {size === 'medium' && (
            <Box>
              <Typography variant="body2" color="secondary">
                (default)
              </Typography>
            </Box>
          )}
        </Box>
      ))}
    </>
  ),
};

export const Disabled: Story = {
  render: () => (
    <>
      {BUTTON_VARIANTS.map(variant => (
        <Box key={variant} mr={4} display="inline">
          <Button variant={variant} disabled>
            {variant}
          </Button>
        </Box>
      ))}
    </>
  ),
};

export const ButtonWithLoader: Story = {
  render: () => (
    <>
      <Box mb={4}>
        <Typography variant="body2" color="secondary">
          Note, this is a separate <code>@mui/labs</code> component which does not inherit from{' '}
          <code>Button</code>
        </Typography>
      </Box>
      <Stack direction="column" gap={4}>
        <Typography variant="body2" color="secondary">
          <code>color=primary loading=false</code>
        </Typography>
        <Stack direction="row" gap={4}>
          {BUTTON_VARIANTS.map(variant => (
            <LoadingButton key={`medium-${variant}`} variant={variant} color="primary">
              {variant}
            </LoadingButton>
          ))}
        </Stack>
        <Typography variant="body2" color="secondary">
          <code>color=primary loading=true</code>
        </Typography>
        <Stack direction="row" gap={4}>
          {BUTTON_VARIANTS.map(variant => (
            <LoadingButton key={`medium-${variant}`} variant={variant} color="primary" loading>
              {variant}
            </LoadingButton>
          ))}
        </Stack>
        <Typography variant="body2" color="secondary">
          <code>color=primary size="small" loading=true</code>
        </Typography>
        <Stack direction="row" gap={4}>
          {BUTTON_VARIANTS.map(variant => (
            <LoadingButton
              key={`small-${variant}`}
              variant={variant}
              color="primary"
              size="small"
              loading
            >
              {variant}
            </LoadingButton>
          ))}
        </Stack>
      </Stack>
    </>
  ),
};

export const ButtonWithTooltip: Story = {
  render: () => (
    <Box mt={8}>
      <Tooltip id="sample-button-tooltip" title="tooltip" placement="top">
        <Button variant="contained">Button</Button>
      </Tooltip>
      <Box mt={8} mb={2}>
        <Typography variant="body2" color="secondary">
          Note, `disabled` buttons must be wrapped in a MUI component or html element for tooltips
          to be operable.
        </Typography>
      </Box>
      <Tooltip id="sample-button-tooltip-disabled" title="disabled" placement="bottom">
        <Box display="inline-block">
          <Button variant="contained" disabled>
            Disabled
          </Button>
        </Box>
      </Tooltip>
    </Box>
  ),
};

export const ButtonWithLink: Story = {
  decorators: [
    Story => (
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
  render: () => (
    <>
      <Box display="flex" flexDirection="column" alignItems="flex-start" gap={4}>
        <Typography variant="body2" color="secondary">
          When an <code>href</code> is included as a prop the Button component renders an `a` tag
        </Typography>
        <Button href="https://www.regrow.ag">HTMLAnchor</Button>
      </Box>
      <Box mt={8} display="flex" flexDirection="column" alignItems="flex-start" gap={4}>
        <Typography variant="body2" color="secondary">
          The <code>component</code> prop can be used to pass a react router <code>Link</code>{' '}
          component.
          <br />
          This can be then used with the `to` prop as you would a react router Link. <br />
        </Typography>
        <Typography variant="body2" color="secondary">
          Note, the component you pass in needs to be able to hold a `ref`.
        </Typography>
        <Button component={Link} to="/sample/link/here">
          RouterLink
        </Button>
      </Box>
    </>
  ),
};

export const ButtonWithIcons: Story = {
  render: () => (
    <>
      <Box display="inline" mr={4}>
        <Button color="primary" startIcon={<SvgIcon type="plus" />}>
          Icon Start
        </Button>
      </Box>
      <Box display="inline">
        <Button color="secondary" variant="outlined" endIcon={<SvgIcon type="plus" />}>
          Icon End
        </Button>
      </Box>
      <Box mt={4}>
        <Box display="inline">
          <Button color="primary">
            <SvgIcon type="plus" />
          </Button>
        </Box>
        <Box ml={2} display="inline">
          <Button color="secondary" variant="outlined">
            <SvgIcon type="plus" />
          </Button>
        </Box>
        <Box ml={2} display="inline">
          <Button color="secondary" variant="text">
            <SvgIcon type="plus" />
          </Button>
        </Box>
        <Box ml={2} display="inline">
          <Button color="error" variant="outlined">
            <SvgIcon type="plus" />
          </Button>
        </Box>
      </Box>
      <Box mt={4}>
        <Box display="inline">
          <Button color="primary" size="small">
            <SvgIcon type="plus" />
          </Button>
        </Box>
        <Box display="inline" ml={2}>
          <Button color="secondary" variant="outlined" size="small">
            <SvgIcon type="plus" />
          </Button>
        </Box>
        <Box ml={2} display="inline">
          <Button color="secondary" variant="text" size="small">
            <SvgIcon type="plus" />
          </Button>
        </Box>
        <Box ml={2} display="inline">
          <Button color="error" variant="outlined" size="small">
            <SvgIcon type="plus" />
          </Button>
        </Box>
      </Box>
    </>
  ),
};

export const IconButtonBasic: Story = {
  render: () => (
    <>
      <Typography variant="h3">Icon Buttons</Typography>
      <Typography>{`(Should only be used in rare cases, like dialog close button)`}</Typography>
      <Box display="flex" gap={2}>
        <IconButton>
          <SvgIcon type="minus" />
        </IconButton>
        <IconButton color="primary">
          <SvgIcon type="plus" />
        </IconButton>
        <IconButton color="secondary">
          <SvgIcon type="plus" />
        </IconButton>
      </Box>
      <Box display="flex" gap={2} mt={2}>
        <IconButton size="small">
          <SvgIcon type="minus" />
        </IconButton>
        <IconButton color="primary" size="small">
          <SvgIcon type="plus" />
        </IconButton>
        <IconButton color="secondary" size="small">
          <SvgIcon type="plus" />
        </IconButton>
      </Box>
    </>
  ),
};
