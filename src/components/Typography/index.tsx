import {forwardRef, type ForwardedRef} from 'react';
import {Typography as MUITypography} from '@mui/material';
import type {TypographyProps as MUITypographyProps} from '@mui/material';
import type {
  BordersProps as MUIBordersProps,
  DisplayProps as MUIDisplayProps,
  FlexboxProps as MUIFlexboxProps,
  GridProps as MUIGridProps,
  PositionsProps as MUIPositionsProps,
  SizingProps as MUISizingProps,
  SpacingProps as MUISpacingProps,
} from '@mui/system';

type SystemProps =
  | keyof MUISpacingProps
  | keyof MUIGridProps
  | keyof MUIDisplayProps
  | keyof MUIPositionsProps
  | keyof MUIFlexboxProps
  | keyof MUIBordersProps
  | keyof MUISizingProps
  | 'gutterBottom'
  | 'paragraph'
  | 'align'
  | 'textAlign'
  | 'boxShadow'
  | 'bgcolor';

type TypographyProps<C extends React.ElementType> = Omit<MUITypographyProps, SystemProps> & {
  component?: C;
};

const TypographyComponent = <C extends React.ElementType>(
  props: TypographyProps<C>,
  ref: ForwardedRef<HTMLElement>
) => <MUITypography ref={ref} {...props} />;

export const Typography = forwardRef(TypographyComponent);

Typography.displayName = 'Typography';

export type {TypographyProps};
