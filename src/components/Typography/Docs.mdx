import { Meta, <PERSON>vas, Story, Controls, Description } from "@storybook/blocks";
import * as TypographyStories from './index.stories.tsx';

<Meta of={TypographyStories}/>

## Overview

Typography is used to display text on a page. The component is used to set the font family, size, weight, and color of text.

<Story of={TypographyStories.AdditionalInformation}/>

<Canvas of={TypographyStories.Basic} />
<Controls of={TypographyStories.Basic}/>

## Variants
<Canvas of={TypographyStories.Variants} />

## Colors
<Canvas of={TypographyStories.Colors} />

## Weights
These font weight options should not be referenced directly, and are defined as is required by the MUI theme. `Typography` components should be utilized which have font weights specified per variant.
<Canvas of={TypographyStories.Weights} />



