import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {Box, Divider, Typography} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';
import type {TYPOGRAPHY_VARIANTS} from 'src/tokens/constants';
// use getCssFontFile for documentation only
// eslint-disable-next-line @typescript-eslint/no-restricted-imports
import {getCssFontFile} from 'src/tokens/muiTheme';

import {ClickToCopy, ExternalLink} from 'src/storybook-utils/story-components';

const VARIANTS: Array<{
  variant: (typeof TYPOGRAPHY_VARIANTS)[number];
  text: string;
  description: string;
}> = [
  {
    variant: 'h1',
    text: 'Header 1',
    description: 'h1 bold main heading',
  },
  {
    variant: 'h2',
    text: 'Header 2',
    description: 'h2 bold subheading',
  },
  {
    variant: 'h3',
    text: 'Header 3',
    description: 'h3 bold subheading',
  },
  {
    variant: 'h4',
    text: 'Header 4',
    description: 'h4 bold subheading',
  },
  {
    variant: 'h5',
    text: 'Header 5',
    description: 'h5 bold subheading',
  },
  {
    variant: 'h6',
    text: 'Header 6',
    description: 'h6 bold subheading',
  },
  {
    variant: 'body1',
    text: 'Body 1',
    description: 'body1 regular paragraph',
  },
  {
    variant: 'body2',
    text: 'Body 2',
    description: 'body2 regular paragraph',
  },
];

const WEIGHTS = ['fontWeightLight', 'fontWeightRegular', 'fontWeightMedium', 'fontWeightBold'];

const COLORS = [
  'primary',
  'secondary',
  'semanticPalette.text.main',
  'semanticPalette.text.secondary',
  'semanticPalette.text.brand',
  'semanticPalette.text.success',
  'semanticPalette.text.info',
  'semanticPalette.text.warning',
  'semanticPalette.text.error',
  'categoryPalette.0.text',
  'categoryPalette.1.text',
  'categoryPalette.2.text',
  'categoryPalette.3.text',
];

const typographyArgTypeCategories = categorizeArgTypes(
  ['children', 'component', 'ref'],
  'MUI System Props'
);
const meta: Meta<typeof Typography> = {
  argTypes: {...typographyArgTypeCategories},
  component: Typography,
  title: 'components/Data Display/Typography',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1778-16706',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Typography>;

export const Basic: Story = {
  args: {
    variant: 'h1',
    children: 'Scale your impact with Resilient Agriculture',
  },
  render: args => <Typography {...args} />,
};

export const Variants: Story = {
  render: () => (
    <>
      {VARIANTS.map(variant => (
        <Box mb={4} display="grid" gridTemplateColumns="1.5fr 3fr">
          <Box mr={8}>
            <Typography variant={variant.variant}>{variant.text}</Typography>
          </Box>
          <Box>
            <p>{variant.description}</p>
          </Box>
        </Box>
      ))}
    </>
  ),
};

export const Weights: Story = {
  render: () => (
    <>
      {WEIGHTS.map((weight, i) => (
        <div key={weight}>
          <Box my={4}>
            <Typography variant="body1" fontWeight={weight} fontSize="20px">
              {weight} - Scale your impact with Resilient Agriculture
            </Typography>
          </Box>
          {i !== WEIGHTS.length - 1 && <Divider />}
        </div>
      ))}
    </>
  ),
};

export const Colors: Story = {
  render: () => (
    <>
      {COLORS.map((color, i) => (
        <div key={color}>
          <Box my={4}>
            <Typography variant="body1" color={color} fontSize="20px">
              {color} - Scale your impact with Resilient Agriculture
            </Typography>
          </Box>
          {i !== COLORS.length - 1 && <Divider />}
        </div>
      ))}
    </>
  ),
};

export const AdditionalInformation: Story = {
  render: (args, {globals: {muiThemeKey}}) => (
    <div
      style={{
        fontFamily: 'Nunito Sans',
        fontSize: '14px',
        margin: '16px 0',
        lineHeight: '24px',
        color: '#2E3438',
      }}
    >
      See full MUI Typography API{' '}
      <ExternalLink href="https://mui.com/material-ui/api/typography/">here</ExternalLink>. Note,
      all spacing related properties were removed per Regrow requirements. Font face css
      declarations can be found as a public asset file per theme. This file should be included
      within your application. JS import for Font-face CSS file for the current theme:
      <ClickToCopy>{`import '@regrow-internal/design-system/fonts/${getCssFontFile(
        muiThemeKey
      )}'`}</ClickToCopy>
    </div>
  ),
};
