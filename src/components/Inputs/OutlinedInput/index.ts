import type {Components, OutlinedInputProps, Theme} from '@mui/material';
import {inputBaseClasses, OutlinedInput, outlinedInputClasses} from '@mui/material';

// Note: This component has augmented props via InputBase. See src/muiTheme.d.ts for prop overrides
export const OutlinedInputOverrides: Components<Theme>['MuiOutlinedInput'] = {
  defaultProps: {
    notched: false,
  },
  styleOverrides: {
    root: ({theme}) => ({
      background: theme.palette.semanticPalette.surface.main,
      [`& .${outlinedInputClasses.notchedOutline}, &:hover .${outlinedInputClasses.notchedOutline}, &.${outlinedInputClasses.disabled} .${outlinedInputClasses.notchedOutline}`]:
        {
          borderColor: theme.palette.semanticPalette.stroke.main,
        },
      [`&.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]: {
        borderColor: theme.palette.semanticPalette.stroke.brand,
      },
      [`&.${outlinedInputClasses.error}`]: {
        background: theme.palette.semanticPalette.surface.error,
      },
      [`&.${outlinedInputClasses.error}:hover .${outlinedInputClasses.notchedOutline}, &.${outlinedInputClasses.error}.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]:
        {
          borderColor: theme.palette.semanticPalette.stroke.error,
        },
      [`& .${inputBaseClasses.inputAdornedEnd}`]: {
        textOverflow: 'ellipsis',
      },
    }),
  },
};

export {OutlinedInput, type OutlinedInputProps};
