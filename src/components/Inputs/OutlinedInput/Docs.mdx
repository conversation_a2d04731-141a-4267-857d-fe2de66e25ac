import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as OutlinedInputStories from './index.stories.tsx'

<Meta of={OutlinedInputStories} />

## Overview
For a pre-composed `OutlinedInput` with a `Label` and `FormHelperText` please use `<TextField variant="filled" />`. <br />
This component should be used when a more custom solution is required than what `TextField` can provide. <br />
`OutlinedInput` should be composed with `FormControl` and `FormLabel`. <br /> 

[MUI OutlinedInput](https://mui.com/material-ui/api/outlined-input/)

<Canvas of={OutlinedInputStories.Basic} />
<Controls of={OutlinedInputStories.Basic}/>

## Composed Outlined Input
Utilizes `FormControl`, `FormLabel` and `FormHelperText`
<Canvas of={OutlinedInputStories.ComposedInput} />

### States
<Canvas of={OutlinedInputStories.States} />

### Input Types
<Canvas of={OutlinedInputStories.Types} />

## FullWidth
<Canvas of={OutlinedInputStories.FullWidth} />

