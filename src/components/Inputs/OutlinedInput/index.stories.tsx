import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {
  Box,
  FormControl,
  FormHelperText,
  FormLabel,
  InputAdornment,
  OutlinedInput,
  Stack,
  SvgIcon,
  Typography,
} from 'src/index';

import {ExternalLink} from 'src/storybook-utils/story-components';

import argTypes from './argTypes';

const meta: Meta<typeof OutlinedInput> = {
  component: OutlinedInput,
  title: 'components/Inputs/OutlinedInput',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=10150-7083&mode=design&t=hYrKeDviZBiwl8B4-0',
    },
  },
};

export default meta;

type Story = StoryObj<typeof OutlinedInput>;

export const Basic: Story = {
  render: props => (
    <>
      <Typography variant="body2" color="secondary">
        The <code>outlined</code> variant is the default general use input.
      </Typography>
      <Box mt={2}>
        <FormControl>
          <OutlinedInput
            {...props}
            inputProps={{'aria-label': 'input-outlined'}}
            defaultValue="outlined input"
          />
        </FormControl>
      </Box>
    </>
  ),
};

export const ComposedInput: Story = {
  render: props => (
    <FormControl>
      <Stack rowGap={2} direction="row">
        <Box height={theme => theme.spacing(9)} display="flex" alignItems="center" mr={2}>
          <FormLabel htmlFor="component-outlined">Form Label: </FormLabel>
        </Box>
        <Box>
          <OutlinedInput
            id="component-outlined"
            endAdornment={
              <InputAdornment position="end">
                <SvgIcon type="filter" />
              </InputAdornment>
            }
            placeholder="placeholder"
            {...props}
          />
          <Box mt={2}>
            <FormHelperText>Form helper text</FormHelperText>
          </Box>
        </Box>
      </Stack>
    </FormControl>
  ),
};

export const States = {
  render: () => (
    <>
      <Typography variant="body2" color="secondary">
        The <code>outlined</code> variant accommodates <code>disabled</code>, <code>required</code>,{' '}
        <code>readOnly</code>, <code>focused</code> and <code>error</code> states.
      </Typography>
      <Box mt={2} width={theme => theme.fixedWidths.xs}>
        <Stack gap={2}>
          <Box mb={5}>
            <FormControl>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-1">OutlinedInput Label</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-1" />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-2">OutlinedInput DefaultValue</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-2" defaultValue="defaultValue" />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-3">OutlinedInput Placeholder</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-3" placeholder="Placeholder text" />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl disabled>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-4">OutlinedInput Disabled</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-4" value="disabled" />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl required>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-5">OutlinedInput Required</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-5" placeholder="required" />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-6">OutlinedInput ReadOnly</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-6" readOnly value="readOnly" />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl focused>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-7">OutlinedInput Focused</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-7" placeholder="focused" />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl error>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-8">OutlinedInput Error</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-8" placeholder="error" />
              <Box mt={2}>
                <FormHelperText>This is an error message</FormHelperText>
              </Box>
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl error focused>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-9">OutlinedInput Focused Error</FormLabel>
              </Box>
              <OutlinedInput id="component-outlined-9" placeholder="focused error" />
              <Box mt={2}>
                <FormHelperText>This is a serious error message</FormHelperText>
              </Box>
            </FormControl>
          </Box>
        </Stack>
      </Box>
    </>
  ),
};

export const Types: Story = {
  render: () => {
    return (
      <Stack width={theme => theme.fixedWidths.xs}>
        <Box mb={2}>
          <FormControl>
            <OutlinedInput
              type="password"
              placeholder="password"
              endAdornment={
                <InputAdornment position="end">
                  <SvgIcon type="visibility" />
                </InputAdornment>
              }
              fullWidth
              inputProps={{'aria-label': 'Password Demo'}}
            />
          </FormControl>
        </Box>
        <Box mb={2}>
          <FormControl>
            <OutlinedInput
              type="number"
              placeholder="number"
              endAdornment={<InputAdornment position="end">kg</InputAdornment>}
              fullWidth
              inputProps={{'aria-label': 'Number Demo'}}
            />
          </FormControl>
        </Box>
        <Box mb={2}>
          <FormControl>
            <OutlinedInput
              type="search"
              placeholder="search"
              startAdornment={
                <InputAdornment position="start">
                  <SvgIcon type="search" />
                </InputAdornment>
              }
              fullWidth
              inputProps={{'aria-label': 'Search Demo'}}
            />
          </FormControl>
        </Box>
        <Box mt={6} mb={3}>
          <Typography variant="body2">
            The variant below showcases the usage of multiline which renders a <code>textArea</code>{' '}
            instead of an <code>input</code>.
          </Typography>
        </Box>
        <Box mb={3}>
          <FormControl>
            <OutlinedInput multiline fullWidth inputProps={{'aria-label': 'Textarea Demo'}} />
          </FormControl>
        </Box>
        <Box mt={6} mb={3}>
          <Typography variant="body2">
            The variants below showcase the usage of the{' '}
            <ExternalLink href="https://mui.com/material-ui/react-text-field/#input-adornments">
              InputAdornment prop.
            </ExternalLink>
          </Typography>
        </Box>
        <Box display="block" mb={2}>
          <Box display="block" mb={2}>
            <FormControl>
              <OutlinedInput
                type="text"
                placeholder="Start Adornment"
                startAdornment={
                  <InputAdornment position="start">
                    <SvgIcon type="livestock" />
                  </InputAdornment>
                }
                fullWidth
                inputProps={{'aria-label': 'Start Adornment Demo'}}
              />
            </FormControl>
          </Box>
          <Box display="block" mb={2}>
            <FormControl>
              <OutlinedInput
                type="text"
                placeholder="End Adornment"
                endAdornment={
                  <InputAdornment position="end">
                    <Typography variant="body1" color="text.primary">
                      kg
                    </Typography>
                  </InputAdornment>
                }
                fullWidth
                inputProps={{'aria-label': 'End Adornment Demo'}}
              />
            </FormControl>
          </Box>
        </Box>
      </Stack>
    );
  },
};

export const FullWidth: Story = {
  render: () => (
    <Stack gap={5}>
      <FormControl fullWidth>
        <FormLabel htmlFor="component-outlined-1">FullWidth OutlinedInput Label</FormLabel>
        <OutlinedInput id="component-outlined-1" placeholder="fullWidth OutlinedInput" />
      </FormControl>
    </Stack>
  ),
};
