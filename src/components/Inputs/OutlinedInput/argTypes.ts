import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const outlinedInputArgTypes = [
  'autoComplete',
  'autoFocus',
  'classes',
  'color',
  'components',
  'componentsProps',
  'defaultValue',
  'disabled',
  'endAdornment',
  'error',
  'fullWidth',
  'id',
  'inputComponent',
  'inputProps',
  'inputRef',
  'maxRows',
  'minRows',
  'multiline',
  'name',
  'onChange',
  'placeholder',
  'readOnly',
  'required',
  'rows',
  'slotProps',
  'slots',
  'startAdornment',
  'sx',
  'type',
  'value',
];

const argTypes = getArgTypes(outlinedInputArgTypes, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
});

export default argTypes;
