import type {<PERSON>a, StoryObj} from '@storybook/react';
import {
  Box,
  InputAdornment,
  Label,
  MenuItem,
  Stack,
  SvgIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from 'src/index';

import {ExternalLink} from 'src/storybook-utils/story-components';

import argTypes from './argTypes';

type Story = StoryObj<typeof TextField>;

export default {
  component: TextField,
  title: 'components/Inputs/TextField',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1808-17141&mode=design&t=qdj6CO3MAYfevq7u-0',
    },
  },
} as Meta<typeof TextField>;

export const Basic: Story = {
  args: {
    label: 'Basic TextField',
    helperText: 'This is the input helper text',
  },
  render: props => {
    return <TextField {...props} />;
  },
};

export const Variants: Story = {
  render: () => {
    return (
      <>
        <Typography variant="body2" color="secondary">
          The <code>outlined</code> (default) variant is intended to cover most of our input use
          cases.
        </Typography>
        <Box mb={4} mt={2}>
          <TextField variant="outlined" placeholder="outlined" />
        </Box>
        <Typography variant="body2" color="secondary">
          The <code>filled</code> variant is only used for table inputs in the MRV data collection
          forms.
        </Typography>
        <Box mb={2} mt={2}>
          <TextField variant="filled" placeholder="filled" />
        </Box>
      </>
    );
  },
};

export const OutlinedStates: Story = {
  render: () => {
    return (
      <>
        <Box mb={2}>
          <Typography variant="body2" color="secondary">
            The <code>outlined</code> (default) variant is intended to cover most of our select use
            cases.
          </Typography>
        </Box>
        <Box mb={2}>
          <Typography variant="body2" color="secondary">
            This variant accommodates <code>disabled</code>, <code>required</code>,{' '}
            <code>readOnly</code>, <code>focused</code> and <code>error</code> states.
          </Typography>
        </Box>
        <Box mt={4} mb={4}>
          <TextField placeholder="Placeholder text" label={<Label title="Input Placeholder" />} />
        </Box>
        <Box mb={4}>
          <TextField placeholder="disabled" label={<Label title="Input Disabled" />} disabled />
        </Box>
        <Box mb={4}>
          <TextField placeholder="required" label={<Label title="Input Required" />} required />
        </Box>
        <Box mb={4}>
          <TextField
            value="readOnly"
            label={<Label title="Input Read Only" />}
            InputProps={{readOnly: true}}
            helperText={"This is the input's helper text"}
          />
        </Box>
        <Box mb={4}>
          <TextField
            placeholder="focused"
            focused
            label={<Label title="Input Focused" />}
            helperText={"This is the input's helper text"}
          />
        </Box>
        <Box mb={4}>
          <TextField
            placeholder="error"
            error
            label={<Label title="Input Error" />}
            helperText={'This is an error message'}
          />
        </Box>
        <Box mb={4}>
          <TextField
            placeholder="focused error"
            error
            focused
            label={<Label title="Input Focused Error" />}
            helperText={'This is an error message'}
          />
        </Box>
      </>
    );
  },
};

export const FilledStates: Story = {
  render: () => {
    return (
      <>
        <Box mb={2}>
          <Typography variant="body2" color="secondary">
            The <code>filled</code> variant is intended to be used only in tables.
          </Typography>
        </Box>
        <Box mb={2}>
          <Typography variant="body2" color="secondary">
            This variant accommodates <code>disabled</code>, <code>readOnly</code>,{' '}
            <code>focused</code> and <code>error</code> states.
          </Typography>
        </Box>
        <Box mb={4}>
          <TextField
            variant="filled"
            placeholder="Placeholder text"
            inputProps={{'aria-label': 'Input1'}}
          />
        </Box>
        <Box mb={2}>
          <TextField
            variant="filled"
            placeholder="disabled"
            disabled
            inputProps={{'aria-label': 'Input2'}}
          />
        </Box>
        <Box mb={4}>
          <TextField
            variant="filled"
            value="readOnly"
            InputProps={{'aria-label': 'Input3', readOnly: true}}
          />
        </Box>
        <Box mb={2}>
          <TextField
            variant="filled"
            placeholder="focused"
            focused
            inputProps={{'aria-label': 'Input4'}}
          />
        </Box>
        <Box mb={2}>
          <TextField
            variant="filled"
            placeholder="error"
            error
            inputProps={{'aria-label': 'Input5'}}
          />
        </Box>
        <Box mb={2}>
          <TextField
            variant="filled"
            placeholder="focused error"
            focused
            error
            inputProps={{'aria-label': 'Input6'}}
          />
        </Box>
      </>
    );
  },
};

export const FilledColors: Story = {
  render: () => {
    return (
      <>
        <Stack gap={2}>
          <Typography variant="body2" color="secondary">
            The <code>filled</code> variant can be used alongside color options. Note this variant
            is only intended to be used for table inputs.
          </Typography>
          <Typography variant="body2" color="secondary">
            Also note that the <code>error</code> prop is independent of <code>color="error"</code>.
            Both achieve the same input styling.
          </Typography>
        </Stack>
        <Box mt={2} mb={8} width={theme => theme.fixedWidths.xs}>
          <Stack gap={2}>
            <TextField
              variant="filled"
              placeholder="default"
              inputProps={{'aria-label': 'Input6'}}
            />
            <TextField
              variant="filled"
              placeholder="color='error'"
              color="error"
              inputProps={{'aria-label': 'Input7'}}
            />
            <TextField
              variant="filled"
              placeholder="color='warning'"
              color="warning"
              inputProps={{'aria-label': 'Input8'}}
            />
          </Stack>
        </Box>
        <Typography variant="body2" color="secondary">
          Sample usage in <code>Table</code>
        </Typography>
        <Box mt={2} width={theme => theme.fixedWidths.lg}>
          <TableContainer>
            <Table padding="none">
              <TableHead>
                <TableRow>
                  <TableCell>Field 1</TableCell>
                  <TableCell>Field 2</TableCell>
                  <TableCell>Field 3</TableCell>
                  <TableCell>Field 4</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell>
                    <TextField
                      variant="filled"
                      placeholder="default"
                      fullWidth
                      inputProps={{'aria-label': 'Input01'}}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      variant="filled"
                      defaultValue="disabled"
                      fullWidth
                      disabled
                      inputProps={{'aria-label': 'Input02'}}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Typography color="text.disabled">m2</Typography>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      variant="filled"
                      placeholder="color='warning'"
                      fullWidth
                      color="warning"
                      inputProps={{'aria-label': 'Input03'}}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <SvgIcon type="livestock" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      type="number"
                      variant="filled"
                      placeholder="color='error'"
                      fullWidth
                      color="error"
                      inputProps={{'aria-label': 'Input04'}}
                      InputProps={{
                        endAdornment: (
                          <>
                            <Typography variant="body1" color="text.primary">
                              kg
                            </Typography>
                            <Box component="span" pl={2}>
                              <SvgIcon type="cross-circled" color="error" fontSize="body1" />
                            </Box>
                          </>
                        ),
                      }}
                    />
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <TextField
                      type="date"
                      variant="filled"
                      fullWidth
                      inputProps={{'aria-label': 'Input11'}}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      variant="filled"
                      fullWidth
                      error
                      value="error"
                      inputProps={{'aria-label': 'Input12'}}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      variant="filled"
                      placeholder="with tooltip"
                      fullWidth
                      inputProps={{'aria-label': 'Input13'}}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Tooltip id="" title="tooltip">
                              <span>
                                <SvgIcon type="info-circled" />
                              </span>
                            </Tooltip>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      type="number"
                      variant="filled"
                      select
                      fullWidth
                      error
                      inputProps={{'aria-label': 'Input14'}}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <SvgIcon type="cross-circled" color="error" />
                          </InputAdornment>
                        ),
                      }}
                      SelectProps={{
                        displayEmpty: true,
                        renderValue: selected =>
                          selected ?? (
                            <Typography color="text.disabled">select placeholder</Typography>
                          ),
                      }}
                    >
                      <MenuItem value="" disabled>
                        select placeholder
                      </MenuItem>
                      <MenuItem value="1">Option 1</MenuItem>
                      <MenuItem value="2">Option 2</MenuItem>
                      <MenuItem value="3">Option 3</MenuItem>
                    </TextField>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </>
    );
  },
};

export const Select: Story = {
  render: () => {
    return (
      <>
        <Stack spacing={8}>
          <Box mb={2}>
            <Typography variant="h5">Outlined Variant</Typography>
            <Typography variant="body2" color="secondary">
              The <code>outlined</code> (default) variant is intended to cover most of our select
              use cases.
            </Typography>
            <Typography variant="body2" color="secondary">
              This variant accommodates <code>disabled</code>, <code>required</code>,{' '}
              <code>readOnly</code>, <code>focused</code> and <code>error</code> states.
            </Typography>
          </Box>
          <Box mb={5}>
            <TextField variant="outlined" select label="TextField select">
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField variant="outlined" select label={<Label title="Select Label" />}>
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              label={<Label title="Select DefaultValue" />}
              defaultValue="3"
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              label={<Label title="Select Placeholder" />}
              SelectProps={{
                displayEmpty: true,
                renderValue: selected =>
                  selected ?? <Typography color="text.placeholder">Select placeholder</Typography>,
              }}
            >
              <MenuItem value="" disabled>
                select placeholder
              </MenuItem>
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              disabled
              label={<Label title="Select Disabled" />}
              defaultValue="1"
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5} width={theme => theme.fixedWidths.xs}>
            <TextField
              variant="outlined"
              select
              required
              label={<Label title="Select Required" />}
              defaultValue="1"
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              InputProps={{readOnly: true}}
              label={<Label title="Select ReadOnly" />}
              defaultValue="1"
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              defaultValue="1"
              focused
              label={<Label title="Select Focused" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              defaultValue="1"
              error
              label={<Label title="Select Error" />}
              helperText={'This is an error message'}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              defaultValue="1"
              focused
              error
              label={<Label title="Select Focused Error" />}
              helperText={'This is an error message'}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
        </Stack>
        <Stack spacing={8}>
          <Box mt={8} mb={2}>
            <Typography variant="h5">Filled Variant</Typography>
            <Typography variant="body2" color="secondary">
              The <code>filled</code> variant is intended to be used only in tables.
            </Typography>
            <Typography variant="body2" color="secondary">
              This variant accommodates <code>disabled</code>, <code>readOnly</code>,{' '}
              <code>focused</code> and <code>error</code> states.
            </Typography>
            <Typography variant="body2" color="secondary">
              Notice, there are two color options, <code>error</code> and <code>warning</code>, as
              well.
            </Typography>
          </Box>

          <Box mb={2}>
            <TextField
              variant="filled"
              select
              label={<Label title="Filled variant w/ default value" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              defaultValue="3"
              label={<Label title="Filled variant" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">defaultValue</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              label={<Label title="Filled variant w/ placeholder" />}
              SelectProps={{
                displayEmpty: true,
                renderValue: selected =>
                  selected ?? <Typography color="text.placeholder">Select placeholder</Typography>,
              }}
            >
              <MenuItem value="" disabled>
                select placeholder
              </MenuItem>
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              disabled
              select
              defaultValue="2"
              label={<Label title="Filled variant disabled" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">disabled</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              InputProps={{readOnly: true}}
              defaultValue="2"
              label={<Label title="Filled variant readOnly" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">readOnly</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              focused
              defaultValue="2"
              label={<Label title="Filled variant focused" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Focused</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              error
              defaultValue="2"
              label={<Label title="Filled variant error" />}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <SvgIcon type="cross-circled" color="error" />
                  </InputAdornment>
                ),
              }}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Error</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              error
              focused
              defaultValue="2"
              label={<Label title="Filled variant error focused" />}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <SvgIcon type="cross-circled" color="error" />
                  </InputAdornment>
                ),
              }}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Focused Error</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              color="error"
              defaultValue="2"
              label={<Label title="Filled variant color = error" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">color='error'</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
          <Box mb={2}>
            <TextField
              variant="filled"
              select
              color="warning"
              defaultValue="2"
              label={<Label title="Filled variant color = warning" />}
            >
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">color='warning'</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
        </Stack>
      </>
    );
  },
};

export const Types: Story = {
  render: () => {
    return (
      <Stack width={theme => theme.fixedWidths.xs}>
        <Box mb={2}>
          <TextField
            variant="outlined"
            type="password"
            label="Password type"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <SvgIcon type="visibility" />
                </InputAdornment>
              ),
            }}
            fullWidth
          />
        </Box>
        <Box mb={2}>
          <TextField
            variant="outlined"
            type="number"
            label="Number type"
            InputProps={{
              endAdornment: <InputAdornment position="end">kg</InputAdornment>,
            }}
            fullWidth
          />
        </Box>
        <Box mb={2}>
          <TextField
            variant="outlined"
            type="search"
            label="Search type"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SvgIcon type="search" />
                </InputAdornment>
              ),
            }}
            fullWidth
          />
        </Box>
        <Box mb={2}>
          <TextField
            variant="outlined"
            type="search"
            label="Search"
            placeholder="Search"
            error
            helperText={'This is an error message'}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SvgIcon type="search" />
                </InputAdornment>
              ),
            }}
            fullWidth
          />
        </Box>
        <Box mt={6} mb={3}>
          <Typography variant="body2">
            The variant below showcases the usage of multiline which renders a <code>textArea</code>{' '}
            instead of an <code>input</code>.
          </Typography>
        </Box>
        <Box mb={3}>
          <TextField variant="outlined" label="Multiline" multiline fullWidth />
        </Box>
        <Box mt={6} mb={3}>
          <Typography variant="body2">
            The variants below showcase the usage of the{' '}
            <ExternalLink href="https://mui.com/material-ui/react-text-field/#input-adornments">
              InputAdornment prop.
            </ExternalLink>
          </Typography>
        </Box>
        <Box display="block" mb={2}>
          <Box display="block" mb={2}>
            <TextField
              variant="outlined"
              type="text"
              placeholder="Start Adornment"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SvgIcon type="livestock" />
                  </InputAdornment>
                ),
              }}
              fullWidth
            />
          </Box>
          <Box display="block" mb={2}>
            <TextField
              variant="outlined"
              type="text"
              placeholder="End Adornment"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Typography variant="body1" color="text.primary">
                      kg
                    </Typography>
                  </InputAdornment>
                ),
              }}
              fullWidth
            />
          </Box>
          <Box mb={5}>
            <TextField
              variant="outlined"
              select
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <SvgIcon type="filter" />
                  </InputAdornment>
                ),
              }}
              SelectProps={{
                displayEmpty: true,
                renderValue: selected =>
                  selected ?? (
                    <Typography color="text.placeholder">Select End Adornment</Typography>
                  ),
              }}
            >
              <MenuItem value="" disabled>
                Select placeholder
              </MenuItem>
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">Option 2</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </TextField>
          </Box>
        </Box>
      </Stack>
    );
  },
};
