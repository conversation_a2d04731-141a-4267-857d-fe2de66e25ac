import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const textfieldArgTypeKeys = [
  'autoFocus',
  'color',
  'defaultValue',
  'disabled',
  'error',
  'fullWidth',
  'focused',
  'helperText',
  'id',
  'FormHelperTextProps',
  'InputLabelProps',
  'inputProps',
  'InputProps',
  'inputRef',
  'label',
  'multiline',
  'rows',
  'maxRows',
  'minRows',
  'name',
  'select',
  'onChange',
  'placeholder',
  'readOnly',
  'required',
  'SelectProps',
  'variant',
  'classes',
  'sx',
];

const TEXTFIELD_ARGTYPES = {
  color: {
    table: {
      type: {
        summary: 'undefined | error | warning',
      },
    },
    control: {
      type: 'select',
    },
    options: [undefined, 'error', 'warning'],
  },
  helperText: {
    description: 'The helper text content.',
    table: {
      category: 'Nested Components',
      type: {summary: 'node'},
    },
  },
  label: {
    description: 'The label content.',
    table: {
      category: 'Nested Components',
      type: {summary: 'node'},
    },
  },
};

const argTypes = getArgTypes(textfieldArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...TEXTFIELD_ARGTYPES,
});

export default argTypes;
