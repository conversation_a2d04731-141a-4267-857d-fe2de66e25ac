import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as TextFieldStories from './index.stories.tsx'

<Meta of={TextFieldStories} />

## Overview
`TextField` is a simplified, all-in-one `input`, `textarea` or `select` that can include an optional label and helper text. 

It is composed of more fundamental MUI Form components ( `FormControl`, `Input`, `FilledInput`, `OutlinedInput`, `InputLabel`, and `FormHelperText` ) that you can leverage directly if you need to build a more customized form input. When composing `Inputs`, instead of `InputLabel`, `FormLabel` should be used.

[MUI TextField](https://mui.com/material-ui/api/text-field/)

<Canvas of={TextFieldStories.Basic} />
<Controls of={TextFieldStories.Basic}/>

### Variants
<Canvas of={TextFieldStories.Variants} />

### Outlined Variant (default) States 
<Canvas of={TextFieldStories.OutlinedStates} />

### Filled Variant (Table inputs) States
<Canvas of={TextFieldStories.FilledStates} />

### Filled Variant Colors
<Canvas of={TextFieldStories.FilledColors} />

### Select
<Canvas of={TextFieldStories.Select} />

### Types
<Canvas of={TextFieldStories.Types} />
