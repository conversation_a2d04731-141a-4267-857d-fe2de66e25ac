import {INPUT_PLACEHOLDER_OPACITY} from 'src/tokens/constants';
import {
  formHelperTextClasses,
  inputLabelClasses,
  lighten,
  TextField as MuiTextField,
  selectClasses,
} from '@mui/material';
import type {InputLabelProps, TextFieldProps as MuiTextFieldProps} from '@mui/material';
import type {Components, Theme} from '@mui/material/styles';

import {FormHelperText, type FormHelperTextProps} from 'src/components/Inputs/FormHelperText';
import {type InputVariant} from 'src/components/Inputs/InputBase';
import {SelectIcon} from 'src/components/Inputs/Select';

// Note: This component has augmented props. See src/muiTheme.d.ts for prop overrides
export const TextFieldOverrides: Components<Theme>['MuiTextField'] = {
  defaultProps: {
    size: 'medium',
    InputLabelProps: {
      shrink: false,
      disableAnimation: false,
    },
  },
  styleOverrides: {
    root: ({theme}) => ({
      [`.${formHelperTextClasses.root}`]: {
        marginLeft: 0,
        marginTop: theme.spacing(2),
      },
      [`.${inputLabelClasses.formControl}`]: {
        position: 'relative',
        transform: 'none',
        paddingBottom: theme.spacing(2),
        maxWidth: 'unset',
        whiteSpace: 'unset',
      },
      [`.${selectClasses.icon}`]: {
        right: theme.spacing(3),
      },
      'label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input::-webkit-input-placeholder':
        {
          // MUI is using important and this placeholder lives in the shadow dom
          // See issue here: https://github.com/mui/material-ui/issues/8436#issuecomment-419711196
          color: lighten(theme.palette.text.secondary, INPUT_PLACEHOLDER_OPACITY),
          opacity: '1 !important',
        },
    }),
  },
};

export type TextFieldProps = Omit<
  MuiTextFieldProps,
  'autoComplete' | 'margin' | 'InputLabelProps' | 'FormHelperTextProps' | 'variant'
> & {
  variant?: InputVariant;
  InputLabelProps?: Omit<InputLabelProps, 'shrink' | 'disableAnimation'>;
  // manage known type issue overriding component - https://github.com/mui/material-ui/issues/33339
  FormHelperTextProps?: FormHelperTextProps<'div'> & {component: React.ElementType};
};

export const TextField = (props: TextFieldProps) => {
  const {color, InputProps, inputProps, FormHelperTextProps, SelectProps, select, ...rest} = props;

  const selectProps = {
    ...SelectProps,
    IconComponent: SelectProps?.IconComponent ?? SelectIcon,
  };

  const formHelperTextProps = {
    ...FormHelperTextProps,
    component: FormHelperText,
  };

  return (
    <MuiTextField
      FormHelperTextProps={formHelperTextProps}
      InputProps={InputProps}
      inputProps={inputProps}
      color={color}
      {...(select && {select, SelectProps: selectProps})}
      {...rest}
    />
  );
};
