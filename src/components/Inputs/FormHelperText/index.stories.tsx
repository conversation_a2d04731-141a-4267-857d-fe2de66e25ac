import type {<PERSON><PERSON>, <PERSON>Obj} from '@storybook/react';
import React from 'react';
import {
  Box,
  Divider,
  FormControl,
  FormHelperText,
  FormLabel,
  MenuItem,
  OutlinedInput,
  Select,
  SelectField,
  Stack,
  TextField,
  Typography,
} from 'src/index';

const meta: Meta<typeof FormHelperText> = {
  component: FormHelperText,
  title: 'components/Inputs/FormHelperText',
};

export default meta;

type Story = StoryObj<typeof FormHelperText>;

const ExampleSelectField = () => {
  const [value, setValue] = React.useState('');
  return (
    <SelectField
      onChange={e => setValue(e.target.value)}
      value={value}
      options={[
        {label: 'Option 1', value: '1'},
        {label: 'error', value: '2'},
        {label: 'Option 3', value: '3'},
      ]}
    />
  );
};

export const Basic: Story = {
  render: args => <FormHelperText {...args}>Some helper text</FormHelperText>,
};

export const Usage: Story = {
  render: () => (
    <>
      <Stack spacing={4}>
        <Typography variant="body1">
          TextField renders the FormHelperText component implicitly.
        </Typography>
        <Divider />
        <Box my={4}>
          <TextField label="TextField Example" helperText="Helper text regular" />
        </Box>
        <Box my={4}>
          <TextField label="TextField Error Example" error helperText="Helper text error" />
        </Box>
      </Stack>
      <Stack spacing={4} mt={12}>
        <Typography variant="body1">
          Select components need to be composed with FormHelperText
        </Typography>
        <Divider />
        <Box my={4}>
          <FormControl>
            <Box mb={2}>
              <FormLabel>Select Example</FormLabel>
            </Box>
            <Select defaultValue="2">
              <MenuItem value="1">Option 1</MenuItem>
              <MenuItem value="2">error</MenuItem>
              <MenuItem value="3">Option 3</MenuItem>
            </Select>
            <Box mt={2}>
              <FormHelperText> Form helper text</FormHelperText>
            </Box>
          </FormControl>
        </Box>
        <Box my={4}>
          <Box>
            <FormControl error>
              <Box mb={2}>
                <FormLabel>Select Example Error</FormLabel>
              </Box>
              <Select defaultValue="2">
                <MenuItem value="1">Option 1</MenuItem>
                <MenuItem value="2">error</MenuItem>
                <MenuItem value="3">Option 3</MenuItem>
              </Select>
              <Box mt={2}>
                <FormHelperText> Form helper text</FormHelperText>
              </Box>
            </FormControl>
          </Box>
        </Box>
      </Stack>
      <Stack spacing={4} mt={12}>
        <Typography variant="body1">
          SelectField components need to be composed with FormHelperText
        </Typography>
        <Divider />
        <Box my={4}>
          <FormControl>
            <Box mb={2}>
              <FormLabel>SelectField Example</FormLabel>
            </Box>
            <ExampleSelectField />
            <Box mt={2}>
              <FormHelperText> Form helper text</FormHelperText>
            </Box>
          </FormControl>
        </Box>
        <Box my={4}>
          <FormControl error>
            <Box mb={2}>
              <FormLabel>SelectField Example Error</FormLabel>
            </Box>
            <ExampleSelectField />
            <Box mt={2}>
              <FormHelperText> Form helper text</FormHelperText>
            </Box>
          </FormControl>
        </Box>
      </Stack>
      <Stack spacing={4} mt={12}>
        <Typography variant="body1">
          OutlinedInput components need to be composed with FormHelperText
        </Typography>
        <Divider />
        <Box my={4}>
          <FormControl>
            <Box mb={2}>
              <FormLabel>OutlinedInput Example</FormLabel>
            </Box>
            <OutlinedInput />
            <Box mt={2}>
              <FormHelperText> Form helper text</FormHelperText>
            </Box>
          </FormControl>
        </Box>
        <Box my={4}>
          <FormControl error>
            <Box mb={2}>
              <FormLabel>OutlinedInput Example Error</FormLabel>
            </Box>
            <OutlinedInput />
            <Box mt={2}>
              <FormHelperText> Form helper text</FormHelperText>
            </Box>
          </FormControl>
        </Box>
      </Stack>
    </>
  ),
};

export const Error: Story = {
  args: {
    error: true,
  },
  render: args => <FormHelperText {...args}>Helper text when there is an error</FormHelperText>,
};
