import {
  FormHelperText as MuiFormHelperText,
  useFormControl,
  type Components,
  type FormHelperTextProps as MuiFormHelperTextProps,
  type Theme,
} from '@mui/material';

import {SvgIcon} from 'src/components/Icon';

export const FormHelperTextOverrides: Components<Theme>['MuiFormHelperText'] = {
  styleOverrides: {
    root: ({theme}) => ({
      margin: 0,
      display: 'flex',
      alignItems: 'center',
      '& > svg': {
        marginRight: theme.spacing(1),
      },
    }),
  },
};

type FormHelperTextProps<D extends React.ElementType> = Omit<
  MuiFormHelperTextProps<D>,
  'variant' | 'margin' | 'filled'
>;

const FormHelperText = <D extends React.ElementType>(props: FormHelperTextProps<D>) => {
  const muiFormControl = useFormControl();

  const {error} = muiFormControl || {
    error: props.error,
  };

  return (
    <MuiFormHelperText component="div" {...props} variant="outlined">
      {error && <SvgIcon type="cross-circled" />}
      {props.children}
    </MuiFormHelperText>
  );
};

export {FormHelperText, type FormHelperTextProps};
