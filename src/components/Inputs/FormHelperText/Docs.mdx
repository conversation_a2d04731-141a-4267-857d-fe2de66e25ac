import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as FormHelperText from './index.stories.tsx'

<Meta of={FormHelperText} />

## Overview
[MUI FormHelperText](https://mui.com/material-ui/api/form-helper-text/)

`FormHelperText` is composed with `TextField` by default to render helper text for form inputs, including error messages.
`FormHelperText` should be composed with `OutlinedlinedInput`, `Select` or `SelectField` to render helper text for form inputs, including error messages.

<Canvas of={FormHelperText.Basic} />
<Controls of={FormHelperText.Basic}/>

### Error
<Canvas of={FormHelperText.Error} />

### Usage
<Canvas of={FormHelperText.Usage} />