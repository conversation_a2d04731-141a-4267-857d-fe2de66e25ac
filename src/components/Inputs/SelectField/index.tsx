import {useCallback, useState} from 'react';
import {MultiSelectText, SelectAllText} from 'src/utils/staticText';
import {isEmptyArray, isNil, isNonEmptyArray} from 'src/utils/typeGuards';
import {
  Box,
  filledInputClasses,
  IconButton,
  iconButtonClasses,
  InputAdornment,
  inputAdornmentClasses,
  inputBaseClasses,
  ListItemButton,
  selectClasses,
  Stack,
  useFormControl,
} from '@mui/material';

import {Checkbox} from 'src/components/Checkbox';
import {Divider} from 'src/components/Divider';
import {SvgIcon} from 'src/components/Icon';
import type {SelectChangeEvent} from 'src/components/Inputs/Select';
import {Select, type SelectProps} from 'src/components/Inputs/Select';
import {MenuItem} from 'src/components/Menu';
import {Typography} from 'src/components/Typography';

type Option<Value> = {
  label: string | number | JSX.Element;
  value: Value extends Array<unknown> ? Value[0] : Value;
  disabled?: boolean;
};

export type SelectOptions<Value> = Array<Option<Value>> | undefined;

type GetMultiSelectTextType = ({selectedCount}: {selectedCount: number}) => string;

type SelectFieldCommonProps<Value> = Omit<
  SelectProps<Value>,
  'onChange' | 'value' | 'multiple' | 'displayEmpty'
> & {
  placeholder?: string;
  options: SelectOptions<Value>;
  onChange: (e: SelectChangeEvent<Value>) => void;
  value: Value;
  /**
   * If present, an x will appear in the select field input and clear the selected items when clicked;.
   * */
  hasClear?: boolean;
};

export type SelectFieldSingleProps = {
  multiple?: false;
  localeText?: never;
  hasSelectAll?: never;
};

type SelectFieldPropsMultiWithSelectAll = {
  hasSelectAll: true;
  localeText: {
    selectAll: string;
    deselectAll: string;
    /**
     * A function to format the text displayed when multiple items are selected. This should be `x item's selected` in the correct locale.
     *
     * example:
     * ```ts
     * const formatMessage = ({selectedCount}: {selectedCount: number}) => {
     *    return `${selectedCount} items selected`;
     * };
     * ```
     * */
    getMultiSelectText: GetMultiSelectTextType;
  };
};

type SelectFieldPropsMultiWithoutSelectAll = {
  hasSelectAll?: false;
  localeText: {
    /**
     * A function to format the text displayed when multiple items are selected. This should be `x item's selected` in the correct locale.
     * ex: const formatMessage = ({selectedCount}: {selectedCount: number}) => {
        return `${selectedCount} items selected`;
      };
     * */
    getMultiSelectText: GetMultiSelectTextType;
  };
};

type SelectFieldMultiProps = {
  multiple: true;
} & (SelectFieldPropsMultiWithSelectAll | SelectFieldPropsMultiWithoutSelectAll);

type SelectFieldProps<Value> = (Value extends Array<unknown>
  ? SelectFieldMultiProps
  : SelectFieldSingleProps) &
  SelectFieldCommonProps<Value>;

function ClearAdornment({
  onClear,
  disabled,
  error,
}: {
  onClear: React.MouseEventHandler<HTMLButtonElement>;
  disabled?: boolean;
  error?: boolean;
}) {
  return (
    <IconButton
      size="small"
      onClick={onClear}
      aria-label="clear-select-input"
      disabled={disabled}
      color={error ? 'error' : 'primary'}
    >
      <SvgIcon type="cross-circled" fontSize="h5" />
    </IconButton>
  );
}

/**
 * A hook to help with the renderValue and displayEmpty props of the SelectField component
 * @param placeholder - The placeholder text to display when no value is selected
 * @returns A function to handle the renderValue prop and a function to display a placeholder MenuItem
 */

function useRenderValueHelper<Value>(placeholder?: string): {
  handleRenderValue: (
    selected: Value,
    options: SelectOptions<Value>,
    getMultiSelectText?: GetMultiSelectTextType
  ) => JSX.Element | string | number;
  makePlaceholderMenuItem: ({required}: {required: boolean}) => false | JSX.Element;
} {
  const defaultPlaceholder = 'Select a value';

  /**
   *
   * @param selected -  The value of the selected MenuItem
   * @returns A Typography component with the placeholder text if the selected value is null or an empty string or the currently selected value
   */
  const handleRenderValue = (
    selected: Value,
    options: SelectOptions<Value>,
    getMultiSelectText?: GetMultiSelectTextType
  ): JSX.Element | string | number => {
    if (isNil(selected) || selected === '' || isEmptyArray(selected)) {
      return <Typography color="text.placeholder">{placeholder ?? defaultPlaceholder}</Typography>;
    }

    if (Array.isArray(selected)) {
      return selected.length > 1
        ? getMultiSelectText?.({selectedCount: selected.length}) ??
            `${selected.length} ${MultiSelectText.ITEMS_SELECTED}`
        : selected.join('');
    }

    return options?.find(option => option.value === selected)?.label ?? '';
  };

  /**
   *
   * @param required - If the field is required we will not display a MenuItem to prevent the user from selecting an empty value
   * @returns A MenuItem with a placeholder if the field is not required
   */
  const makePlaceholderMenuItem = ({required}: {required: boolean}) => {
    return (
      !required && (
        <MenuItem value="">
          <Typography color="text.placeholder">{placeholder ?? defaultPlaceholder}</Typography>
        </MenuItem>
      )
    );
  };

  return {handleRenderValue, makePlaceholderMenuItem};
}

function SelectDeselectAllButtons<Value>({
  options,
  localeText,
  handleOnClick,
}: {
  options: SelectOptions<Value>;
  localeText: {
    selectAllLabel: string;
    deselectAllLabel: string;
  };
  handleOnClick: (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>['target'],
    value: unknown
  ) => void;
}) {
  const {selectAllLabel, deselectAllLabel} = localeText ?? {
    selectAllLabel: SelectAllText.SELECT_ALL,
    deselectAllLabel: SelectAllText.DESELECT_ALL,
  };
  return (
    <>
      <ListItemButton
        sx={{
          display: 'inline-block',
        }}
        color="secondary"
        onClick={e => {
          e.stopPropagation();
          const selected = options?.filter(option => !option.disabled).map(option => option.value);
          handleOnClick(e.target, selected);
        }}
      >
        <Typography variant="h5" component="span">
          {selectAllLabel}
        </Typography>
      </ListItemButton>
      <ListItemButton
        sx={{
          display: 'inline-block',
        }}
        onClick={e => {
          e.stopPropagation();
          handleOnClick(e.target, []);
        }}
        color="secondary"
      >
        <Typography variant="h5" component="span">
          {deselectAllLabel}
        </Typography>
      </ListItemButton>
      <Divider />
    </>
  );
}

function SelectField<Value>(props: SelectFieldProps<Value>): JSX.Element {
  const {
    placeholder,
    options = [],
    required = false,
    onChange,
    value,
    multiple,
    hasSelectAll,
    hasClear,
    localeText,
    name,
    renderValue: suppliedRenderValue,
    sx,
    ...rest
  } = props;

  const muiFormControl = useFormControl();
  const {error, disabled} = muiFormControl || {
    error: props.error,
    disabled: props.disabled,
  };

  const {handleRenderValue, makePlaceholderMenuItem} = useRenderValueHelper<Value>(placeholder);

  const optionIsSelected = (option: Option<Value>) =>
    !option.disabled && isNonEmptyArray(value) && value.includes(option.value);

  const valueIsEmptyArray = Array.isArray(value) && !value.length;
  const valueIsNull = !Array.isArray(value) && isNil(value);

  const [expanded, setExpanded] = useState(false);

  const simulateChangeEvent = useCallback(
    (target: React.MouseEvent<HTMLDivElement, MouseEvent>['target'], eventValue: unknown) => {
      const changeEvent = new Event('change', {bubbles: false});
      const simulatedChangeEvent = {
        ...changeEvent,
        target: {...target, name, value: eventValue},
      } as SelectChangeEvent<Value>;
      onChange(simulatedChangeEvent);
    },
    [name, onChange]
  );

  return (
    <Select<Value>
      displayEmpty
      inputProps={{placeholder: placeholder ?? 'Select a value', required, ...props.inputProps}}
      endAdornment={
        <InputAdornment position="end" disablePointerEvents={props.readOnly}>
          <Stack direction="row" gap={1}>
            {hasClear && !(valueIsNull || valueIsEmptyArray) && (
              // Note: We are casting to Value in this instance because the type of value is inferred never
              <ClearAdornment
                disabled={isEmptyArray(value) || value === ''}
                onClear={e => {
                  simulateChangeEvent(e.target, isNonEmptyArray(value) ? [] : '');
                }}
                error={error}
              />
            )}
            <IconButton
              aria-label="toggle dropdown"
              disabled={disabled}
              onClick={() => setExpanded(true)}
              size="small"
              color="primary"
            >
              <SvgIcon type={expanded ? 'chevron-up' : 'chevron-down'} />
            </IconButton>
          </Stack>
        </InputAdornment>
      }
      multiple={multiple}
      open={expanded}
      onOpen={() => setExpanded(true)}
      onClose={() => setExpanded(false)}
      value={value}
      onChange={onChange}
      renderValue={
        typeof suppliedRenderValue === 'function'
          ? suppliedRenderValue
          : renderVal => handleRenderValue(renderVal, options, localeText?.getMultiSelectText)
      }
      disabled={disabled}
      sx={theme => ({
        [`&.${inputBaseClasses.root}`]: {
          [`.${selectClasses.icon}`]: {
            display: 'none',
          },
          [`.${iconButtonClasses.root}`]: {
            padding: 0,
          },
          [`.${iconButtonClasses.root}:hover`]: {
            backgroundColor: 'transparent',
          },
          [`& .${inputAdornmentClasses.positionEnd}`]: {
            paddingRight: 3,
          },
          [`&.${filledInputClasses.root}`]: {
            padding: 0,
            paddingRight: 3,
            [`.${selectClasses.filled}`]: {
              padding: `${theme.spacing(3.5)} ${theme.spacing(3)}`,
            },
            [`&.${inputBaseClasses.adornedEnd}`]: {
              paddingRight: theme.spacing(3),
              [`& .${inputAdornmentClasses.positionEnd}`]: {
                paddingRight: 0,
              },
            },
          },
        },
        ...sx,
      })}
      {...rest}
    >
      {hasSelectAll && (
        <SelectDeselectAllButtons
          options={options}
          handleOnClick={simulateChangeEvent}
          localeText={{
            selectAllLabel: localeText.selectAll,
            deselectAllLabel: localeText.deselectAll,
          }}
        />
      )}

      {!multiple && makePlaceholderMenuItem({required})}

      {options.map(option => (
        <MenuItem key={`key-${option.value}`} value={option.value} disabled={option.disabled}>
          {multiple && (
            <Box component="span" pl={1}>
              <Checkbox checked={optionIsSelected(option)} edge="start" />
            </Box>
          )}
          {option.label}
        </MenuItem>
      ))}
    </Select>
  );
}

SelectField.displayName = 'SelectField';

export {
  SelectField,
  type SelectFieldProps,
  type GetMultiSelectTextType,
  SelectDeselectAllButtons as SelectFieldSelectDeselectAllButtons,
  ClearAdornment as SelectFieldClearAdornment,
  useRenderValueHelper as useSelectFieldRenderValueHelper,
};
