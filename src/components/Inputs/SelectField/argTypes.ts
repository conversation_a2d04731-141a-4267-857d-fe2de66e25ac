import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

import {SELECT_ARGTYPES} from 'src/components/Inputs/Select/argTypes';

const selectFieldArgTypeKeys = [
  'autoWidth',
  'autoFocus',
  'color',
  'defaultOpen',
  'defaultValue',
  'disabled',
  'error',
  'fullWidth',
  'focused',
  'IconComponent',
  'id',
  'input',
  'inputProps',
  'inputRef',
  'label',
  'labelId',
  'MenuProps',
  'multiple',
  'name',
  'onChange',
  'onClose',
  'onOpen',
  'open',
  'placeholder',
  'readOnly',
  'required',
  'SelectDisplayProps',
  'variant',
  'classes',
  'sx',
];

const SELECTFIELD_ARGTYPES = {
  onChange: {
    table: {
      type: {summary: 'func'},
    },
    description:
      'Callback fired when the value is changed. `function(event: SelectChangeEvent<Value>) => void`. You can pull out the new value by accessing `event.target.value`.',
  },
  placeholder: {
    table: {
      type: {summary: 'string'},
      defaultValue: {summary: 'Select a value'},
    },
    control: 'text',
    description:
      'The short hint displayed in the input before the user enters a value. When `required` is false the placeholder will appear in the list of options to clear the selection.',
  },
  hasClear: {
    table: {
      type: {summary: 'boolean'},
    },
    control: 'boolean',
    description:
      'If present, a clear button (x) will be displayed in the selectField input and clear the selected items on click.',
  },
  hasSelectAll: {
    table: {
      type: {summary: 'boolean'},
    },
    control: 'boolean',
    description:
      'If present, a select all button and a deselect all button will appear in the menu list and select/deselect all options on respective clicks.',
  },
  localeText: {
    table: {
      type: {
        summary: `{
          selectAll: 'string',
          deselectAll: 'string',
          getMultiSelectText: 'function',
        }`,
      },
    },
    control: 'object',
    description:
      'Object to support localized text for: select/deselect all buttons and a function that should return a string that reads `x {items} {selected}` formatted for i18n by the consuming application.',
  },
};

const argTypes = getArgTypes(selectFieldArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...SELECT_ARGTYPES,
  ...SELECTFIELD_ARGTYPES,
});

export default argTypes;
