import type {<PERSON><PERSON>, <PERSON>Obj} from '@storybook/react';
import React from 'react';
import {
  Box,
  Button,
  Chip,
  DialogActions,
  DialogContent,
  FormControl,
  FormHelperText,
  FormLabel,
  Label,
  SelectField,
  SimpleDialog,
  Stack,
  SvgIcon,
  Tooltip,
  Typography,
  type SelectFieldProps,
} from 'src/index';
import type {SelectChangeEvent} from 'src/index';

import argTypes from 'src/components/Inputs/SelectField/argTypes';

const formatMessageExample = ({selectedCount}: {selectedCount: number}) => {
  return `${selectedCount} articles sélectionnés`;
};

const formatMessageExampleEnglish = ({selectedCount}: {selectedCount: number}) => {
  return `${selectedCount} items selected`;
};

const SingleExample = (args: SelectFieldProps<string>) => {
  const [value, setValue] = React.useState<string>(args.defaultValue ?? '');
  const handleChange = (event: SelectChangeEvent<string>) => {
    setValue(event.target.value);
  };
  return <SelectField {...args} value={value} required={true} onChange={handleChange} />;
};

SingleExample.displayName = 'SingleExample';

const MultiExampleSelectAll = (args: SelectFieldProps<string[]>) => {
  const {renderValue, ...rest} = args;
  const [value, setValue] = React.useState<string[]>(args.defaultValue ?? []);
  const handleChange = (event: SelectChangeEvent<string[]>) => {
    setValue(event.target.value);
  };
  return (
    <SelectField
      {...rest}
      value={value}
      onChange={handleChange}
      // Make empty value `undefined` in order to retain original placeholder and styling
      renderValue={value.length === 0 ? undefined : renderValue}
    />
  );
};

MultiExampleSelectAll.displayName = 'MultiExampleSelectAll';

type Story<T> = StoryObj<SelectFieldProps<T>>;

const meta: Meta<typeof SelectField> = {
  component: SelectField,
  title: 'components/Inputs/SelectField',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=11592-46685&mode=design&t=yMKIJzz0ic7d6oOg-0',
    },
  },
};

export default meta;

export const Basic: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'Select a year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <FormControl error={args.error}>
        <FormLabel>SelectField</FormLabel>
        <Box my={2}>
          <SingleExample {...args} />
        </Box>
        <FormHelperText>Helper text</FormHelperText>
      </FormControl>
    );
  },
};

export const Single: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'Select a year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <>
        <Box mb={4} width={theme => theme.fixedWidths.md}>
          <FormControl>
            <FormLabel>SelectField</FormLabel>
            <Box my={2}>
              <SingleExample {...args} />
            </Box>
          </FormControl>
        </Box>
        <Box mb={4}>
          <FormControl>
            <FormLabel>
              SelectField w/ <code>hasClear</code>
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} hasClear />
            </Box>
          </FormControl>
        </Box>
      </>
    );
  },
};

export const Multi: Story<string[]> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'sélectionner une année',
    multiple: true,
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2024', value: '2024'},
      {label: '2025 - ex disabled', value: '2025', disabled: true},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
    ],
    defaultValue: [],
  },
  render: (args: SelectFieldProps<string[]>) => {
    const {placeholder, ...rest} = args;
    return (
      <>
        <Box mb={4}>
          <FormControl>
            <FormLabel>SelectField</FormLabel>
            <Box my={2}>
              <MultiExampleSelectAll {...rest} />
            </Box>
          </FormControl>
        </Box>
        <Box mb={4}>
          <FormControl>
            <FormLabel>
              SelectField: <code>hasClear</code>
            </FormLabel>
            <Box my={2}>
              <MultiExampleSelectAll {...rest} hasClear />
            </Box>
          </FormControl>
        </Box>
        <Box mb={4}>
          <FormControl>
            <FormLabel>
              SelectField: <code>hasSelectAll</code> & <code>hasClear</code>
            </FormLabel>
            <Box my={2}>
              <MultiExampleSelectAll
                {...rest}
                hasClear
                hasSelectAll
                localeText={{
                  selectAll: 'Select All',
                  deselectAll: 'Deselect All',
                  getMultiSelectText: formatMessageExampleEnglish,
                }}
              />
            </Box>
          </FormControl>
        </Box>
        <Box width={theme => theme.fixedWidths.lg}>
          <FormControl>
            <FormLabel>
              SelectField with <code>hasClear</code>, <code>hasSelectAll</code>,
              <code>localeText</code>
              set with <code>selectAll</code>,<code>deselectAll</code> and{' '}
              <code>getMultiSelectText</code> for translations.
            </FormLabel>
            <Box my={2}>
              <MultiExampleSelectAll
                {...rest}
                placeholder={placeholder}
                hasClear
                hasSelectAll
                localeText={{
                  selectAll: 'tout sélectionner',
                  deselectAll: 'tout désélectionner',
                  getMultiSelectText: formatMessageExample,
                }}
              />
            </Box>
          </FormControl>
        </Box>
      </>
    );
  },
};

const multiWithChipOptions = [
  {label: <Chip label="2021" />, value: '2021'},
  {label: <Chip label="2022 - ex disabled" />, value: '2022', disabled: true},
  {label: <Chip label="2023" />, value: '2023'},
  {label: <Chip label="2024" />, value: '2024'},
  {label: <Chip label="2025 - ex disabled" />, value: '2025', disabled: true},
  {label: <Chip label="2026" />, value: '2026'},
  {label: <Chip label="2027" />, value: '2027'},
  {label: <Chip label="2028" />, value: '2028'},
  {label: <Chip label="2029" />, value: '2029'},
  {label: <Chip label="2030" />, value: '2030'},
];

export const MultiWithChips: Story<string[]> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'sélectionner une année',
    multiple: true,
    options: multiWithChipOptions,
    defaultValue: [],
  },
  render: (args: SelectFieldProps<string[]>) => {
    const {placeholder, ...rest} = args;
    return (
      <>
        <Box mb={4}>
          <Typography variant="h6">
            Multiple Select with Chips, but default renderValue used
          </Typography>
          <MultiExampleSelectAll {...rest} />
        </Box>
        <Box mb={4}>
          <Typography variant="h6">
            Multiple Select with Chips, and Chips are present in the renderValue
          </Typography>
          <MultiExampleSelectAll
            {...rest}
            renderValue={selected => (
              <Box display="flex" flexWrap="wrap" gap={2}>
                {selected.map(optionValue => {
                  return (
                    multiWithChipOptions.find(option => option.value === optionValue)?.label || null
                  );
                })}
              </Box>
            )}
          />
        </Box>
        <Box mb={4}>
          <Typography variant="h6">Multiple Select with `hasClear`</Typography>
          <MultiExampleSelectAll {...rest} hasClear />
        </Box>
        <Box mb={4}>
          <Typography variant="h6">Multiple Select with `hasSelectAll` and `hasClear`</Typography>
          <MultiExampleSelectAll
            {...rest}
            hasClear
            hasSelectAll
            localeText={{
              selectAll: 'Select All',
              deselectAll: 'Deselect All',
              getMultiSelectText: formatMessageExampleEnglish,
            }}
          />
        </Box>
        <Box>
          <Typography variant="h6">
            Multiple Select with `hasClear`, `hasSelectAll`, `localeText` set with `selectAll`
            `deselectAll` and `getMultiSelectText` for translations.
          </Typography>
          <MultiExampleSelectAll
            {...rest}
            placeholder={placeholder}
            hasClear
            hasSelectAll
            localeText={{
              selectAll: 'tout sélectionner',
              deselectAll: 'tout désélectionner',
              getMultiSelectText: formatMessageExample,
            }}
          />
        </Box>
      </>
    );
  },
};

export const Required: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    label: 'Year',
    required: true,
    options: [
      {label: '2021', value: '2021'},
      {label: '2022', value: '2022'},
      {label: '2023', value: '2023'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return <SingleExample {...args} />;
  },
};

export const RequiredWithPlaceholder: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    label: 'Year',
    required: true,
    placeholder: 'User placeholder',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022', value: '2022'},
      {label: '2023', value: '2023'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <FormControl>
        <FormLabel>
          SelectField w/ placeholder & <code>required</code>
        </FormLabel>
        <Box my={2}>
          <SingleExample {...args} />
        </Box>
      </FormControl>
    );
  },
};

export const States: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    label: 'Year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022', value: '2022'},
      {label: '2023', value: '2023'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <Stack spacing={4} width={theme => theme.fixedWidths.md}>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-1">
              <Label title="SelectField Standard" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} />
            </Box>
            <FormHelperText>Standard select field</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-2">
              <Label title="SelectField Default Value" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} defaultValue={'2022'} />
            </Box>
            <FormHelperText>Default value</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-3">
              <Label title="SelectField Placeholder" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} />
            </Box>
            <FormHelperText>SelectField with a placeholder value</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl disabled>
            <FormLabel htmlFor="component-selectField-4">
              <Label title="SelectField Disabled" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} />
            </Box>
            <FormHelperText>Disabled SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box width={theme => theme.fixedWidths.sm}>
          <FormControl required fullWidth>
            <FormLabel htmlFor="component-selectField-5">
              <Label
                title="SelectField Required"
                description="Example of a SelectField in a required state"
              />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} required />
            </Box>
            <FormHelperText>Required SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-6">
              <Label title="SelectField ReadOnly" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} readOnly />
            </Box>
            <FormHelperText>ReadOnly SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl focused>
            <FormLabel htmlFor="component-selectField-7">
              <Label title="SelectField Focused" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} hasClear />
            </Box>
            <FormHelperText>Focused SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl error>
            <FormLabel htmlFor="component-selectField-8">
              <Label title="SelectField Error" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} />
            </Box>
            <FormHelperText>SelectField with an error</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl error>
            <FormLabel htmlFor="component-selectField-8">
              <Label title="SelectField Error" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} hasClear error />
            </Box>
            <FormHelperText>SelectField with an error and hasClear</FormHelperText>
          </FormControl>
        </Box>
      </Stack>
    );
  },
};

export const Filled: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    label: 'Year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022', value: '2022'},
      {label: '2023', value: '2023'},
    ],
    variant: 'filled',
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <Stack spacing={4} width={theme => theme.fixedWidths.md}>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-1">
              <Label title="SelectField Filled" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} />
            </Box>
            <FormHelperText>Standard select field</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-2">
              <Label title="SelectField Default Value" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} defaultValue={'2022'} />
            </Box>
            <FormHelperText>Default value</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-3">
              <Label title="SelectField Placeholder" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} />
            </Box>
            <FormHelperText>SelectField with a placeholder value</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl disabled>
            <FormLabel htmlFor="component-selectField-4">
              <Label title="SelectField Disabled" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} />
            </Box>
            <FormHelperText>Disabled SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box width={theme => theme.fixedWidths.sm}>
          <FormControl required fullWidth>
            <FormLabel htmlFor="component-selectField-5">
              <Label title="SelectField Required" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} />
            </Box>
            <FormHelperText>Required SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl>
            <FormLabel htmlFor="component-selectField-6">
              <Label title="SelectField ReadOnly" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} readOnly />
            </Box>
            <FormHelperText>ReadOnly SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl focused>
            <FormLabel htmlFor="component-selectField-7">
              <Label title="SelectField Focused" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} hasClear />
            </Box>
            <FormHelperText>Focused SelectField</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl error>
            <FormLabel htmlFor="component-selectField-8">
              <Label title="SelectField Error" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} />
            </Box>
            <FormHelperText>SelectField with an error</FormHelperText>
          </FormControl>
        </Box>
        <Box>
          <FormControl error>
            <FormLabel htmlFor="component-selectField-8">
              <Label title="SelectField Error" />
            </FormLabel>
            <Box my={2}>
              <SingleExample {...args} placeholder={'Select a year'} hasClear />
            </Box>
            <FormHelperText>SelectField with an error and hasClear</FormHelperText>
          </FormControl>
        </Box>
      </Stack>
    );
  },
};

export const Dialog: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'Select a year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022', value: '2022'},
      {label: '2023', value: '2023'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <SimpleDialog maxWidth="sm" open={true} onClose={() => {}} title="Select a year">
        <DialogContent>
          <Box pt={2}>
            <FormControl>
              <FormLabel>Select a year</FormLabel>
              <Box my={2}>
                <SingleExample {...args} />
              </Box>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Box pb={6} pr={6}>
            <Button autoFocus>Save changes</Button>
          </Box>
        </DialogActions>
      </SimpleDialog>
    );
  },
};

interface UnavailableOptionLabelProps {
  label: string;
  message: string;
}

const UnavailableOptionLabel: React.FC<UnavailableOptionLabelProps> = ({label, message}) => (
  <Stack width="100%" direction="row" gap={2} alignItems="center">
    <Box>{label}</Box>
    <Tooltip title={message} disableInteractive placement="right">
      <span style={{pointerEvents: 'auto'}}>
        <SvgIcon fontSize="body1" type="warning-triangled" />
      </span>
    </Tooltip>
  </Stack>
);

export const WithJSX: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'Select a year',
    options: [
      {label: '2021', value: '2021'},
      {
        label: (
          <UnavailableOptionLabel
            label="2022"
            message="This option utilizes JSX to show a tooltip"
          />
        ),
        value: '2022',
        disabled: true,
      },
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <>
        <Box mb={4}>
          <Typography variant="body2" color="secondary">
            JSX can be used with SelectField option labels
          </Typography>
        </Box>
        <Stack width={theme => theme.fixedWidths.xs} spacing={4} mt={10}>
          <SelectField {...args} />
        </Stack>
      </>
    );
  },
};

export const WithTooltip: Story<string> = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'Select a year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
  },
  render: (args: SelectFieldProps<string>) => {
    return (
      <>
        <Box mb={4}>
          <Typography variant="body2" color="secondary">
            For SelectField implementations to show a tooltip, it must be wrapped in another MUI
            component or div.
          </Typography>
        </Box>
        <Stack width={theme => theme.fixedWidths.xs} spacing={4} mt={10}>
          <Tooltip title="SelectField must be wrapped to show a tooltip">
            <div>
              <SelectField {...args} />
            </div>
          </Tooltip>
        </Stack>
      </>
    );
  },
};
