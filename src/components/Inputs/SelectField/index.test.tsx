import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import {SelectField} from './index';

const mockOptions = [
  {label: 'The Shawshank Redemption', value: 'shawshank'},
  {label: 'The Godfather', value: 'godfather'},
  {label: 'The Dark Knight', value: 'dark-knight'},
];

const mockSingleValue = 'shawshank';

const emptyMockValue: string[] = [];

describe('SelectField', () => {
  it('should render the the list of movies', () => {
    render(<SelectField onChange={() => {}} options={mockOptions} value={mockSingleValue} />);

    expect(screen.getByRole('button', {name: 'The Shawshank Redemption'})).toBeInTheDocument();
  });

  it('should render the placeholder when no value is selected', () => {
    render(
      <SelectField onChange={() => {}} options={mockOptions} value={''} placeholder="Placeholder" />
    );

    expect(screen.getByRole('button', {name: 'Placeholder'})).toBeInTheDocument();
  });

  it('should handle an undefined list of options', async () => {
    render(
      <SelectField onChange={() => {}} options={undefined} value={''} placeholder="Placeholder" />
    );
    expect(screen.getByRole('button', {name: 'Placeholder'})).toBeInTheDocument();
    userEvent.click(screen.getByRole('button', {name: 'Placeholder'}));
    await waitFor(() =>
      expect(screen.getByRole('option', {name: 'Placeholder'})).toBeInTheDocument()
    );
    expect(screen.getAllByRole('option')).toHaveLength(1);
  });

  it('should set the required input attribute', () => {
    render(
      <SelectField
        onChange={() => {}}
        options={mockOptions}
        value={''}
        required
        placeholder="Placeholder"
      />
    );

    expect(screen.getByPlaceholderText('Placeholder')).toHaveAttribute('required');
  });

  it('should render the placeholder in the list of options', async () => {
    render(
      <SelectField onChange={() => {}} options={mockOptions} value={''} placeholder="Placeholder" />
    );

    expect(screen.getByRole('button', {name: 'Placeholder'})).toBeInTheDocument();

    userEvent.click(screen.getByRole('button', {name: 'Placeholder'}));

    await waitFor(() => {
      screen.getByText('The Godfather');
    });

    expect(screen.getByRole('option', {name: 'Placeholder'})).toBeInTheDocument();
  });

  it('should clear the selected value when the clear button is clicked', async () => {
    const mockValue = 'shawshank';
    const onChangeHandler = jest.fn();
    render(
      <SelectField
        onChange={onChangeHandler}
        options={mockOptions}
        value={mockValue}
        placeholder="Select a value"
        hasClear
      />
    );

    await waitFor(() => {
      screen.getByLabelText('clear-select-input');
    });

    screen.getByLabelText('clear-select-input').click();

    expect(onChangeHandler).toHaveBeenCalledTimes(1);
    expect(onChangeHandler).toHaveBeenCalledWith(
      expect.objectContaining({target: expect.objectContaining({value: ''})})
    );
  });

  it('should render the number of selected items when the value is an array', async () => {
    render(
      <SelectField
        multiple={true}
        onChange={() => {}}
        options={mockOptions}
        value={['shawshank']}
        placeholder="Select a value"
        localeText={{
          selectAll: 'Select All',
          deselectAll: 'Deselect All',
          getMultiSelectText: ({selectedCount}) => `${selectedCount} item selected`,
        }}
      />
    );

    userEvent.click(screen.getByRole('button', {name: 'shawshank'}));

    await waitFor(() => {
      screen.getByRole('option', {name: 'The Shawshank Redemption'});
    });

    expect(screen.getByRole('option', {name: 'The Shawshank Redemption'})).toBeInTheDocument();
  });

  it('should render the number of selected items when the value is an array', async () => {
    render(
      <SelectField
        multiple={true}
        onChange={() => {}}
        options={mockOptions}
        value={['shawshank', 'godfather']}
        placeholder="Select a value"
        localeText={{
          selectAll: 'Select All',
          deselectAll: 'Deselect All',
          getMultiSelectText: ({selectedCount}) => `${selectedCount} items selected`,
        }}
      />
    );

    expect(screen.getByRole('button', {name: '2 items selected'})).toBeInTheDocument();
  });

  it('should render the list of movies as checkboxes when multiple is true', async () => {
    render(
      <SelectField
        multiple={true}
        onChange={() => {}}
        options={mockOptions}
        value={emptyMockValue}
        placeholder="Select a value"
        localeText={{
          selectAll: 'Select All',
          deselectAll: 'Deselect All',
          getMultiSelectText: () => 'Selected {0} of {1}',
        }}
      />
    );

    userEvent.click(screen.getByRole('button', {name: 'Select a value'}));

    await waitFor(() => {
      screen.getByRole('option', {name: 'The Shawshank Redemption'});
    });

    expect(screen.getByRole('option', {name: 'The Shawshank Redemption'})).toBeInTheDocument();

    expect(screen.getAllByRole('checkbox')).toHaveLength(3);
  });

  it('should render the list of movies and select all when hasSelectAll prop is true', async () => {
    render(
      <SelectField
        multiple={true}
        onChange={() => {}}
        options={mockOptions}
        value={emptyMockValue}
        placeholder="Select a value"
        hasSelectAll
        localeText={{
          selectAll: 'Select All',
          deselectAll: 'Deselect All',
          getMultiSelectText: () => 'Selected {0} of {1}',
        }}
      />
    );

    userEvent.click(screen.getByRole('button', {name: 'Select a value'}));

    await waitFor(() => {
      screen.getByRole('button', {name: 'Select All'});
    });

    expect(screen.getByRole('button', {name: 'Select All'})).toBeInTheDocument();
  });

  it('should select all when selectAll is clicked', async () => {
    const mockValue: string[] = [];
    const onChangeHandler = jest.fn();
    render(
      <SelectField
        multiple={true}
        onChange={onChangeHandler}
        options={mockOptions}
        value={mockValue}
        placeholder="Select a value"
        hasSelectAll
        localeText={{
          selectAll: 'Select All',
          deselectAll: 'Deselect All',
          getMultiSelectText: selected => `Selected ${selected} of ${mockOptions.length}`,
        }}
      />
    );

    userEvent.click(screen.getByRole('button', {name: 'Select a value'}));

    await waitFor(() => {
      screen.getByRole('button', {name: 'Select All'});
    });

    fireEvent.click(screen.getByRole('button', {name: 'Select All'}));

    expect(onChangeHandler).toHaveBeenCalledTimes(1);
    expect(onChangeHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({value: ['shawshank', 'godfather', 'dark-knight']}),
      })
    );
  });

  it('should deselect all when deselectAll is clicked', async () => {
    const mockValue: string[] = ['shawshank', 'godfather', 'dark-knight'];
    const onChangeHandler = jest.fn();
    render(
      <SelectField
        multiple={true}
        onChange={onChangeHandler}
        options={mockOptions}
        value={mockValue}
        placeholder="Select a value"
        hasSelectAll
        localeText={{
          selectAll: 'Select All',
          deselectAll: 'Deselect All',
          getMultiSelectText: ({selectedCount}) =>
            `Selected ${selectedCount} of ${mockOptions.length}`,
        }}
      />
    );

    userEvent.click(screen.getByRole('button', {name: 'Selected 3 of 3'}));

    await waitFor(() => {
      screen.getByRole('button', {name: 'Deselect All'});
    });

    fireEvent.click(screen.getByRole('button', {name: 'Deselect All'}));

    expect(onChangeHandler).toHaveBeenCalledTimes(1);
    expect(onChangeHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({value: []}),
      })
    );
  });

  it('should disable the select and dropdown arrow', () => {
    const changeFn = jest.fn();
    render(
      <SelectField
        disabled
        onChange={changeFn}
        options={mockOptions}
        value={''}
        placeholder="Select a value"
      />
    );

    const select = screen.getByRole('button', {name: 'Select a value'});
    expect(select).toHaveAttribute('aria-disabled', 'true');
    userEvent.click(select);
    expect(changeFn).not.toHaveBeenCalled();

    const dropdownArrow = screen.getByLabelText('toggle dropdown');
    expect(dropdownArrow).toBeDisabled();
    userEvent.click(dropdownArrow);
    expect(changeFn).not.toHaveBeenCalled();
  });
});
