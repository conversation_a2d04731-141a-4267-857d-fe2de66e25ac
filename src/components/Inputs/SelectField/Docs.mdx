import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as SelectFieldStories from './index.stories.tsx'

<Meta of={SelectFieldStories}/>

## Overview
`SelectField` is an abstraction of `mui`'s `Select` component creating a simplified API for our most common use cases.

`SelectField` is an uncontrolled component that is composed using:
- `useRenderValueHelper` that exposes a 'placeholder' `MenuItem` component and a `renderValue` method to generate the SelectField's display text from selected values
- `SelectFieldClearAdornment` to render a 'clear' button
- `SelectFieldSelectDeselectAllButtons` to render 'Select All' and 'Deselect All' buttons

These helpers can be used to compose your own custom Select when needed.

`SelectField` requires:
- an `options` Array of objects with keys `label` & `value` to display as select options
- an `onChange` handler that accepts the selected values
- a `value` prop to define the selected value

When `multiple` is `true` more props become required that support `SelectField` multiple variant:

The below props support i18n.
- when `multiple` is `true` `localeText.getMultiSelectText` is required to display the number of selected items within the selectField input
- when `multiple` is `true` and `hasSelectAll`  is `true` `localeText.selectAll` and `localeText.deselectAll` are required

<Canvas of={SelectFieldStories.Basic} />
<Controls of={SelectFieldStories.Basic}/>

### Required
Demo of `SelectField` with the `required` prop set to `true` which removes the ability to select an empty value.
<Canvas of={SelectFieldStories.Required} />

### Required & Placeholder
Demo of `SelectField` with the `required` prop set to `true` and a `placeholder` value set which removes the ability to select an empty value but displays the users placeholder text untill a value is selected.
<Canvas of={SelectFieldStories.RequiredWithPlaceholder} />

### Multiple Select
Demo of `SelectField` with the `multiple` prop set to `true` which allows for multiple values to be selected.
<Canvas of={SelectFieldStories.Multi} />

### States
<Canvas of={SelectFieldStories.States} />

### Filled
<Canvas of={SelectFieldStories.Filled} />

### Usages
<Canvas of={SelectFieldStories.WithTooltip} />
<Canvas of={SelectFieldStories.WithJSX} />