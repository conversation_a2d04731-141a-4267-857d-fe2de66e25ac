import {Typography} from 'src/components/Typography';

type LabelProps = {
  title: string;
  description?: string;
};

const Label = ({title, description}: LabelProps) => {
  return (
    <>
      <Typography variant="body1" component={description ? 'div' : 'span'}>
        {title}
      </Typography>
      {description && (
        <Typography variant="body2" color="secondary" component="span">
          {description}
        </Typography>
      )}
    </>
  );
};

export {Label, type LabelProps};
