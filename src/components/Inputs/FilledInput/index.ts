import type {Components, FilledInputProps, Theme} from '@mui/material';
import {
  FilledInput,
  filledInputClasses,
  inputAdornmentClasses,
  inputBaseClasses,
} from '@mui/material';

// Note: This component has augmented props via InputBase. See src/muiTheme.d.ts for prop overrides
export const FilledInputOverrides: Components<Theme>['MuiFilledInput'] = {
  defaultProps: {
    fullWidth: true,
    disableUnderline: true,
    color: undefined,
  },
  styleOverrides: {
    root: ({ownerState: {color, disabled}, theme}) => ({
      [`&.${filledInputClasses.root}, &.${filledInputClasses.root}:hover, &.${filledInputClasses.root}.${filledInputClasses.disabled}`]:
        {
          transition: 'none',
          background: disabled
            ? theme.palette.grey[200]
            : color
            ? theme.palette.semanticPalette.surface[color]
            : 'none',
          borderRadius: 0,
          borderWidth: '2px',
          borderStyle: 'solid',
          borderColor: 'transparent',
          padding: `${theme.spacing(3.5)} ${theme.spacing(3)}`,
          [`&.${filledInputClasses.multiline}`]: {
            padding: `${theme.spacing(3.5)} ${theme.spacing(2.5)}`,
          },
          [`&.${inputBaseClasses.adornedStart}`]: {
            paddingLeft: theme.spacing(3),
            [`& .${inputAdornmentClasses.positionStart}`]: {
              paddingLeft: 0,
            },
          },
          [`&.${inputBaseClasses.adornedEnd}`]: {
            paddingRight: theme.spacing(3),
            [`& .${inputAdornmentClasses.positionEnd}`]: {
              paddingRight: 0,
            },
          },
          [`& .${filledInputClasses.input}`]: {
            padding: 0,
          },
          ':before, :after': {
            border: 'none',
            transition: 'none',
          },
        },
      [`&.${filledInputClasses.root}.${filledInputClasses.focused}`]: {
        borderColor: color
          ? theme.palette.semanticPalette.stroke[color]
          : theme.palette.semanticPalette.stroke.brand,
      },
      [`& .${inputBaseClasses.inputAdornedEnd}`]: {
        textOverflow: 'ellipsis',
      },
    }),
    error: ({theme}) => ({
      [`&.${filledInputClasses.error}, &.${filledInputClasses.error}:hover`]: {
        background: theme.palette.semanticPalette.surface.error,
      },
      [`&.${filledInputClasses.error}.${filledInputClasses.focused}`]: {
        borderColor: theme.palette.semanticPalette.stroke.error,
      },
    }),
  },
};

export {FilledInput, type FilledInputProps};
