import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as FilledInputStories from './index.stories.tsx'

<Meta of={FilledInputStories} />

## Overview
`FilledInput` is intended to be used within `table` components only. <br /> 

For a pre-composed `FilledInput` please use `<TextField variant="filled" />`. <br />
This component should be used when a more custom solution is required than what `TextField` can provide. <br />
`FilledInput` should be composed with `FormControl` and `aria-label`. <br /> 


[MUI FilledInput](https://mui.com/material-ui/api/filled-input/)

<Canvas of={FilledInputStories.Basic} />
<Controls of={FilledInputStories.Basic}/>

### States
<Canvas of={FilledInputStories.States} />

### Colors
<Canvas of={FilledInputStories.Colors} />

### Usage in Table
<Canvas of={FilledInputStories.UsageInTable} />
