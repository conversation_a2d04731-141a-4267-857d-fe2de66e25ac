import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const filledInputArgTypes = [
  'autoComplete',
  'autoFocus',
  'classes',
  'color',
  'components',
  'componentsProps',
  'defaultValue',
  'disabled',
  'endAdornment',
  'error',
  'fullWidth',
  'hiddenLabel',
  'id',
  'inputComponent',
  'inputProps',
  'inputRef',
  'maxRows',
  'minRows',
  'multiline',
  'name',
  'onChange',
  'placeholder',
  'readOnly',
  'required',
  'rows',
  'slotProps',
  'slots',
  'startAdornment',
  'sx',
  'type',
  'value',
];

const argTypes = getArgTypes(filledInputArgTypes, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
});

export default argTypes;
