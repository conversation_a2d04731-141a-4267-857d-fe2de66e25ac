import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {
  Box,
  FilledInput,
  FormControl,
  InputAdornment,
  Stack,
  SvgIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from 'src/index';

import argTypes from './argTypes';

const meta: Meta<typeof FilledInput> = {
  component: FilledInput,
  title: 'components/Inputs/FilledInput',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=10150-7083&mode=design&t=hYrKeDviZBiwl8B4-0',
    },
  },
};

export default meta;

type Story = StoryObj<typeof FilledInput>;

export const Basic: Story = {
  render: props => (
    <>
      <Typography variant="body2" color="secondary">
        The <code>filled</code> variant is only intended to be used within table cells. <br />
        Since a label won't be used, be sure to include an <code>aria-label</code> to meet
        accessibility requirements.
      </Typography>
      <Box mt={2}>
        <FormControl>
          <FilledInput
            {...props}
            inputProps={{'aria-label': 'input-filled'}}
            defaultValue="filled input"
          />
        </FormControl>
      </Box>
    </>
  ),
};

export const States: Story = {
  render: () => (
    <>
      <Typography variant="body2" color="secondary">
        The <code>filled</code> variant accommodates <code>disabled</code>, <code>readOnly</code>,{' '}
        <code>focused</code> and <code>error</code> states.
      </Typography>
      <Box mt={2} width={theme => theme.fixedWidths.xs}>
        <Stack gap={2}>
          <FormControl variant="filled">
            <FilledInput
              inputProps={{'aria-label': 'component-filled'}}
              placeholder="placeholder text"
            />
          </FormControl>
          <FormControl variant="filled">
            <FilledInput
              inputProps={{'aria-label': 'component-filled'}}
              defaultValue="defaultValue"
            />
          </FormControl>
          <FormControl variant="filled" focused>
            <FilledInput inputProps={{'aria-label': 'component-filled'}} placeholder="focused" />
          </FormControl>
          <FormControl variant="filled" disabled>
            <FilledInput
              inputProps={{'aria-label': 'component-filled-disabled'}}
              value="disabled"
            />
          </FormControl>
          <FormControl variant="filled">
            <FilledInput
              inputProps={{'aria-label': 'component-filled-readOnly'}}
              readOnly
              value="readOnly"
            />
          </FormControl>
          <FormControl variant="filled" error>
            <FilledInput
              inputProps={{'aria-label': 'component-filled-error'}}
              placeholder="error"
            />
          </FormControl>
          <FormControl variant="filled" error focused>
            <FilledInput
              inputProps={{'aria-label': 'component-filled-error'}}
              placeholder="focused error"
            />
          </FormControl>
        </Stack>
      </Box>
    </>
  ),
};

export const Colors: Story = {
  render: () => (
    <>
      <Typography variant="body2" color="secondary">
        The <code>filled</code> variant can be used alongside color options. Note, the error state
        is distinct from <code>color="error"</code>
      </Typography>
      <Box mt={2} width={theme => theme.fixedWidths.xs}>
        <Stack gap={2}>
          <FormControl>
            <FilledInput inputProps={{'aria-label': 'default'}} placeholder="default" />
          </FormControl>
          <FormControl>
            <FilledInput
              inputProps={{'aria-label': 'error'}}
              placeholder="color='error'"
              color="error"
            />
          </FormControl>
          <FormControl>
            <FilledInput
              inputProps={{'aria-label': 'warning'}}
              placeholder="color='warning'"
              color="warning"
            />
          </FormControl>
        </Stack>
      </Box>
    </>
  ),
};

export const UsageInTable: Story = {
  render: () => (
    <TableContainer>
      <Table padding="none">
        <TableHead>
          <TableRow>
            <TableCell>Field 1</TableCell>
            <TableCell>Field 2</TableCell>
            <TableCell>Field 3</TableCell>
            <TableCell>Field 4</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <FormControl>
                <FilledInput
                  placeholder="color='warning"
                  color="warning"
                  inputProps={{'aria-label': 'Input1'}}
                  startAdornment={
                    <InputAdornment position="start">
                      <SvgIcon type="livestock" />
                    </InputAdornment>
                  }
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl>
                <FilledInput
                  type="password"
                  placeholder="password"
                  inputProps={{'aria-label': 'Input2'}}
                  endAdornment={
                    <InputAdornment position="end">
                      <SvgIcon type="visibility" />
                    </InputAdornment>
                  }
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl error>
                <FilledInput
                  placeholder="error"
                  inputProps={{'aria-label': 'Input3'}}
                  endAdornment={
                    <InputAdornment position="end">
                      <SvgIcon type="cross-circled" color="error" />
                    </InputAdornment>
                  }
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl>
                <FilledInput
                  type="date"
                  placeholder="color='error'"
                  color="error"
                  inputProps={{'aria-label': 'Input4'}}
                  startAdornment={
                    <InputAdornment position="start">
                      <SvgIcon type="calendar" />
                    </InputAdornment>
                  }
                  endAdornment={
                    <InputAdornment position="end">
                      <SvgIcon type="cross-circled" color="error" />
                    </InputAdornment>
                  }
                />
              </FormControl>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>
              <FormControl disabled>
                <FilledInput
                  defaultValue="disabled"
                  inputProps={{'aria-label': 'Input5'}}
                  endAdornment={
                    <InputAdornment position="end">
                      <Tooltip id="" title="tooltip">
                        <span>
                          <SvgIcon type="info-circled" color="disabled" />
                        </span>
                      </Tooltip>
                    </InputAdornment>
                  }
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl>
                <FilledInput
                  type="number"
                  placeholder="number"
                  inputProps={{'aria-label': 'Input6'}}
                  endAdornment={
                    <Typography variant="body1" color="text.primary">
                      kg
                    </Typography>
                  }
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl>
                <FilledInput
                  placeholder="multiline"
                  multiline
                  inputProps={{'aria-label': 'Input7'}}
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl>
                <FilledInput
                  type="search"
                  placeholder="search"
                  inputProps={{'aria-label': 'Input8'}}
                  endAdornment={
                    <InputAdornment position="end">
                      <SvgIcon type="search" />
                    </InputAdornment>
                  }
                />
              </FormControl>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  ),
};
