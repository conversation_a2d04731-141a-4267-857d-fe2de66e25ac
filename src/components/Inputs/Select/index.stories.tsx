import type {<PERSON>a, StoryObj} from '@storybook/react';
import {
  Box,
  FormControl,
  FormHelperText,
  FormLabel,
  InputAdornment,
  MenuItem,
  Select,
  Stack,
  SvgIcon,
  Typography,
  type SelectProps,
} from 'src/index';

import argTypes from './argTypes';

type Story = StoryObj<SelectProps<string>>;

const meta: Meta<typeof Select> = {
  component: Select,
  title: 'components/Inputs/Select',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=5291%3A60590',
    },
  },
};

export default meta;

export const Basic: StoryObj = {
  args: {
    defaultValue: 1,
  },
  render: args => {
    return (
      <Select {...args}>
        <MenuItem value={1}>Option 1</MenuItem>
        <MenuItem value={2}>Option 2</MenuItem>
        <MenuItem value={3}>Option 3</MenuItem>
      </Select>
    );
  },
};

// Note: The `Select` component can be used to create a multi-select dropdown by adding the `multiple` prop to the `Select` component.
export const Multi: StoryObj = {
  args: {
    defaultValue: [1],
    options: [
      {label: 'Option 1', value: 1},
      {label: 'Option 2', value: 2},
      {label: 'Option 3', value: 3},
    ],
    multiple: true,
  },
  render: args => {
    return (
      <Select<number[]> {...args} multiple={true}>
        <MenuItem value={1}>Option 1</MenuItem>
        <MenuItem value={2}>Option 2</MenuItem>
        <MenuItem value={3}>Option 3</MenuItem>
      </Select>
    );
  },
};

export const ComposedSelect: Story = {
  render: props => (
    <FormControl>
      <Stack rowGap={2} direction="row">
        <Box height={theme => theme.spacing(9)} display="flex" alignItems="center" mr={2}>
          <FormLabel>Form Label: </FormLabel>
        </Box>
        <Box>
          <Select
            label="Select Label"
            displayEmpty
            renderValue={selected =>
              selected ?? <Typography color="text.placeholder">Placeholder</Typography>
            }
            endAdornment={
              <InputAdornment position="end">
                <SvgIcon type="filter" />
              </InputAdornment>
            }
            {...props}
          >
            <MenuItem value="" disabled>
              Select placeholder
            </MenuItem>
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
          <Box mt={2}>
            <FormHelperText>Form helper text</FormHelperText>
          </Box>
        </Box>
      </Stack>
    </FormControl>
  ),
};

export const Variants: Story = {
  render: () => {
    return (
      <>
        <Typography variant="body2" color="secondary">
          The <code>outlined</code> (default) variant is intended to cover most of our input use
          cases.
        </Typography>
        <Box mb={4} mt={2}>
          <Select variant="outlined" defaultValue="1">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </Box>
        <Typography variant="body2" color="secondary">
          The <code>filled</code> variant is only used for table inputs in the MRV data collection
          forms.
        </Typography>
        <Box mb={2} mt={2}>
          <Select variant="filled" defaultValue="1">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </Box>
      </>
    );
  },
};

export const OutlinedStates: Story = {
  render: () => (
    <>
      <Box mb={2}>
        <Typography variant="body2" color="secondary">
          The <code>outlined</code> (default) variant is intended to cover most of our select use
          cases.
        </Typography>
      </Box>
      <Box mb={2}>
        <Typography variant="body2" color="secondary">
          This variant accommodates <code>disabled</code>, <code>required</code>,{' '}
          <code>readOnly</code>, <code>focused</code> and <code>error</code> states.
        </Typography>
      </Box>
      <Box mb={5}>
        <FormControl>
          <Box mb={2}>
            <FormLabel>Select Label</FormLabel>
          </Box>
          <Select>
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl>
          <Box mb={2}>
            <FormLabel>Select DefaultValue</FormLabel>
          </Box>
          <Select defaultValue="3">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl>
          <Box mb={2}>
            <FormLabel>Select Placeholder</FormLabel>
          </Box>
          <Select
            displayEmpty
            renderValue={selected =>
              selected ?? <Typography color="text.placeholder">Select placeholder</Typography>
            }
          >
            <MenuItem>
              <Typography color="text.placeholder">Select placeholder</Typography>
            </MenuItem>
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl disabled>
          <Box mb={2}>
            <FormLabel>Select Disabled</FormLabel>
          </Box>
          <Select defaultValue="1">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl required>
          <Box mb={2}>
            <FormLabel>Select Required</FormLabel>
          </Box>
          <Select defaultValue="1">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl>
          <Box mb={2}>
            <FormLabel>Select ReadOnly</FormLabel>
          </Box>
          <Select defaultValue="2" readOnly>
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl focused>
          <Box mb={2}>
            <FormLabel>Select Focused</FormLabel>
          </Box>
          <Select defaultValue="1">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl error>
          <Box mb={2}>
            <FormLabel>Select Error</FormLabel>
          </Box>
          <Select defaultValue="1">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={5}>
        <FormControl error focused>
          <Box mb={2}>
            <FormLabel>Select Focused Error</FormLabel>
          </Box>
          <Select defaultValue="1">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
    </>
  ),
};

export const FilledStates: Story = {
  render: () => (
    <>
      <Box mb={2}>
        <Typography variant="body2" color="secondary">
          The <code>filled</code> variant is intended to be used only in tables.
        </Typography>
      </Box>
      <Box mb={2}>
        <Typography variant="body2" color="secondary">
          This variant accommodates <code>disabled</code>, <code>readOnly</code>,{' '}
          <code>focused</code> and <code>error</code> states.
        </Typography>
      </Box>
      <Box mb={4}>
        <Typography variant="body2" color="secondary">
          Notice, there are two color options, <code>error</code> and <code>warning</code>, as well.
        </Typography>
      </Box>
      <Box mb={2}>
        <FormControl>
          <Select variant="filled" defaultValue="3">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Default Value</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl>
          <Select
            variant="filled"
            displayEmpty
            renderValue={selected =>
              selected ?? <Typography color="text.placeholder">Select placeholder</Typography>
            }
          >
            <MenuItem>
              <Typography color="text.placeholder">select placeholder</Typography>
            </MenuItem>
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">Option 2</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl disabled>
          <Select variant="filled" defaultValue="2">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">disabled</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl>
          <Select variant="filled" readOnly defaultValue="2">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">readOnly</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl focused>
          <Select variant="filled" defaultValue="2">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">focused</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl error>
          <Select
            variant="filled"
            defaultValue="2"
            endAdornment={
              <InputAdornment position="end">
                <SvgIcon type="cross-circled" color="error" />
              </InputAdornment>
            }
          >
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">error</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl focused error>
          <Select
            variant="filled"
            defaultValue="2"
            endAdornment={
              <InputAdornment position="end">
                <SvgIcon type="cross-circled" color="error" />
              </InputAdornment>
            }
          >
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">focused error</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl>
          <Select variant="filled" defaultValue="2" color="error">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">color='error'</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box mb={2}>
        <FormControl>
          <Select variant="filled" defaultValue="2" color="warning">
            <MenuItem value="1">Option 1</MenuItem>
            <MenuItem value="2">color='warning'</MenuItem>
            <MenuItem value="3">Option 3</MenuItem>
          </Select>
        </FormControl>
      </Box>
    </>
  ),
};
