import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as SelectStories from './index.stories.tsx'

<Meta of={SelectStories}/>

## Overview
`Select` is a fundamental MUI Form component.

For a pre-composed `Select` with a `Label` and `FormHelperText`, please use `<TextField select />`. <br />
This component should be used when a more custom solution is required than what `TextField` can provide. <br />
`Select` should be composed with `FormControl`, `FormLabel`, and `MenuItem`s

[MUI Select](https://mui.com/material-ui/api/select/)

<Canvas of={SelectStories.Basic} />
<Controls of={SelectStories.Basic}/>

## Composed Select
Utilizes `FormControl`, `FormLabel` and `FormHelperText`
<Canvas of={SelectStories.ComposedSelect} />

## Variants
<Canvas of={SelectStories.Variants} />

## Outlined Variant (default) States
<Canvas of={SelectStories.OutlinedStates} />

## Filled Variant (Table input) States
<Canvas of={SelectStories.FilledStates} />

## Multi
The `Select` component can be used to create a multi-select dropdown by adding the `multiple` prop to the `Select` component.
<Canvas of={SelectStories.Multi} />
