import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const selectArgTypeKeys = [
  'autoWidth',
  'autoFocus',
  'color',
  'defaultOpen',
  'defaultValue',
  'displayEmpty',
  'disabled',
  'error',
  'fullWidth',
  'focused',
  'IconComponent',
  'id',
  'input',
  'inputProps',
  'inputRef',
  'label',
  'labelId',
  'MenuProps',
  'multiple',
  'name',
  'onChange',
  'onClose',
  'onOpen',
  'open',
  'placeholder',
  'readOnly',
  'renderValue',
  'required',
  'SelectDisplayProps',
  'variant',
  'classes',
  'sx',
  'children',
];

export const SELECT_ARGTYPES = {
  color: {
    table: {
      type: {summary: 'undefined | error | warning'},
    },
    control: {
      type: 'select',
    },
    options: [undefined, 'error', 'warning'],
  },
  autoWidth: {
    table: {
      type: {summary: 'boolean'},
      defaultValue: {summary: false},
    },
    control: 'boolean',
    description:
      'If `true`, the width of the popover will automatically be set according to the items inside the menu, otherwise it will be at least the width of the select input.',
  },
  displayEmpty: {
    table: {
      type: {summary: 'boolean'},
      defaultValue: {summary: false},
    },
    control: 'boolean',
    description:
      'If `true`, a value is displayed even if no items are selected. In order to display a meaningful value, a function can be passed to the `renderValue` prop which returns the value to be displayed when no items are selected.',
  },
  IconComponent: {
    description: 'The icon that displays the arrow.',
    table: {
      type: {summary: 'elementType'},
    },
  },
  MenuProps: {
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
    description: 'Props applied to the `Menu` element.',
  },
  multiple: {
    table: {
      type: {summary: 'boolean'},
      defaultValue: {summary: false},
    },
    control: 'boolean',
    description: 'If `true`, value must be an array and the menu will support multiple selections.',
  },
  onChange: {
    table: {
      type: {summary: 'func'},
    },
    description:
      'Callback fired when the value is changed. <br /> `function(event: SelectChangeEvent, child?: object) => void`<br />`event` The event source of the callback. You can pull out the new value by accessing <br />`event.target.value`<br />`child` The react element that was selected when native is false (default).',
  },
  renderValue: {
    table: {
      type: {summary: 'func'},
    },
    description:
      'Render the selected value. You can only use it when the native prop is false (default). <br /> `function(value: any) => ReactNode` <br />`value` The `value` provided to the component.',
  },
  SelectDisplayProps: {
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
    description: 'Props applied to the clickable div element.',
  },
};

const argTypes = getArgTypes(selectArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...SELECT_ARGTYPES,
});

export default argTypes;
