import {
  Select as MUISelect,
  selectClasses,
  type Components,
  type SelectProps as MUISelectProps,
  type Theme,
} from '@mui/material';

import {SvgIcon} from 'src/components/Icon';
import {type InputVariant} from 'src/components/Inputs/InputBase';

export const SelectOverrides: Components<Theme>['MuiSelect'] = {
  defaultProps: {
    MenuProps: {
      elevation: 4,
    },
  },
  styleOverrides: {
    select: () => ({
      [`&&.${selectClasses.select}`]: {
        background: 'none',
      },
    }),
    icon: ({theme}) => ({
      color: theme.palette.semanticPalette.text.main,
      fontSize: theme.typography.h5.lineHeight,
      right: theme.spacing(2),
    }),
  },
};

const SelectIcon = ({className}: {className: string}) => (
  <SvgIcon type="chevron-down" className={className} color="inherit" fontSize="h5" />
);

// Note, we're internally modifying the SelectChangeEvent
// because M<PERSON> does not properly pass through the generic
// https://github.com/regrowag/design-system/pull/243/files#r1506847527
// This can be removed once this issue is resolved https://github.com/mui/material-ui/issues/35715
type SelectChangeEvent<Value> = Event & {target: {value: Value; name: string}};

// TODO: Value should extend string | number when multiple: false and string[] | number[] when multiple: true
// https://regrow.atlassian.net/browse/DES-241
type SelectProps<Value> = Omit<MUISelectProps<Value>, 'autoComplete' | 'onChange' | 'variant'> & {
  onChange?: (e: SelectChangeEvent<Value>) => void;
  variant?: InputVariant;
};

// function syntax to avoid vscode syntax highlight issue with generic default and arrow function
// https://github.com/microsoft/TypeScript-TmLanguage/issues/990
const Select = function <Value>(props: SelectProps<Value>) {
  const {onChange, IconComponent = SelectIcon, ...rest} = props;

  return (
    <MUISelect<Value>
      IconComponent={IconComponent}
      onChange={onChange as MUISelectProps['onChange']} // MUI does not properly pass through the generic (see above note for details)
      autoComplete="off" // We want to disable browser autocomplete for select
      {...rest}
    />
  );
};

export {Select, type SelectProps, SelectIcon, type SelectChangeEvent};
