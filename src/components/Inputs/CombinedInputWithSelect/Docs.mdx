import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as CombinedInputWithSelectStories from './index.stories.tsx'

<Meta of={CombinedInputWithSelectStories} />

## Overview
The `CombinedInputWithSelect` is an extended version of the [OutlinedInput](https://mui.com/material-ui/api/outlined-input/) and [FilledInput](https://mui.com/material-ui/api/filled-input/) component composed with a [Select](https://mui.com/material-ui/api/select/).
The parent Box component contains the text Input and the Select components. It also uses custom components to: 
- `CombinedInputWithSelectOutline` is used for displaying the background color and the border color. 
- `ShadowComponent` is util component that helps to expand the input and select components accordingly to the their content. 


This component takes five primary props:
- `selectOptions` (required) which is an array of objects `{label: string | ReactElement, value: T (default string)}`. These are used to define the options available to the `Select`.
- `selectDefaultValue` (required) which is the select component default value (not label)
- The optional `variant` prop accepts `"filled"` (default) or `"outlined"`. This prop controls the appearance of the Input.
- The optional `type` prop is restricted to `text` (default) or `number`. This prop is a pass through for the native input `type` property.
- The optional `textAlign` prop is restricted to `right` (default) or `left`. This prop controls text input's text alignment. 

This component also optionally accepts all props (except `endAdornment`) of either [OutlinedInput]( https://mui.com/material-ui/api/outlined-input/ ) or [FilledInput](https://mui.com/material-ui/api/filled-input/), in accordance with the variant prop specified.

[SelectProps](https://mui.com/material-ui/api/select/) are passed through to the nested Select component. 
This should be used to set the select component `onChange` handler, and input `value` for controlled selects.

`disabled` and `readOnly` states of the Select are inherited from the parent `CombinedInputWithSelect`. 
Note, `disabled`, `readOnly`, `focused`, `error` or `required` are all inherited from the wrapping `FormControl`.

Further style customizations can be provided to both the nested `Select` and `MenuItem` components, though not recommended, via [`SelectProps`](https://mui.com/material-ui/api/select/), mentioned above, and [`MenuItemProps`](https://mui.com/material-ui/api/menu-item/), respectively.





<Canvas of={CombinedInputWithSelectStories.Basic} />
<Controls of={CombinedInputWithSelectStories.Basic}/>

### Variants
<Canvas of={CombinedInputWithSelectStories.Variants} />

### States
<Canvas of={CombinedInputWithSelectStories.States} />

### Colors
<Canvas of={CombinedInputWithSelectStories.Colors} />
