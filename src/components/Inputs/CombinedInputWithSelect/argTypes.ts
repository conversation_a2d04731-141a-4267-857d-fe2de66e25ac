import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const inputArgTypes = [
  'autoComplete',
  'autoFocus',
  'classes',
  'color',
  'components',
  'componentsProps',
  'defaultValue',
  'disabled',
  'error',
  'fullWidth',
  'hiddenLabel',
  'id',
  'inputComponent',
  'inputProps',
  'inputRef',
  'maxRows',
  'minRows',
  'multiline',
  'name',
  'onChange',
  'placeholder',
  'readOnly',
  'required',
  'rows',
  'slotProps',
  'slots',
  'startAdornment',
  'sx',
  'type',
  'value',
  'variant',
  'selectDefaultValue',
  'selectOptions',
  'SelectProps',
  'MenuItemProps',
  'textAlign',
];

const COMBINED_INPUT_ARGTYPES = {
  type: {
    description: 'The type of `select` element to render.',
    table: {
      type: {summary: 'text | number'},
      defaultValue: {summary: 'text'},
    },
    control: 'inline-radio',
    options: ['text', 'number'],
  },
  selectOptions: {
    description: 'Required array of option objects to render within the Select',
    table: {
      type: {summary: `Array<{label: string | ReactElement, value: T (default string)}>`},
    },
  },
  selectDefaultValue: {
    description: 'Required default select value.',
    table: {
      type: {summary: `T (default string)`},
    },
  },
  SelectProps: {
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
    description:
      'Props applied to the nested Select component, including defaultValue, value, onChange.',
  },
  MenuItemProps: {
    table: {
      category: 'Nested Components',
      type: {summary: 'object'},
    },
    description: 'Props applied to the nested MenuItem components.',
  },

  textAlign: {
    description: 'Align text of the input.',
    table: {
      type: {summary: 'right | left'},
      defaultValue: {summary: 'right'},
    },
    control: 'inline-radio',
    options: ['right', 'left'],
  },
};

const argTypes = getArgTypes(inputArgTypes, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...COMBINED_INPUT_ARGTYPES,
});

export default argTypes;
