import {forwardRef, useMemo, useState, type ForwardedRef} from 'react';
import {useDebouncedState} from 'src/hooks';
import {
  filledInputClasses,
  inputBaseClasses,
  outlinedInputClasses,
  selectClasses,
  useFormControl,
  useTheme,
} from '@mui/material';

import {
  INPUT_BASE_SIDES_TOTAL_PADDING,
  INPUT_NUMBER_ARROWS_WIDTH,
  SELECT_ARROW_ICON_WIDTH_WITH_PADDING,
  useGetLongestOptionContent,
} from 'src/components/Inputs/CombinedInputWithSelect/utils';
import {FilledInput, type FilledInputProps} from 'src/components/Inputs/FilledInput';
import {type InputVariant} from 'src/components/Inputs/InputBase';
import {OutlinedInput, type OutlinedInputProps} from 'src/components/Inputs/OutlinedInput';
import {Select, type SelectProps} from 'src/components/Inputs/Select';
import {MenuItem, type MenuItemProps} from 'src/components/Menu';
import {Typography} from 'src/components/Typography';

import type {SelectOptions} from '../SelectField';
import {
  CombinedInputWithSelectContainer,
  CombinedInputWithSelectOutline,
  ShadowComponent,
  StyledInputFormControl,
  StyledSelectFormControl,
} from './CombinedInputWithSelect.styled';

type ExtendedInputProps<T extends InputVariant> = {
  variant?: T;
} & Omit<T extends 'filled' ? FilledInputProps : OutlinedInputProps, 'endAdornment' | 'ref'>;

type CombinedInputWithSelectProps<
  InputVariantT extends InputVariant,
  Value extends string | number
> = ExtendedInputProps<InputVariantT> & {
  MenuItemProps?: MenuItemProps;
  SelectProps?: Omit<SelectProps<Value>, 'variant' | 'fullWidth'>;
  selectOptions: SelectOptions<Value>;
  selectDefaultValue: SelectProps<Value>['value'];
  type?: 'text' | 'number';
  textAlign?: 'left' | 'right';
};

const COMBINED_SELECT_PLACEHOLDER_VALUE = 'combined-select-placeholder';
const CombinedInputWithSelectComponent = <
  InputVariantT extends InputVariant,
  Value extends string | number
>(
  props: CombinedInputWithSelectProps<InputVariantT, Value>,
  ref: ForwardedRef<HTMLInputElement>
) => {
  const {
    variant = 'filled',
    disabled,
    readOnly,
    selectOptions,
    selectDefaultValue,
    SelectProps = {},
    MenuItemProps,
    sx: inputSx,
    value,
    textAlign = 'right',
    ...restInputProps
  } = props;
  const theme = useTheme();
  const {sx: selectSx, placeholder, ...restSelectProps} = SelectProps;

  const formControlValues = useFormControl();
  const [$focused, $setFocused] = useDebouncedState(formControlValues?.focused, 50);
  const [$inputValue, $setInputValue] = useState(value ?? restInputProps.defaultValue ?? '');

  const longestOptionContent = useGetLongestOptionContent({
    selectOptions,
  });

  const inputValue = restInputProps?.onChange ? value : $inputValue;

  const shadowInputPadding =
    INPUT_BASE_SIDES_TOTAL_PADDING +
    (restInputProps.type === 'number' ? INPUT_NUMBER_ARROWS_WIDTH : 0);

  const InputComponent: typeof FilledInput | typeof OutlinedInput = useMemo(() => {
    switch (variant) {
      case 'outlined':
        return OutlinedInput;
      case 'filled':
      default:
        return FilledInput;
    }
  }, [variant]);

  const inputStyles = useMemo(
    () => ({
      [`&.${inputBaseClasses.root}, 
        &.${inputBaseClasses.root}.${inputBaseClasses.disabled},
        &.${inputBaseClasses.root}.${inputBaseClasses.error},
        &.${inputBaseClasses.root}.${filledInputClasses.root},
        &.${inputBaseClasses.root}.${filledInputClasses.root}:hover,
        &.${inputBaseClasses.root}:hover`]: {
        borderWidth: 0,
        backgroundColor: 'transparent',
      },
      [`& .${inputBaseClasses.input}`]: {
        textAlign: textAlign,
      },
      [`&.${inputBaseClasses.root}.${filledInputClasses.root},
        &.${inputBaseClasses.root}.${filledInputClasses.root}:hover`]: {
        padding: theme.spacing(4, 3),
      },

      [`& .${outlinedInputClasses.notchedOutline}`]: {
        display: 'none',
      },
      ...(inputSx ?? {}),
    }),
    [inputSx, textAlign, theme]
  );

  const selectStyles = useMemo(
    () => ({
      [`&.${inputBaseClasses.root},
        &.${inputBaseClasses.root}:hover,
        &.${inputBaseClasses.root}.${inputBaseClasses.disabled},
        &.${inputBaseClasses.root}.${inputBaseClasses.error},
        &.${inputBaseClasses.root}.${inputBaseClasses.focused}`]: {
        [`&& .${selectClasses.select}`]: {
          paddingRight: theme.spacing(7),
        },
        padding: 0,
        borderWidth: 0,
        backgroundColor: 'transparent',
      },
      ...(selectSx ?? {}),
    }),
    [selectSx, theme]
  );

  const SelectMenuItems = useMemo(() => {
    return selectOptions?.map((option, i) => (
      <MenuItem
        key={`opt-${i}`}
        value={option.value}
        disabled={option.disabled}
        {...(MenuItemProps ?? {})}
      >
        {option.label}
      </MenuItem>
    ));
  }, [selectOptions, MenuItemProps]);

  const renderPlaceholder = placeholder && (!selectDefaultValue || selectDefaultValue === '');
  const defaultValue = (
    renderPlaceholder ? COMBINED_SELECT_PLACEHOLDER_VALUE : selectDefaultValue
  ) as Value;

  return (
    <CombinedInputWithSelectContainer
      onFocus={() => $setFocused(true)}
      onBlur={() => $setFocused(false)}
    >
      <CombinedInputWithSelectOutline
        $variant={variant}
        $error={restInputProps.error || formControlValues?.error}
        $focused={$focused || formControlValues?.focused}
        $color={restInputProps.color}
      />
      <StyledInputFormControl disabled={disabled ?? formControlValues?.disabled}>
        <ShadowComponent $paddingRight={shadowInputPadding} children={inputValue} />
        <ShadowComponent $paddingRight={shadowInputPadding} children={restInputProps.placeholder} />
        <InputComponent
          {...restInputProps}
          value={inputValue}
          onChange={e => restInputProps?.onChange?.(e) ?? $setInputValue(e.target.value)}
          ref={ref}
          sx={inputStyles}
          readOnly={readOnly}
        />
      </StyledInputFormControl>
      <StyledSelectFormControl disabled={disabled ?? formControlValues?.disabled}>
        <ShadowComponent
          $paddingRight={SELECT_ARROW_ICON_WIDTH_WITH_PADDING}
          children={longestOptionContent}
        />
        <Select
          {...restSelectProps}
          defaultValue={defaultValue}
          readOnly={readOnly}
          sx={selectStyles}
          variant="filled"
        >
          {placeholder ? (
            <MenuItem disabled value={COMBINED_SELECT_PLACEHOLDER_VALUE}>
              <Typography color="text.placeholder">{placeholder}</Typography>
            </MenuItem>
          ) : null}
          {SelectMenuItems}
        </Select>
      </StyledSelectFormControl>
    </CombinedInputWithSelectContainer>
  );
};

const CombinedInputWithSelect = forwardRef(CombinedInputWithSelectComponent) as <
  InputVariantT extends InputVariant,
  Value extends string | number
>(
  props: CombinedInputWithSelectProps<InputVariantT, Value> & {
    ref?: React.ForwardedRef<HTMLInputElement>;
  }
) => ReturnType<typeof CombinedInputWithSelectComponent>;

export {CombinedInputWithSelect};
export type {CombinedInputWithSelectProps};

// @ts-expect-error storybook only name
CombinedInputWithSelect.displayName = 'CombinedInputWithSelect';
