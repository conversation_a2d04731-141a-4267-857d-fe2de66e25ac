import type {SEMANTIC_STATE_KEYS} from 'src/tokens/constants';
import {FormControl, styled, TableRow} from '@mui/material';

import {Box} from 'src/components/Box';
import type {InputVariant} from 'src/components/Inputs/InputBase';

export const StyledInputFormControl = styled(FormControl)`
  flex-grow: 1;
`;
export const StyledSelectFormControl = styled(FormControl)`
  min-width: auto;
`;

// This is a hidden component, used to expand parent's component width, so the sibling input will take more space.
// The <input> component don't have ability to span accordingly to the content, so we need to use this trick.
export const ShadowComponent = styled('div')<{
  $paddingRight?: number;
}>(({$paddingRight = 0}) => {
  return `
  visibility: hidden;
  overflow: hidden;
  height: 0;
  padding-right: ${$paddingRight}px;
  white-space: nowrap;
  width: fit-content;
  min-width: 50px;
  `;
});

export const CombinedInputWithSelectContainer = styled(Box)`
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  position: relative;
  width: fit-content;
  min-width: 100%;
`;

export const CombinedInputWithSelectOutline = styled(Box)<{
  $focused?: boolean;
  $error?: boolean;
  $color?: (typeof SEMANTIC_STATE_KEYS)[number];
  $variant: InputVariant;
}>(({theme, $error, $variant, $focused, $color}) => {
  let borderColor = theme.palette.semanticPalette.stroke.main;
  let focusColor = theme.palette.semanticPalette.stroke.brand;
  const borderRadius = $variant === 'filled' ? 0 : theme.shape.borderRadius;
  let borderWidth = $variant === 'filled' ? 0 : 1;

  switch ($color) {
    case 'main':
      focusColor = theme.palette.semanticPalette.stroke.brand;
      break;
    case 'success':
      focusColor = theme.palette.semanticPalette.stroke.success;
      break;
    case 'warning':
      focusColor = theme.palette.semanticPalette.stroke.warning;
      break;

    default:
      focusColor = theme.palette.semanticPalette.stroke.brand;
  }

  if ($error || $color === 'error') {
    $color = 'error';
    borderColor = theme.palette.semanticPalette.stroke.error;
    focusColor = theme.palette.semanticPalette.stroke.error;
  }

  if ($focused) {
    borderWidth = 2;
    borderColor = focusColor;
  }

  return {
    '&': {
      backgroundColor: $color ? theme.palette.semanticPalette.surface[$color] : 'transparent',
      border: `${borderWidth}px solid ${borderColor}`,
      borderRadius: `${borderRadius}px`,
      boxSizing: 'border-box',
      position: 'absolute',
      width: '100%',
      height: '100%',
    },
  };
});

export const StyledTableRow = styled(TableRow)`
  &:hover {
    background-color: ${({theme}) => {
      return theme.palette.semanticPalette.surface.secondary;
    }};
  }
`;
