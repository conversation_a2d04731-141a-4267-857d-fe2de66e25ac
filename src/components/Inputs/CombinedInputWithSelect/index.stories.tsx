import type {Meta, StoryObj} from '@storybook/react';
import {
  Box,
  CombinedInputWithSelect,
  FormControl,
  FormHelperText,
  FormLabel,
  Stack,
  SvgIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from 'src/index';

import argTypes from 'src/components/Inputs/CombinedInputWithSelect/argTypes';
import {StyledTableRow} from 'src/components/Inputs/CombinedInputWithSelect/CombinedInputWithSelect.styled';

const selectOptions = [
  {value: '1', label: 'kg'},
  {value: '2', label: 'lbs'},
  {value: '3', label: 'oz'},
  {value: '4', label: 'wider one'},
];
const selectOptions2 = [
  {value: '1', label: 'gal1ac-1'},
  {value: '2', label: 'qt1ac-1'},
];

const meta: Meta<typeof CombinedInputWithSelect> = {
  component: CombinedInputWithSelect,
  title: 'components/Inputs/CombinedInputWithSelect',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=2852%3A27954&mode=dev',
    },
  },
  args: {
    selectOptions,
    selectDefaultValue: '1',
  },
};

export default meta;

type Story = StoryObj<typeof CombinedInputWithSelect>;

export const Basic: Story = {
  render: props => (
    <>
      <Typography variant="body2" color="secondary">
        The combined <b>filled</b> (default variant) input with select. (CombinedInputWithSelect)
      </Typography>
      <Box mt={2} maxWidth={200}>
        <FormControl fullWidth>
          <CombinedInputWithSelect
            value="100"
            inputProps={{'aria-label': 'input-filled'}}
            {...props}
          />
        </FormControl>
      </Box>
    </>
  ),
};

const Error = () => {
  return (
    <FormHelperText component={'div'}>
      <Box display="flex" alignContent="baseline" mt={2}>
        <Box mr={1} component="span">
          <SvgIcon type="cross-circled" color="error" />
        </Box>
        <Typography variant="body2" component="span">
          This is an error message
        </Typography>
      </Box>
    </FormHelperText>
  );
};

export const Variants: Story = {
  render: props => (
    <>
      <Typography variant="body2" color="secondary">
        There are two variants for the CombinedInputWithSelect: <b>filled</b> and <b>outlined</b>.
        The <b>filled</b> variant is the default one.
      </Typography>
      <Box mt={5} width={theme => theme.fixedWidths.xs}>
        <Stack gap={5}>
          <Box mb={5}>
            <FormControl fullWidth>
              <CombinedInputWithSelect
                inputProps={{
                  'aria-label': 'CombinedInputWithSelect Default variant (filled) Label',
                }}
                {...props}
              />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined">
                  CombinedInputWithSelect outlined variant Label
                </FormLabel>
              </Box>
              <CombinedInputWithSelect id="component-outlined" variant="outlined" {...props} />
            </FormControl>
          </Box>
        </Stack>
      </Box>
    </>
  ),
};

export const States: Story = {
  render: props => (
    <>
      <Typography variant="body2" color="secondary">
        The <b>filled and outlined</b> variants accommodate <code>disabled</code>,{' '}
        <code>required</code>, <code>readOnly</code>, <code>focused</code> and <code>error</code>{' '}
        states.
      </Typography>
      <Typography variant="body2" color="secondary">
        Below is an example of the <code>outlined</code> variant with all states.
      </Typography>
      <Box mt={2} width={theme => theme.fixedWidths.xs}>
        <Stack gap={2}>
          <Box mb={5}>
            <FormControl fullWidth>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-1">CombinedInputWithSelect Label</FormLabel>
              </Box>
              <CombinedInputWithSelect id="component-outlined-1" variant="outlined" {...props} />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-2">CombinedInputWithSelect value</FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-2"
                variant="outlined"
                value="100"
                {...props}
              />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-3">
                  CombinedInputWithSelect Placeholder
                </FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-3"
                variant="outlined"
                placeholder="Input placeholder"
                SelectProps={{placeholder: 'Select placeholder'}}
                {...props}
                selectDefaultValue={undefined}
              />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth disabled>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-4">
                  CombinedInputWithSelect Disabled
                </FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-4"
                variant="outlined"
                value="disabled"
                {...props}
              />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth required>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-5">
                  CombinedInputWithSelect Required
                </FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-5"
                variant="outlined"
                placeholder="required"
                {...props}
              />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-6">
                  CombinedInputWithSelect ReadOnly
                </FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-6"
                variant="outlined"
                value="readOnly"
                readOnly
                {...props}
              />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth focused>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-7">
                  CombinedInputWithSelect Focused
                </FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-7"
                variant="outlined"
                placeholder="focused"
                {...props}
              />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth error>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-8">CombinedInputWithSelect Error</FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-8"
                variant="outlined"
                placeholder="error"
                {...props}
              />
              <Error />
            </FormControl>
          </Box>
          <Box mb={5}>
            <FormControl fullWidth error focused>
              <Box mb={2}>
                <FormLabel htmlFor="component-outlined-9">
                  CombinedInputWithSelect Focused Error
                </FormLabel>
              </Box>
              <CombinedInputWithSelect
                id="component-outlined-9"
                variant="outlined"
                placeholder="focused error"
                {...props}
              />
              <Error />
            </FormControl>
          </Box>
        </Stack>
      </Box>
    </>
  ),
};

export const Colors: Story = {
  render: props => (
    <>
      <Typography variant="body2" color="secondary">
        The <b>filled</b> variant can be used alongside color options. Note, the error state is
        distinct from <code>color="error"</code>
      </Typography>
      <Box mt={2} width={theme => theme.fixedWidths.xs}>
        <Stack gap={2}>
          <FormControl fullWidth>
            <CombinedInputWithSelect
              inputProps={{'aria-label': 'default'}}
              placeholder="default"
              {...props}
            />
          </FormControl>
          <FormControl fullWidth>
            <CombinedInputWithSelect
              inputProps={{'aria-label': 'error'}}
              placeholder="color='error'"
              color="error"
              {...props}
            />
          </FormControl>
          <FormControl fullWidth>
            <CombinedInputWithSelect
              inputProps={{'aria-label': 'warning'}}
              placeholder="color='warning'"
              color="warning"
              {...props}
            />
          </FormControl>
        </Stack>
      </Box>
    </>
  ),
};

export const UsageInTable: Story = {
  render: props => (
    <TableContainer>
      <Table padding="none">
        <TableHead>
          <TableRow>
            <TableCell>Field 1</TableCell>
            <TableCell>Field 2</TableCell>
            <TableCell>Field 3</TableCell>
            <TableCell>Field 4</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <StyledTableRow>
            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  placeholder="color='warning"
                  color="warning"
                  inputProps={{'aria-label': 'Input1'}}
                  {...props}
                />
              </FormControl>
            </TableCell>

            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  placeholder="color='error'"
                  error
                  inputProps={{'aria-label': 'Input2'}}
                  {...props}
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  disabled
                  value="disabled"
                  inputProps={{'aria-label': 'Input3'}}
                  {...props}
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  type="number"
                  placeholder="type='number"
                  inputProps={{'aria-label': 'Input4'}}
                  {...props}
                />
              </FormControl>
            </TableCell>
          </StyledTableRow>
          <StyledTableRow>
            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  inputProps={{'aria-label': 'Input5'}}
                  {...props}
                  value={'872'}
                  selectOptions={selectOptions2}
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  type="number"
                  placeholder="type='number"
                  inputProps={{'aria-label': 'Input6'}}
                  {...props}
                  value={'42'}
                  selectOptions={selectOptions2}
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  inputProps={{'aria-label': 'Input7'}}
                  {...props}
                  value={'3821'}
                  selectOptions={selectOptions2}
                />
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl fullWidth>
                <CombinedInputWithSelect
                  type="number"
                  placeholder="type='number"
                  inputProps={{'aria-label': 'Input8'}}
                  {...props}
                  value={'500'}
                  selectOptions={selectOptions2}
                />
              </FormControl>
            </TableCell>
          </StyledTableRow>
        </TableBody>
      </Table>
    </TableContainer>
  ),
};
