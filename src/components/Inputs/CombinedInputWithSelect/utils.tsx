import {useMemo} from 'react';
import {isReactElement} from 'src/utils/typeGuards';

import type {CombinedInputWithSelectProps} from 'src/components/Inputs/CombinedInputWithSelect/index';
import type {InputVariant} from 'src/components/Inputs/InputBase';

type AccessibleCombinedInputWithSelectProps = CombinedInputWithSelectProps<
  InputVariant,
  string | number
>;

export const useGetLongestOptionContent = ({
  selectOptions = [],
}: {
  selectOptions: AccessibleCombinedInputWithSelectProps['selectOptions'];
}) => {
  const longestOptionContent = useMemo(() => {
    let longestOption = '';

    selectOptions?.forEach(option => {
      let currentOptionLabel = '';

      if (typeof option.label === 'string') {
        currentOptionLabel = option.label;
      } else if (
        // if the label is a ReactElement, try to catch the string from the children
        isReactElement(option.label) &&
        typeof option.label?.props?.children === 'string'
      ) {
        currentOptionLabel = option.label?.props?.children;
      }

      if (currentOptionLabel.length > longestOption.length) {
        longestOption = currentOptionLabel;
      }
    });

    return longestOption;
  }, [selectOptions]);

  return longestOptionContent;
};

// Verified size in px of actual components
export const SELECT_ARROW_ICON_WIDTH_WITH_PADDING = 28; // spacing 7 for the CombinedInputSelect
export const INPUT_NUMBER_ARROWS_WIDTH = 15;
export const INPUT_BASE_SIDES_TOTAL_PADDING = 24;
