import type {Components, Theme} from '@mui/material';
import {inputAdornmentClasses} from '@mui/material';

export const InputAdornmentOverrides: Components<Theme>['MuiInputAdornment'] = {
  styleOverrides: {
    root: ({theme}) => ({
      color: theme.palette.semanticPalette.text.main,
      svg: {
        fontSize: theme.typography.body1.lineHeight,
      },
      [`&&.${inputAdornmentClasses.root}.${inputAdornmentClasses.positionStart}`]: {
        marginTop: 0,
      },
    }),
  },
};
