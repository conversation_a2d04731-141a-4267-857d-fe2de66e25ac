import {
  INPUT_DISABLED_OPACITY,
  INPUT_PLACEHOLDER_OPACITY,
  INPUT_READ_ONLY_OPACITY,
} from 'src/tokens/constants';
import type {Components} from '@mui/material';
import {inputAdornmentClasses, inputBaseClasses, lighten, selectClasses} from '@mui/material';
import type {Theme} from '@mui/material/styles';

export type InputVariant = 'filled' | 'outlined';

export const InputBaseOverrides: Components<Theme>['MuiInputBase'] = {
  styleOverrides: {
    root: ({theme}) => ({
      [`.${inputBaseClasses.input}`]: {
        height: theme.typography.body1.lineHeight,
        lineHeight: theme.typography.body1.lineHeight,
        padding: `${theme.spacing(2)} ${theme.spacing(3)}`,
        // This is added to override the box-sizing: inherit that gets added by ScopedCSSBaseline
        // This can be removed when we have fully migrated away from react-md
        boxSizing: 'content-box',
        [`&::placeholder, &::-webkit-input-placeholder`]: {
          // This is to better distinguish placeholder text from disabled state text
          color: lighten(theme.palette.text.secondary, INPUT_PLACEHOLDER_OPACITY),
          opacity: 1,
          transition: 'none',
        },
        [`&.${inputBaseClasses.readOnly}`]: {
          // This is align readOnly and disabled state text
          color: lighten(theme.palette.text.secondary, INPUT_READ_ONLY_OPACITY),
        },
        [`&.Mui-disabled`]: {
          opacity: INPUT_DISABLED_OPACITY,
        },
      },
      [`&.${inputBaseClasses.root}.${inputBaseClasses.multiline}`]: {
        height: 'auto',
        padding: 0,
      },
      [`&.${inputBaseClasses.adornedStart}`]: {
        paddingLeft: 0,
        [`& .${inputAdornmentClasses.positionStart}`]: {
          paddingLeft: theme.spacing(3),
        },
      },
      [`&.${inputBaseClasses.adornedEnd}`]: {
        paddingRight: 0,
        [`& .${inputAdornmentClasses.positionEnd}`]: {
          paddingRight: theme.spacing(3),
        },
      },
      [`.${inputBaseClasses.inputAdornedStart}`]: {
        paddingLeft: 0,
      },
      [`.${inputBaseClasses.inputAdornedEnd}`]: {
        paddingRight: 0,
      },
      // select root selector is no longer supported so this style must be applied to inputbase
      // https://github.com/mui/material-ui/issues/30225
      [`&.${inputBaseClasses.adornedEnd} .${selectClasses.icon}`]: {
        right: theme.spacing(9),
      },
    }),
    inputTypeSearch: () => ({
      // Note: We decided to remove the native clear icon from the search input by default
      // Please use inputProps - endAdornment to add a clear icon should you need
      '::-webkit-search-cancel-button': {
        display: 'none',
      },
    }),
  },
};
