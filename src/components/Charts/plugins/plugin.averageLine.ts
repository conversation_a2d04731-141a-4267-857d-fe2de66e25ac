import type {Chart, ChartMeta, Plugin} from 'chart.js';
import {isDefined} from 'src/utils/typeGuards';
import type {Theme} from '@mui/material';

import {isDataArrayOfNumbers} from 'src/components/Charts/typeGuards';

export interface AverageLinePluginOptions {
  label: string;
  formatter?: (value: number) => string;
  palette: Theme['palette'];
}

const CHART_TYPES = ['line', 'bar'];

export const AverageLinePlugin: Plugin = {
  id: 'averageLine',
  //Important to use afterDatasetDraw for this to be render after all datasets have been drawn and tooltip when hovering
  afterDatasetDraw(
    chart: Chart,
    args: {index: number; meta: ChartMeta},
    options: AverageLinePluginOptions
  ) {
    const {
      data: {datasets},
    } = chart;
    const {type: chartType, indexAxis} = args.meta;

    if (!CHART_TYPES.includes(chartType) || indexAxis !== 'x') return;
    if (!isDataArrayOfNumbers(datasets[0].data)) return;

    const {label, formatter, palette} = options;

    const average = getDataAverage(datasets[0].data);
    const annotation = `${label}: ${
      formatter ? formatter(average) : Intl.NumberFormat().format(average)
    }`;

    drawLine({average, chart, strokeColor: palette.semanticPalette.stroke.main});

    drawAnnotation({
      annotation,
      average,
      chart,
      textColor: palette.semanticPalette.text.secondary,
    });
  },
};

const getDataAverage = (data: Array<number>) => {
  const nonNilData = data.filter(isDefined);
  return nonNilData.reduce((acc, value) => acc + value, 0) / nonNilData.length;
};

const drawLine = ({
  average,
  chart,
  strokeColor,
}: {
  average: number;
  chart: Chart;
  strokeColor: string;
}) => {
  const {
    ctx,
    scales: {x, y},
  } = chart;
  const yValue = y.getPixelForValue(average);

  const lineDash = ctx.getLineDash(); // Gets default line dash configuration
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(x.left, yValue);
  ctx.setLineDash([6, 3]);
  ctx.lineTo(x.right, yValue);
  ctx.strokeStyle = strokeColor;
  ctx.stroke();
  ctx.setLineDash(lineDash); // return line dash to default configuration
};

const drawAnnotation = ({
  annotation,
  average,
  chart,
  textColor,
}: {
  annotation: string;
  average: number;
  chart: Chart;
  textColor: string;
}) => {
  const {
    ctx,
    scales: {x, y},
  } = chart;
  const padding = 4;
  const spacing = 4; // Space between label and average
  const borderRadius = 4;
  const xAdjustment = 4;
  const yAdjustment = 4;
  const fontCorrection = 1;

  const {
    width: annotationWidth,
    actualBoundingBoxAscent,
    actualBoundingBoxDescent,
  } = ctx.measureText(annotation);

  const contentWidth = annotationWidth + spacing + padding * 2;
  const contentHeight = actualBoundingBoxAscent + actualBoundingBoxDescent + padding * 2;
  const positionX = x.left + xAdjustment;
  const positionY = y.getPixelForValue(average) - yAdjustment - padding;

  // Draws background for the label
  ctx.fillStyle = '#FFFFFF99';
  ctx.beginPath();
  ctx.roundRect(
    positionX,
    positionY + padding + fontCorrection,
    contentWidth,
    -contentHeight,
    borderRadius
  );
  ctx.fill();

  // Draws annotation
  const annotationX = positionX + padding;
  ctx.fillStyle = textColor;
  ctx.fillText(annotation, annotationX, positionY);
};
