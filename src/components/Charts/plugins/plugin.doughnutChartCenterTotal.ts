import type {Chart, ChartMeta, Plugin} from 'chart.js';
import {isDefined} from 'src/utils/typeGuards';
import {type Theme} from '@mui/material';

import {isDataArrayOfNumbers} from 'src/components/Charts/typeGuards';

export interface DoughnutChartCenterTotalPluginOptions {
  unitLabel: string;
  formatter?: (value: number) => string;
  palette: Theme['palette'];
}

const CHART_TYPES = ['doughnut'];

export const DoughnutChartCenterTotalPlugin: Plugin<'doughnut'> = {
  id: 'centerTotal',
  afterDatasetDraw: (
    chart: Chart,
    args: {index: number; meta: ChartMeta},
    options: DoughnutChartCenterTotalPluginOptions
  ) => {
    const {
      data: {datasets},
    } = chart;
    const {type: chartType} = args.meta;

    const {unitLabel, formatter, palette} = options;

    if (!CHART_TYPES.includes(chartType)) return;
    if (!isDataArrayOfNumbers(datasets[0].data)) return;

    const total = getDataTotal(datasets[0].data);
    const annotation = `${
      formatter ? formatter(total) : Intl.NumberFormat().format(total)
    } ${unitLabel}`;

    const ctx = chart.ctx;
    const positionX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
    const positionY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2;

    ctx.save();
    ctx.font = '14px sans-serif';
    ctx.fillStyle = palette.semanticPalette.text.secondary;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(`${annotation}`, positionX, positionY);
    ctx.restore();
  },
};

const getDataTotal = (data: Array<number>) => {
  const nonNilData = data.filter(isDefined);
  return nonNilData.reduce((acc, value) => acc + value, 0);
};
