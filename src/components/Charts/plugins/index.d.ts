import type {ChartType} from 'chart.js';

import type {DoughnutChartCenterTotalPluginOptions} from 'src/components/Charts/plugins/plugin.doughnutChartCenterTotal';

import type {AverageLinePluginOptions} from './plugin.averageLine';
import type {ColorThemesPluginOptions} from './plugin.colorThemes';

declare module 'chart.js' {
  // TType is referenced by name in default type
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface PluginOptionsByType<TType extends ChartType> {
    /**
     * Plugin which automatically generates a chart's color theme given a `type`, optional `color`. (`palette` is already specified in all DS charts)
     *
     * @param type - string color theme type
     * `tonal-single` - Each dataset has a DIFFERENT tone of the base color. Each data point, within a dataset, is the SAME as the parent dataset tonal color.
     * `multi-single` - Each dataset has a DIFFERENT base color. Each data point, within a dataset, is the SAME as the parent dataset color.
     * `tonal` - Each dataset has the SAME base color. Each data point, within a dataset, is a tinted version of the parent dataset base color.
     * `multi` - Each data set uses the same set of colors. Each data point within a dataset has a DIFFERENT color
     * `single` - Each dataset and each data point, within the dataset, has the SAME base color
     * `disabled` - no colors are applied to the chart data points (default)
     * @param color (optional) - string hex color
     *
     * Plugin options are specified as chart options
     * ex.
     * options: {
     *   plugins: {
     *     colorThemes: {
     *       type: 'tonal-single',
     *       color: '#8BA6CF'
     *     }
     *   },
     * }
     *
     */
    colorThemes?: ColorThemesPluginOptions;
    /**
     * Plugin that generates a chart's average line and annotation based on the charts 0 index dataset
     * given a label and optional formatter
     *
     * @param label - string label for the average annotation
     * @param formatter - (optional) callback function that takes the average value and returns a string to be displayed on the chart
     *
     * Plugin options are specified as chart options
     * ex.
     * options: {
     *   plugins: {
     *     averageLine: {
     *       label: 10 year average,
     *       formatter: (n) => n.toFixed(2)
     *     }
     *   },
     * }
     *
     */
    averageLine?: AverageLinePluginOptions;
    centerTotal?: DoughnutChartCenterTotalPluginOptions;
  }
}
