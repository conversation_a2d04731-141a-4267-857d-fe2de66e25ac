import {type Chart} from 'chart.js';
import {type CategoryPalette} from 'src/tokens/themeTypes';
import {isDefined, isNil} from 'src/utils/typeGuards';
import {alpha, lighten, type Theme} from '@mui/material';

import {isCategoryPaletteKey, isChartConfiguration} from 'src/components/Charts/typeGuards';

export interface ColorThemesPluginOptions {
  type?:
    | 'disabled'
    | 'single'
    | 'multi'
    | 'tonal'
    | 'multi-single'
    | 'tonal-single'
    | 'trafficlight-single';
  color?: string;
  palette: Theme['palette'];
}

/**
 * Plugin which automatically generates a chart's color theme given a `type` and `palette` (MUI)
 *
 * @param type - `single`, `multi`, `tonal`, `multi-single`, `tonal-single`, `disabled` (default)
 * @param color - hex string (default is category 1 green) for chart base color. (only applicable to `single`, `tonal`, and `tonal-single` type)
 * @param palette - MUI palette (specified in design system chart components, no need to provide)
 *
 * `tonal-single` - Each dataset has a DIFFERENT tone of the base color. Each data point, within a dataset, is the SAME as the parent dataset tonal color.
 * `multi-single` - Each dataset has a DIFFERENT base color. Each data point, within a dataset, is the SAME as the parent dataset color.
 * `tonal` - Each dataset has the SAME base color. Each data point, within a dataset, is a tinted version of the parent dataset base color.
 * `multi` - Each data set uses the same set of colors. Each data point within a dataset has a DIFFERENT color
 * `single` - Each dataset and each data point, within the dataset, has the SAME base color
 * `disabled` - no colors are applied to the chart data points (default)
 */
export const ColorThemesPlugin = {
  id: 'colorThemes',
  defaults: {
    type: 'disabled',
  },

  beforeInit(chart: Chart, _args: {}, options: ColorThemesPluginOptions) {
    // default color is category palette 1 (green)
    const {palette, type: colorType, color = palette.categoryPalette['1'].chart} = options;

    // Set chart canvas color for all charts
    // This is needed to show the gaps between arcs in the doughnut chart.
    // The arc gap is acheived via a thick arc border where border color = canvas color
    // This is also used as the point style for the line chart
    // The point fill color is set to the canvas color
    const surfaceColor = palette.semanticPalette.surface.main;
    chart.ctx.fillStyle = surfaceColor;

    if (colorType === 'disabled') return;

    chart.config.data.datasets.forEach((dataset, datasetIndex) => {
      const chartType = isDefined(dataset.type)
        ? dataset.type
        : isChartConfiguration(chart.config)
        ? chart.config.type
        : '';

      let baseColor: string;
      let getDataColor: (index: number) => string;

      switch (colorType) {
        // accepts 3 datasets in order, which will render tones of green, red, neutral respectively
        // only one data point per dataset
        // use case: progress chart
        // TODO: this one needs reworking to accommodate multiple data points
        case 'trafficlight-single':
          baseColor = getTrafficColor(datasetIndex, palette);
          getDataColor = makeGetColorTint(baseColor, dataset.data.length);
          break;

        // each dataset has a DIFFERENT tone of the base color
        // each data point, within a dataset, is the SAME as the dataset color
        // use case: bar chart with multiple stacked datasets
        case 'tonal-single':
          baseColor = color;
          getDataColor = () =>
            makeGetColorTint(baseColor, chart.config.data.datasets.length)(datasetIndex);

          break;

        // each dataset has a DIFFERENT base color (category palette chart color)
        // each data point, within a dataset, is the SAME as the dataset color
        // use case: bar chart with multiple grouped datasets
        case 'multi-single':
          baseColor = getCategoryChartColor(datasetIndex, palette.categoryPalette);
          getDataColor = () => baseColor;
          break;

        // every dataset starts with the SAME base color
        // each data point, within a dataset, is a tinted version of the dataset color
        // use case: pie chart
        case 'tonal':
          baseColor = color;
          getDataColor = makeGetColorTint(baseColor, dataset.data.length);
          break;

        // each data set uses the same set of colors
        // each data point, within a dataset has a DIFFERENT color (category palette chart color)
        // use case: pie chart
        case 'multi':
          baseColor = '';
          getDataColor = index => getCategoryChartColor(index, palette.categoryPalette);
          break;

        // every dataset and data point, within the dataset, has the SAME color
        case 'single':
        default:
          baseColor = color;
          getDataColor = () => baseColor;
          break;
      }

      let borderColor: (({dataIndex}: {dataIndex: number}) => string | CanvasGradient) | undefined;
      let backgroundColor:
        | (({dataIndex}: {dataIndex: number}) => string | CanvasGradient)
        | undefined;

      switch (chartType) {
        case 'pie':
        case 'doughnut':
          backgroundColor = ({dataIndex}: {dataIndex: number}) => getDataColor(dataIndex);
          break;
        case 'scatter':
          borderColor = ({dataIndex}: {dataIndex: number}) => getDataColor(dataIndex);
          break;
        case 'line':
          const getLineChartGradient = makeGetLineChartGradient(getDataColor, chart);
          backgroundColor = ({dataIndex}: {dataIndex: number}) => getLineChartGradient(dataIndex);
          borderColor = ({dataIndex}: {dataIndex: number}) => getDataColor(dataIndex);
          break;
        case 'boxplot':
        case 'bar':
        default:
          backgroundColor = ({dataIndex}: {dataIndex: number}) => getDataColor(dataIndex);
          borderColor = ({dataIndex}: {dataIndex: number}) => getDataColor(dataIndex);
          break;
      }

      if (isNil(dataset.backgroundColor) && isDefined(backgroundColor)) {
        dataset.backgroundColor = backgroundColor;
      }

      if (isNil(dataset.borderColor) && isDefined(borderColor)) {
        dataset.borderColor = borderColor;
      }
    });
  },
};

const makeGetLineChartGradient =
  (getDataColor: (index: number) => string, chart: Chart) => (dataIndex: number) => {
    const color = getDataColor(dataIndex);
    const canvasHeight = chart.ctx.canvas.height;

    if (canvasHeight === 0) return color;

    const gradient = chart.ctx.createLinearGradient(0, 0, 0, canvasHeight / 2);
    gradient.addColorStop(0, color);
    gradient.addColorStop(0.75, alpha(color, 0.05));
    gradient.addColorStop(1, alpha(color, 0));

    return gradient;
  };

const getTrafficColor = (index: number, palette: Theme['palette']) => {
  const map = [
    palette.categoryPalette[1].chart, // green
    palette.categoryPalette[6].chart, // red
    palette.semanticPalette.surface.secondary, // neutral
  ];

  // defaults to green
  return map[index] ?? palette.categoryPalette[1].chart;
};

// starts at category 1 (not 0)
const getCategoryChartColor = (index: number, categoryPalette: CategoryPalette) => {
  const maybeCategoryPaletteKey = `${(index + 1) % (Object.keys(categoryPalette).length - 1)}`;
  const categoryPaletteKey = isCategoryPaletteKey(maybeCategoryPaletteKey, categoryPalette)
    ? maybeCategoryPaletteKey
    : '1';

  return categoryPalette[categoryPaletteKey].chart;
};

const makeGetColorTint =
  (baseColor: CategoryPalette[keyof CategoryPalette]['chart'], datasetLength: number) =>
  (index: number) =>
    lighten(baseColor, index / datasetLength);
