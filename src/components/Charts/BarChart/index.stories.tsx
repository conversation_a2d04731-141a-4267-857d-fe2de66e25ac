import type {Meta, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import {Stack, Typography} from 'src/index';

import {AverageLinePlugin as AverageLine} from 'src/components/Charts/plugins/plugin.averageLine';
import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {BarChart} from '.';
import {
  exampleChartData,
  exampleChartDataMultiDataset,
  exampleChartDataMultiDatasetMultiStack,
  exampleChartDataMultiDatasetSingleStack,
} from './story-data';

type BarChartProps = ComponentProps<typeof BarChart>;

export default {
  component: BarChart,
  title: 'components/Charts/Bar Chart',
  args: {
    data: exampleChartData,
    redraw: true,
  },
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => (
      <ChartStoryContainer>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<BarChartProps>;

export const Basic: StoryObj<BarChartProps> = {};

const colors = ['single', 'tonal', 'multi'] as const;
const stackedColors = ['tonal-single', 'multi-single'] as const;

export const BarChartColors = {
  render: () => (
    <Stack gap={25}>
      {colors.map(colorTheme => {
        const props: BarChartProps = {
          data: exampleChartData,
          redraw: true,
          options: {
            plugins: {
              colorThemes: {
                type: colorTheme,
              },
            },
          },
        };

        return (
          <Stack gap={5} height="300px" key={colorTheme}>
            <Typography variant="body2" color="secondary">
              {colorTheme}
            </Typography>
            <BarChart {...props} />
          </Stack>
        );
      })}

      {stackedColors.map(colorTheme => {
        const props: BarChartProps = {
          data: exampleChartDataMultiDatasetSingleStack,
          redraw: true,
          options: {
            plugins: {
              colorThemes: {
                type: colorTheme,
              },
            },
          },
        };

        return (
          <Stack gap={5} height="300px" key={colorTheme}>
            <Typography variant="body2" color="secondary">
              {colorTheme}
            </Typography>
            <BarChart {...props} />
          </Stack>
        );
      })}

      <Stack gap={5} height="300px">
        <Typography variant="body2" color="secondary">
          Dataset color override
        </Typography>
        <BarChart
          {...{
            data: {
              ...exampleChartDataMultiDatasetSingleStack,
              datasets: [
                ...exampleChartDataMultiDatasetSingleStack.datasets,
                {
                  label: 'Total fields 4',
                  data: [1088, 342, 50, 25, 117, 3088, 442, 20, 25, 217, 88, 242, 30, 49, 87],
                  stack: 'stack 0',
                  backgroundColor: '#f00',
                },
              ],
            },
            redraw: true,
            options: {
              plugins: {
                colorThemes: {
                  type: 'multi-single',
                },
              },
            },
          }}
        />
      </Stack>
    </Stack>
  ),
};

export const VerticalGroupedBarChart: StoryObj<BarChartProps> = {
  args: {
    data: exampleChartDataMultiDataset,
    redraw: true,
    options: {
      plugins: {
        colorThemes: {
          type: 'tonal-single',
        },
      },
    },
  },
};

export const VerticalStackedBarChart: StoryObj<BarChartProps> = {
  args: {
    data: exampleChartDataMultiDatasetSingleStack,
    redraw: true,
    options: {
      plugins: {
        colorThemes: {
          type: 'tonal-single',
        },
      },
    },
  },
};

export const VerticalStackedGroupedBarChart: StoryObj<BarChartProps> = {
  args: {
    data: exampleChartDataMultiDatasetMultiStack,
    redraw: true,
    options: {
      plugins: {
        colorThemes: {
          type: 'tonal-single',
        },
      },
    },
  },
};

export const BarChartWithTruncatedLabel: StoryObj<BarChartProps> = {
  args: {
    stacked: true,
    data: {
      ...exampleChartDataMultiDataset,
      labels: [
        'normal',
        'normal2',
        'really really really really really long',
        'normal',
        'a little long',
        'not so long, but a little',
        'something',
        'something else',
      ],
    },
    options: {
      scales: {
        x: {
          ticks: {
            // Truncates just the tick representation of a label
            // see: https://www.chartjs3.com/docs/chart/chart-js-viewers-questions/how-to-truncate-labels-in-chartjs-while-keeping-the-full-label-value-in-the-tooltips-chart-js/
            callback: function (val) {
              const label = this.getLabelForValue(Number(val));
              if (label.length > 10) {
                return `${label.substring(0, 8)}...`;
              }
              return label;
            },
          },
        },
      },
      plugins: {
        colorThemes: {
          type: 'multi-single',
        },
      },
    },
  },
};

export const ShowTooltipOnEachBarSegment: StoryObj<BarChartProps> = {
  args: {
    data: exampleChartDataMultiDatasetMultiStack,
    options: {
      plugins: {
        tooltip: {
          mode: 'point',
        },
        colorThemes: {
          type: 'tonal-single',
        },
      },
    },
  },
};

export const BarChartWithAverageLine: StoryObj<BarChartProps> = {
  args: {
    data: exampleChartData,
  },
  render: (args, {theme}) => (
    <BarChart
      {...args}
      options={{
        plugins: {
          averageLine: {
            label: 'Average',
            palette: theme.palette,
          },
        },
      }}
      plugins={[AverageLine]}
    />
  ),
};
