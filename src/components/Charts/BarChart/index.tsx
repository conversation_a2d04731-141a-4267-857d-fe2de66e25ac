import type {ChartOptions} from 'chart.js';
import {BarElement, CategoryScale, Chart, Legend, LinearScale, Tooltip} from 'chart.js';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Bar} from 'react-chartjs-2';

import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';

import type {BaseChartProps} from '../types';

// Chart.js now uses tree-shaking, so we need to import the types we want to use
Chart.register(CategoryScale, LinearScale, BarElement, Legend, Tooltip);

export type BarChartProps = BaseChartProps<'bar'> & {stacked?: boolean};

export const BarChart = ({data, options, redraw, plugins, stacked}: BarChartProps) => {
  const {
    palette,
    shape: {borderRadius},
  } = useThemeDefaults(Chart);
  const chartPlugins = usePluginDefaults(plugins);

  const chartOptions = useMemo(() => {
    const defaultOptions: ChartOptions<'bar'> = {
      indexAxis: 'x',
      scales: {
        x: {
          grid: {
            display: false,
          },
          stacked,
        },
        y: {
          title: {
            display: true,
            text: data.datasets[0]?.label,
          },
          stacked,
        },
      },
      datasets: {
        bar: {
          borderRadius,
          maxBarThickness: 80,
        },
      },
      plugins: {
        legend: {
          display: data.datasets.length > 1,
          align: 'center',
        },
        colorThemes: {
          type: 'single',
          palette,
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    // TODO replace deepMerge everywhere in the design-system, with something faster that escapes early.
    // Then we won't need to check for no options.
    return deepMerge(defaultOptions, options);
  }, [options, data.datasets, stacked, palette, borderRadius]);

  return <Bar data={data} options={chartOptions} redraw={redraw} plugins={chartPlugins} />;
};
