import type {ChartData} from 'chart.js';

const LABELS = [
  'soybeans',
  'corn',
  'fallow',
  'cotton',
  'hay',
  'soybeans',
  'corn',
  'fallow',
  'cotton',
  'hay',
  'soybeans',
  'corn',
  'fallow',
  'cotton',
  'hay',
];

export const exampleChartData: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17],
    },
  ],
  labels: LABELS,
};

export const exampleChartDataMultiDataset: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17],
    },
    {
      label: 'Total fields 2',
      data: [2030, 542, 30, 25, 29, 1088, 442, 20, 75, 57, 3088, 242, 40, 25, 170],
    },
    {
      label: 'Total fields 3',
      data: [1088, 342, 50, 25, 117, 3088, 442, 20, 25, 217, 88, 242, 30, 49, 87],
    },
  ],
  labels: LABELS,
};

export const exampleChartDataMultiDatasetSingleStack: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17],
      stack: 'stack 0',
    },
    {
      label: 'Total fields 2',
      data: [2030, 542, 30, 25, 29, 1088, 442, 20, 75, 57, 3088, 242, 40, 25, 170],
      stack: 'stack 0',
    },
    {
      label: 'Total fields 3',
      data: [1088, 342, 50, 25, 117, 3088, 442, 20, 25, 217, 88, 242, 30, 49, 87],
      stack: 'stack 0',
    },
  ],
  labels: LABELS,
};

export const exampleChartDataMultiDatasetMultiStack: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, -742, 70, 45, 17, 2088, 742, 70, 45, 17],
      stack: 'stack 1',
    },
    {
      label: 'Total fields 2',
      data: [2030, 542, 30, 25, 29, 1088, -442, 20, 75, 57, 3088, 242, 40, 25, 170],
      stack: 'stack 1',
    },
    {
      label: 'Total fields 3',
      data: [1088, 342, 50, 25, -117, 3088, 442, 20, 25, -217, 88, 242, 30, 49, -87],
      stack: 'stack 2',
    },
  ],
  labels: LABELS,
};
