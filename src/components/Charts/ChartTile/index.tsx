import React, {useCallback, useState} from 'react';
import {isString} from 'src/utils/typeGuards';
import type {PopoverOrigin} from '@mui/material';
import {Stack} from '@mui/system';

import {Box} from 'src/components/Box';
import {Button} from 'src/components/Button';
import {ButtonGroup} from 'src/components/ButtonGroup';
import {SvgIcon} from 'src/components/Icon';
import {Menu, MenuItem} from 'src/components/Menu';
import {Paper} from 'src/components/Paper';
import {Tooltip} from 'src/components/Tooltip';
import {Typography} from 'src/components/Typography';
import {TypographyOverflow} from 'src/components/TypographyOverflow';

export type MenuItems = {
  label: string | React.ReactNode;
  disabled?: boolean;
  onClick: () => void;
  preventMenuCloseOnClick?: boolean;
}[];

export interface ChartTileProps {
  title?: string;
  description?: React.ReactNode;
  tooltip?: React.ReactNode;
  /**  @deprecated use description prop instead*/
  belowTitleContent?: React.ReactNode;
  menuItems?: MenuItems;
  pagination?: React.ReactNode;
}

export const ChartTile: React.FC<ChartTileProps> = ({
  title,
  description,
  tooltip,
  belowTitleContent,
  pagination,
  menuItems,
  children,
}) => (
  <Paper fullHeight fullWidth>
    <Stack gap={2} p={3} height={1} width={1}>
      <Box display="flex" justifyContent="space-between" gap={4} alignItems={'start'}>
        <Stack gap={2}>
          {title && (
            <TypographyOverflow variant="h5" clampLines={2}>
              {title}
            </TypographyOverflow>
          )}
          {isString(description) ? (
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          ) : description ? (
            <Box>{description}</Box>
          ) : null}
        </Stack>
        <Box display="flex" alignItems="start" gap={3}>
          {!!pagination && pagination}

          <ButtonGroup sx={{opacity: 0.5}} size="small" color="secondary" variant="outlined">
            {tooltip && (
              <Tooltip title={tooltip}>
                <Button variant="outlined">
                  <SvgIcon type="question" />
                </Button>
              </Tooltip>
            )}

            <MenuButton menuItems={menuItems} />
          </ButtonGroup>
        </Box>
      </Box>
      {/* deprecated; keep prop supported until unused */}
      {belowTitleContent ? <Box mt={2}>{belowTitleContent}</Box> : null}
      <Box mt={3} flexGrow="1">
        {children}
      </Box>
    </Stack>
  </Paper>
);

const MenuButton = ({menuItems}: {menuItems?: MenuItems}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleMenuItemClick =
    (preventClose: boolean = false, action?: () => void) =>
    () => {
      action?.();
      if (!preventClose) {
        handleClose();
      }
    };

  if (!menuItems?.length) return null;

  return (
    <>
      <Button onClick={handleClick}>
        <SvgIcon type="dots-vertical" />
      </Button>
      <Menu
        anchorOrigin={MenuAnchorOrigin}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={Boolean(anchorEl)}
      >
        {menuItems.map(({label, onClick, disabled, preventMenuCloseOnClick}, i) => (
          <MenuItem
            key={`chart-tile--menuItem--${i}`}
            disabled={disabled}
            onClick={handleMenuItemClick(preventMenuCloseOnClick, onClick)}
          >
            {label}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

const MenuAnchorOrigin: PopoverOrigin = {
  vertical: 'bottom',
  horizontal: 'left',
};
