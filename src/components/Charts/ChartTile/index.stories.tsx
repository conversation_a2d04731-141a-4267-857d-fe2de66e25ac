import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {useState, type ComponentProps} from 'react';
import {BarChart, Box, ChartTile, Pagination, useChartPagination} from 'src/index';

import {exampleChartDataMultiDataset} from 'src/components/Charts/BarChart/story-data';
import {exampleChartTileArgs} from 'src/components/Charts/ChartTile/story-data';
import {ITEMS_PER_CHART_PAGE} from 'src/components/Charts/defaults';

type ChartTileProps = ComponentProps<typeof ChartTile> & {
  hasPagination: boolean;
};

const ChartTileStoryComponent = (props: ChartTileProps) => {
  const [page, setPage] = useState(0);
  const totalDataLength = exampleChartDataMultiDataset.datasets[0].data.length;

  const {paginatedData} = useChartPagination({
    data: exampleChartDataMultiDataset,
    paginated: true,
    page,
    pageSize: ITEMS_PER_CHART_PAGE,
  });

  const showPagination = props.hasPagination && totalDataLength > ITEMS_PER_CHART_PAGE;

  return (
    <Box p={2} width={theme => theme.fixedWidths.md} height={360}>
      <ChartTile
        title={props.title}
        description={props.description}
        tooltip={props.tooltip}
        belowTitleContent={props.belowTitleContent}
        menuItems={props.menuItems}
        pagination={
          showPagination ? (
            <Pagination
              onSetPage={setPage}
              page={page}
              count={totalDataLength}
              rowsPerPage={ITEMS_PER_CHART_PAGE}
            />
          ) : undefined
        }
      >
        <BarChart
          data={paginatedData}
          redraw={true}
          options={{
            plugins: {
              colorThemes: {
                type: 'multi-single',
              },
            },
          }}
        />
      </ChartTile>
    </Box>
  );
};

export default {
  component: ChartTileStoryComponent,
  title: 'components/Charts/Chart Tile',
  args: exampleChartTileArgs,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=3801-30576&mode=design&t=cjb8P1Ll4yj4YA5E-0',
    },
  },
} as Meta<ChartTileProps>;

export const Basic: StoryObj<ChartTileProps> = {};

export const LongTitle: StoryObj<ChartTileProps> = {
  args: {
    ...exampleChartTileArgs,
    title:
      'Really really really really really really really really really really really really really really really really really really really long title for the chart tile component',
  },
};
