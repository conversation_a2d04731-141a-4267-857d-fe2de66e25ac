import {SvgIcon} from 'src/components/Icon';
import {Stack} from 'src/components/Stack';
import {Typography} from 'src/components/Typography';

export const exampleChartTileArgs = {
  title: 'Sample Chart Tile Title',
  description:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. ',
  tooltip: (
    <>
      <Typography variant="h5">This is a ReactNode tooltip</Typography>
      <Typography variant="body2">
        This chart shows an example of chart in Chart tile. It includes all the possible elements
        that can be used in a chart tile. Including title, description, pagination, tooltip, menu
        button and the chart itself.
      </Typography>
    </>
  ),
  belowTitleContent: (
    <Typography variant="h6">
      DEPRECATED: This is an example of some below title content. You should use "description" prop
      instead
    </Typography>
  ),
  menuItems: [
    {
      label: (
        <Stack direction="row" gap={1} alignItems="center">
          <SvgIcon type="sort" fontSize="body2" /> Sort by value
        </Stack>
      ),
      onClick: () => alert('Item 1 clicked'),
    },
    {
      label: (
        <Stack direction="row" gap={1} alignItems="center">
          <SvgIcon type="sort" fontSize="body2" /> Sort by label
        </Stack>
      ),
      onClick: () => alert('Item 1 clicked'),
    },
    {label: 'Long text item 2', onClick: () => alert('Item 2 clicked')},
    {label: 'Disabled item 3', onClick: () => alert('Item 3 clicked'), disabled: true},
  ],
  hasPagination: true,
};
