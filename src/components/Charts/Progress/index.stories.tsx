import type {Meta, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';

import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {ProgressChart} from '.';
import {exampleChartData, getProgressChartData} from './story-data';

type ProgressChartProps = ComponentProps<typeof ProgressChart>;

export default {
  component: ProgressChart,
  args: {
    data: exampleChartData,
    redraw: true,
    formatTooltipLabel: ({formattedValue}) => `${formattedValue}%`,
  },
  title: 'components/Charts/Progress Chart',
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => (
      <ChartStoryContainer>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<ProgressChartProps>;

export const Basic: StoryObj<ProgressChartProps> = {};

// TODO: need to update to use colorTheme plugin when trafficlight type when available
export const ProgressChartWithSingleColor = {
  render: (args, {theme}) => {
    const data = getProgressChartData(theme);
    return <ProgressChart {...args} data={data} />;
  },
  args: {
    options: {
      plugins: {
        colorThemes: {
          type: 'disabled',
        },
      },
    },
  },
} as Meta<ProgressChartProps>;
