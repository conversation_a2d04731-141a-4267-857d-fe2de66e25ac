import type {ScriptableContext} from 'chart.js';

export const roundTheOuterCorners = (ctx: ScriptableContext<'bar'>, borderRadius: number) => {
  const visibleDatasetsCount = ctx.chart.getVisibleDatasetCount();

  // if there is only one element, round all corners
  if (ctx.datasetIndex === 0 && ctx.datasetIndex === visibleDatasetsCount - 1) {
    return borderRadius;
  }

  // round the left corners of the first dataset
  if (ctx.datasetIndex === 0) {
    return {topLeft: borderRadius, topRight: 0, bottomLeft: borderRadius, bottomRight: 0};
  }

  // round the right corners of the last dataset
  if (ctx.datasetIndex === visibleDatasetsCount - 1) {
    return {topLeft: 0, topRight: borderRadius, bottomLeft: 0, bottomRight: borderRadius};
  }

  return 0;
};
