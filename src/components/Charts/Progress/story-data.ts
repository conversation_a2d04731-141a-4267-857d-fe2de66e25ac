import type {ComponentProps} from 'react';
import type {Theme} from '@mui/material';

import type {ProgressChart} from 'src/components/Charts/Progress';

export const exampleChartData = {
  datasets: [
    {
      label: 'Category 1',
      data: [48],
    },
    {
      label: 'Category 2',
      data: [30],
    },
    {
      label: 'Category 3',
      data: [12],
    },
    {
      label: 'Category 4',
      data: [11],
    },
  ],
};

export const getProgressChartData = (theme: Theme) => {
  const data: ComponentProps<typeof ProgressChart>['data'] = {
    datasets: [
      {
        label: 'Category 1',
        data: [30],
        backgroundColor: [theme.palette.categoryPalette[1].chart],
      },
      {
        label: 'Category 2',
        data: [12],
        backgroundColor: [theme.palette.categoryPalette[6].chart],
      },
      {
        label: 'Category 3',
        data: [44],
        backgroundColor: [theme.palette.categoryPalette[6].highlight],
      },
      {
        label: 'Category 4',
        data: [13],
        backgroundColor: [theme.palette.semanticPalette.surface.secondary],
      },
    ],
  };

  return data;
};
