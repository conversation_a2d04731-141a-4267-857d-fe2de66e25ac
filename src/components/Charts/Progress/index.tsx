import type {
  ChartData,
  ChartOptions,
  TooltipItem,
  TooltipPosition,
  TooltipPositionerFunction,
} from 'chart.js';
import {BarElement, CategoryScale, Chart, Legend, LinearScale, Tooltip} from 'chart.js';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Bar} from 'react-chartjs-2';
import {useTheme} from '@mui/material';

import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';
import {roundTheOuterCorners} from 'src/components/Charts/Progress/utils';
import type {BaseChartProps} from 'src/components/Charts/types';

export type ProgressBarProps = Omit<BaseChartProps<'bar'>, 'data'> & {
  formatTooltipLabel?: (tooltipItem: TooltipItem<'bar'>) => string;
  data: Pick<ChartData<'bar'>, 'datasets'>;
};

// Chart.js now uses tree-shaking, so we need to import the types we want to use
Chart.register(CategoryScale, LinearScale, BarElement, Legend, Tooltip);

export const ProgressChart = ({
  data,
  options,
  redraw,
  formatTooltipLabel,
  plugins,
}: ProgressBarProps) => {
  useThemeDefaults(Chart);
  const {
    palette,
    shape: {borderRadius},
  } = useTheme();
  const chartPlugins = usePluginDefaults(plugins);

  const chartOptions = useMemo(() => {
    const defaultOptions: ChartOptions<'bar'> = {
      indexAxis: 'y',
      interaction: {
        mode: 'x',
      },
      plugins: {
        legend: {
          display: true,
          align: 'center',
        },
        tooltip: {
          position: 'progressChartCenter',
          xAlign: 'center',
          yAlign: 'top',
          callbacks: {
            title: tooltipItems => tooltipItems[0].dataset.label,
            label: formatTooltipLabel,
          },
        },
        colorThemes: {
          type: 'tonal-single',
          palette,
        },
      },
      datasets: {
        bar: {
          barThickness: 36,
          borderSkipped: false,
          borderRadius: ctx => roundTheOuterCorners(ctx, borderRadius),
        },
      },
      scales: {
        x: {
          stacked: true,
          grid: {display: false},
          border: {display: false},
          ticks: {display: false},
        },
        y: {
          stacked: true,
          grid: {display: false},
          ticks: {display: false},
          border: {display: false},
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    return deepMerge(defaultOptions, options);
  }, [options, formatTooltipLabel, borderRadius, palette]);

  const chartData = useMemo(() => {
    const defaultData: ChartData<'bar'> = {
      // An empty label is required so that the Chart actually renders
      // For this Chart we use the label tied to the data itself, check the story-data.ts file for an example.
      labels: [''],
      datasets: [],
    };

    return deepMerge(defaultData, data);
  }, [data]);

  return <Bar data={chartData} options={chartOptions} redraw={redraw} plugins={chartPlugins} />;
};

Tooltip.positioners.progressChartCenter = (items): TooltipPosition | false => {
  if (!items.length) {
    return false;
  }

  // @ts-expect-error - this is a valid option, but not in the types
  const {x, y, width} = items[0].element;

  return {
    x: x - width / 2,
    y,
  };
};

declare module 'chart.js' {
  interface TooltipPositionerMap {
    progressChartCenter: TooltipPositionerFunction<'bar'>;
  }
}
