import type {ChartData, ChartOptions, Plugin} from 'chart.js';
import {ArcElement, Chart, Tooltip} from 'chart.js';
import ChartDataLabels, {type Context} from 'chartjs-plugin-datalabels';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Doughnut} from 'react-chartjs-2';

import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';
import type {BaseChartProps} from 'src/components/Charts/types';

// Chart.js now uses tree-shaking, so we need to import the types we want to use
Chart.register(ArcElement, Tooltip);

export type DoughnutChartProps = BaseChartProps<'doughnut'>;

/**
 * Note: dataset[0].data should be in a descending order (from big -> small)
 * This is so the overlapping label logic (`datalabels.display: 'auto'`) works correctly.
 */
export const DoughnutChart = ({data, options, redraw, plugins = []}: DoughnutChartProps) => {
  const {
    palette,
    shape: {borderRadius},
  } = useThemeDefaults(Chart);
  // Casting due to known type issue with ChartDataLabels
  // https://github.com/chartjs/Chart.js/issues/10896
  const chartPlugins = usePluginDefaults([...plugins, ChartDataLabels as Plugin<'doughnut'>]);

  const chartOptions = useMemo(() => {
    const defaultOptions: ChartOptions<'doughnut'> = {
      interaction: {
        mode: 'index',
        axis: 'r',
        intersect: true,
      },
      layout: {
        padding: context => {
          // Required to prevent labels from being cut off
          // hopefully this can be removed in the future with ChartDataLabels plugin update.
          const paddingSize = context.chart.height / 10 + 10; // a manually calculated appropriate value

          return {
            left: paddingSize,
            right: paddingSize,
            top: paddingSize,
            bottom: paddingSize,
          };
        },
      },
      cutout: '70%',
      datasets: {
        doughnut: {
          spacing: 2,
          borderRadius,
          borderWidth: 4,
          hoverBorderWidth: 4,
          // The border colors below are used in conjunction with the ColorThemes plugin
          // Setting the border color to the chart surface color results in the desired visual gap between doughnut arcs
          borderColor: palette.semanticPalette.surface.main,
          hoverBorderColor: palette.semanticPalette.surface.main,
        },
      },
      plugins: {
        legend: {
          display: false,
          align: 'center',
        },
        datalabels: {
          align: 'end',
          anchor: 'end',
          display: 'auto',
          formatter: options?.plugins?.datalabels?.formatter ?? getPercentLabel,
          font: {
            size: 12,
          },
          offset: 8,
        },
        colorThemes: {
          type: 'tonal',
          palette,
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    return deepMerge(defaultOptions, options);
  }, [options, palette, borderRadius]);

  return <Doughnut data={data} options={chartOptions} redraw={redraw} plugins={chartPlugins} />;
};

function getPercentLabel(value: unknown, context: Context): string {
  // Need to check the value because it comes into the function as any from chart.js
  if (typeof value !== 'number') {
    throw new Error(`${DoughnutChart.name} - ${getPercentLabel.name}: value must be a number.`);
  }

  const chartData = context.chart.data as ChartData<'doughnut'>;

  if (chartData.datasets.length !== 1) {
    throw new Error(
      `${DoughnutChart.name} - ${getPercentLabel.name}: currently only supports a single dataset.`
    );
  }

  const label = chartData.labels?.[context.dataIndex];
  const total = chartData.datasets?.[0].data.reduce((acc, curr) => {
    return acc + curr;
  }, 0);
  const percent = ((value / total) * 100).toFixed(2); // nearest 2 decimal places

  return `${label}\n${percent}%`;
}
