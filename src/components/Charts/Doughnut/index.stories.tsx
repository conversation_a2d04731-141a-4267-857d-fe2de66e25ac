import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import {Stack, Typography, useTheme} from 'src/index';

import {DoughnutChartCenterTotalPlugin} from 'src/components/Charts/plugins/plugin.doughnutChartCenterTotal';
import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {DoughnutChart} from '.';
import {exampleChartData} from './story-data';

type DoughnutChartProps = ComponentProps<typeof DoughnutChart>;

export default {
  component: DoughnutChart,
  args: {
    data: exampleChartData,
    redraw: true,
  },
  title: 'components/Charts/Doughnut Chart',
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => (
      <ChartStoryContainer>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<DoughnutChartProps>;

export const Basic: StoryObj<DoughnutChartProps> = {};

const colors = ['single', 'tonal', 'multi'] as const;

export const DoughnutChartColors = {
  render: () => (
    <Stack gap={25}>
      {' '}
      {colors.map(colorTheme => {
        const props: DoughnutChartProps = {
          data: exampleChartData,
          redraw: true,
          options: {
            plugins: {
              colorThemes: {
                type: colorTheme,
              },
            },
          },
        };

        return (
          <Stack gap={5} height="300px" key={colorTheme}>
            <Typography variant="body2" color="secondary">
              {colorTheme}
            </Typography>
            <DoughnutChart {...props} />
          </Stack>
        );
      })}
    </Stack>
  ),
};

export const DoughnutChartWithLabelFormatter = {
  render: () => (
    <DoughnutChart
      {...{
        data: exampleChartData,
        redraw: true,
        options: {
          plugins: {
            datalabels: {
              formatter: (value, context) => [
                'Custom label line 1',
                `line 2: ${context.chart.data.labels![context.dataIndex]}:`,
                `line 3: ${value} fields`,
              ],
            },
          },
        },
      }}
    />
  ),
};

export const DoughnutChartWithCenterTotal = {
  render: () => <WithCenterTotalDoughnut />,
};

const WithCenterTotalDoughnut = () => {
  const {palette} = useTheme();
  const props: DoughnutChartProps = {
    data: exampleChartData,
    redraw: true,
    options: {
      plugins: {
        centerTotal: {
          unitLabel: 'fields',
          palette,
        },
      },
    },
    plugins: [DoughnutChartCenterTotalPlugin],
  };

  return (
    <Stack gap={5} height="300px">
      <Typography variant="body2" color="secondary">
        Use the `centerTotal` plugin to show the total within the center of the doughnut chart
      </Typography>
      <DoughnutChart {...props} />
    </Stack>
  );
};
