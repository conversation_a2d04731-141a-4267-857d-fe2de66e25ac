import type {Meta, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';

import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {ScatterPlotChart} from '.';
import {exampleScatterPlotChartData} from './story-data';

type ScatterPlotChartProps = ComponentProps<typeof ScatterPlotChart>;

export default {
  component: ScatterPlotChart,
  render: (args: ScatterPlotChartProps) => <ScatterPlotChart {...args} />,
  args: {
    data: exampleScatterPlotChartData,
    redraw: true,
    xAxisLabel: 'Emergence (Living root quality)',
    yAxisLabel: 'Coverage (Living root score %)',
    tooltipLabelX: 'Emergence',
    tooltipLabelY: 'Coverage',
    suggestedMinX: 0,
    suggestedMaxX: 7,
    suggestedMinY: 0,
    suggestedMaxY: 100,
    formatTooltipLabelY: (value: number) => `${value}%`,
  },
  title: 'components/Charts/Scatter Plot Chart',
  parameters: {
    layout: 'centered',
  },
  decorators: [
    // @ts-expect-error ScatterPlotChartProps type definition has impossible states and needs to be refactored
    Story => (
      <ChartStoryContainer>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<ScatterPlotChartProps>;

export const Basic: StoryObj<ScatterPlotChartProps> = {};
