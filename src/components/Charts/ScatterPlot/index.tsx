import type {ChartOptions, TooltipItem} from 'chart.js';
import {CategoryScale, Chart, Legend, LinearScale, PointElement, Tooltip} from 'chart.js';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Scatter} from 'react-chartjs-2';

import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';
import type {BaseChartProps} from 'src/components/Charts/types';

// Chart.js now uses tree-shaking, so we need to import the types we want to use
Chart.register(CategoryScale, LinearScale, Legend, Tooltip, PointElement);

export type ScatterPlotChartProps = BaseChartProps<'scatter'> & {
  /** The minimum item to display if there is no datapoint before it. */
  suggestedMinX?: number;
  /** The maximum item to display if there is no datapoint before it. */
  suggestedMaxX?: number;
  /** The minimum item to display if there is no datapoint before it. */
  suggestedMinY?: number;
  /** The maximum item to display if there is no datapoint before it. */
  suggestedMaxY?: number;
} & (
    | {
        formatTooltipLabel: (tooltipItem: TooltipItem<'scatter'>) => string | string[];
        xAxisLabel?: string;
        yAxisLabel?: string;
      }
    | {
        formatTooltipLabel?: never;
        xAxisLabel: string;
        yAxisLabel: string;
      }
  );

export const ScatterPlotChart = ({
  data,
  options,
  redraw,
  xAxisLabel,
  yAxisLabel,
  suggestedMinX,
  suggestedMaxX,
  suggestedMinY,
  suggestedMaxY,
  formatTooltipLabel,
  plugins,
}: ScatterPlotChartProps) => {
  const {palette} = useThemeDefaults(Chart);
  const chartPlugins = usePluginDefaults(plugins);

  const chartOptions = useMemo(() => {
    const defaultOptions: ChartOptions<'scatter'> = {
      datasets: {
        scatter: {
          borderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 4,
          pointHoverBorderWidth: 2,
          backgroundColor: 'transparent', // the backgroundColor must be transparent to make the point's center empty
        },
      },
      interaction: {
        mode: 'nearest',
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: tooltipItem =>
              formatTooltipLabel?.(tooltipItem) ?? [
                `${xAxisLabel}: ${tooltipItem.parsed.x}`,
                `${yAxisLabel}: ${tooltipItem.parsed.y}`,
              ],
          },
        },
        colorThemes: {
          type: 'single',
          palette,
        },
      },
      scales: {
        x: {
          title: {display: !!xAxisLabel, text: xAxisLabel},
          grid: {drawTicks: false},
          ticks: {padding: 10},
          suggestedMin: suggestedMinX,
          suggestedMax: suggestedMaxX,
        },
        y: {
          title: {display: !!yAxisLabel, text: yAxisLabel},
          grid: {drawTicks: false},
          ticks: {padding: 10},
          suggestedMin: suggestedMinY,
          suggestedMax: suggestedMaxY,
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    return deepMerge(defaultOptions, options);
  }, [
    formatTooltipLabel,
    options,
    suggestedMaxX,
    suggestedMaxY,
    suggestedMinX,
    suggestedMinY,
    xAxisLabel,
    yAxisLabel,
    palette,
  ]);

  return <Scatter data={data} options={chartOptions} redraw={redraw} plugins={chartPlugins} />;
};
