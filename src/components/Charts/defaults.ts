import type {Chart, ChartType, Plugin} from 'chart.js';
import {useMemo} from 'react';
import {useTheme} from '@mui/material';

import {ColorThemesPlugin} from 'src/components/Charts/plugins/plugin.colorThemes';

export const ITEMS_PER_CHART_PAGE = 10;

/**
 * Applies default chart settings to chart and returns mui theme
 */
export const useThemeDefaults = (chart: typeof Chart) => {
  const theme = useTheme();

  const memoizedTheme = useMemo(() => {
    // global defaults
    chart.defaults.maintainAspectRatio = false;
    chart.defaults.interaction.intersect = false;
    chart.defaults.interaction.mode = 'index';
    chart.defaults.animation = false;
    chart.defaults.color = theme.palette.semanticPalette.text.secondary;
    chart.defaults.font.size = Number(theme.typography.body2.fontSize);

    // scale defaults
    if (chart.defaults.scales.category) {
      chart.defaults.scales.category.border = {
        ...chart.defaults.scales.category.border,
        display: false, // disable the border by default, we use thicker zero grid line instead of border
      };

      chart.defaults.scales.category.grid = {
        ...chart.defaults.scales.category.grid,
        lineWidth: context => (context?.tick?.value === 0 ? 2 : 1), // thicker zero grid line
      };
    }

    if (chart.defaults.scales.linear) {
      chart.defaults.scales.linear.border = {
        ...chart.defaults.scales.linear.border,
        display: false, // disable the border by default, we use thicker zero grid line instead of border
      };

      chart.defaults.scales.linear.grid = {
        ...chart.defaults.scales.linear.grid,
        lineWidth: context => (context?.tick?.value === 0 ? 2 : 1), // thicker zero grid line
      };
    }

    // plugin defaults
    if (chart.defaults.plugins.legend) {
      chart.defaults.plugins.legend.display = false;

      // define default legend options for charts with enabled legend
      chart.defaults.plugins.legend.position = 'bottom';

      chart.defaults.plugins.legend.labels = {
        ...chart.defaults.plugins.legend.labels,
        pointStyle: 'circle',
        usePointStyle: true,
        boxWidth: 7,
        boxHeight: 7,
        font: {
          size: Number(theme.typography.body2.fontSize),
          lineHeight: Number(theme.typography.body2.lineHeight),
        },
      };
    }

    if (chart.defaults.plugins.tooltip) {
      chart.defaults.plugins.tooltip.backgroundColor =
        theme.palette.semanticPalette.surfaceInverted.main;
      chart.defaults.plugins.tooltip.titleColor = theme.palette.semanticPalette.textInverted.main;
      chart.defaults.plugins.tooltip.padding = 8;
      chart.defaults.plugins.tooltip.displayColors = false;
      chart.defaults.plugins.tooltip.caretSize = 8;
      chart.defaults.plugins.tooltip.cornerRadius = theme.shape.borderRadius;
    }

    return theme;
  }, [chart, theme]);

  return memoizedTheme;
};

export const usePluginDefaults = <T extends ChartType>(plugins?: Array<Plugin<T>>) =>
  useMemo(() => {
    return [ColorThemesPlugin, ...(plugins ?? [])];
  }, [plugins]);
