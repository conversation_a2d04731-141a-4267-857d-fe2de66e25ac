import type {
  ChartConfiguration,
  ChartConfigurationCustomTypesPerDataset,
  ChartDataset,
} from 'chart.js';
import {type Theme} from '@mui/material';

export const isChartConfiguration = (
  x: ChartConfiguration | ChartConfigurationCustomTypesPerDataset
): x is ChartConfiguration => 'type' in x;

export const isCategoryPaletteKey = (
  x: string,
  categoryPalette: Theme['palette']['categoryPalette']
): x is keyof Theme['palette']['categoryPalette'] => Object.hasOwn(categoryPalette, x);

export const isDataArrayOfNumbers = (data: ChartDataset['data']): data is number[] => {
  return typeof data[0] === 'number';
};
