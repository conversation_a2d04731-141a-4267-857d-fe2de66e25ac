import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import {Basic as BarChart} from './BarChart/index.stories'
import {Basic as BoxPlotChart} from './BoxPlot/index.stories'
import {Basic as DoughnutChart} from './Doughnut/index.stories'
import {Basic as HorizontalBarChart} from './HorizontalBar/index.stories'
import {Basic as LineChart} from './Line/index.stories'
import {Basic as ProgressChart} from './Progress/index.stories'
import {Basic as ScatterPlotChart} from './ScatterPlot/index.stories'
import {Basic as ChartTileStory} from './ChartTile/index.stories'


 ## Overview

Charts are graphical representations of data or data collection.
They are used to visualize and simplify complex data sets and to make data easier to read and understand. 
Each chart type is designed to address a different type of data and a different way of viewing that data.

Mainly for our charts we modify and use charts from the [chart.js](https://www.chartjs.org) library.


* [Chart.js docs](https://www.chartjs.org/docs/latest/)
* [Charts in Figma](https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=2535%3A17698&mode=dev)


**Usually when using a chart, it will be wrapped in [Chart tile component.](/?path=/story/components-charts-chart-tile--chart-tile-story)**

 The chart tile component provides a styled wrapper for a chart.
 Also it optionlaly accepts the next arguments a **title, description, menu items, tooltip** and **pagination**. 

<Canvas of={ChartTileStory} />


## Charts types (basic examples)

#### Bar chart
<Canvas of={BarChart} />

#### Boxplot chart
<Canvas of={BoxPlotChart} />

#### Doughnut chart
<Canvas of={DoughnutChart} />

#### Horizontal Bar chart
<Canvas of={HorizontalBarChart} />

#### Line chart
<Canvas of={LineChart} />

#### Progress chart
<Canvas of={ProgressChart} />

#### ScatterPlot chart
<Canvas of={ScatterPlotChart} />



