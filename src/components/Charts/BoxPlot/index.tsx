import type {ChartOptions, TooltipItem} from 'chart.js';
import {BarElement, CategoryScale, Chart as ChartJs, Legend, LinearScale, Tooltip} from 'chart.js';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Chart} from 'react-chartjs-2';

import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';
import type {BaseChartProps} from 'src/components/Charts/types';

import {BoxPlotController} from './controllers/BoxPlotController';
import {BoxAndWhiskers} from './elements/BoxAndWhiskers';

// Chart.js now uses tree-shaking, so we need to import the types we want to use
ChartJs.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Legend,
  Tooltip,
  BoxPlotController,
  BoxAndWhiskers
);

// Used forked version of sgratzl/chartjs-chart-boxplot, to adjust the plugin to our designs.
// the library is placed in src/components/charts/boxPlot/controllers & src/components/charts/boxPlot/elements
// all related to Violin chart data was removed from the folders

export type BoxPlotChartProps = BaseChartProps<'boxplot'>;

export const BoxPlotChart = ({data, options, redraw, plugins}: BoxPlotChartProps) => {
  const {palette} = useThemeDefaults(ChartJs);
  const chartPlugins = usePluginDefaults(plugins);

  const chartOptions = useMemo(() => {
    const defaultOptions: ChartOptions<'boxplot'> = {
      interaction: {
        mode: 'nearest',
      },
      datasets: {
        boxplot: {
          maxBarThickness: 80,
          meanStyle: 'dash',
          medianColor: '#fff',
          borderWidth: 1,
        },
      },
      scales: {
        x: {
          grid: {
            display: false, // hide the line between labels
          },
        },
        y: {
          offset: true,
        },
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: function (context: TooltipItem<'boxplot'>) {
              return [
                `- Min: ${Number(context.parsed.min).toFixed(1)}`,
                `- Q1: ${Number(context.parsed.q1).toFixed(1)}`,
                `- Median: ${Number(context.parsed.median).toFixed(1)}`,
                `- Q3: ${Number(context.parsed.q3).toFixed(1)}`,
                `- Max: ${Number(context.parsed.max).toFixed(1)}`,
                `- Dataset size: ${context.parsed.items.length}`,
              ];
            },
          },
        },
        legend: {
          display: false,
          align: 'center',
        },
        colorThemes: {
          type: 'single',
          palette,
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    return deepMerge(defaultOptions, options);
  }, [options, palette]);

  return (
    <Chart
      type="boxplot"
      data={data}
      options={chartOptions}
      redraw={redraw}
      plugins={chartPlugins}
    />
  );
};
