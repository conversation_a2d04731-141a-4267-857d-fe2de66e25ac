/**
 * NOTE: this is a forked file from the https://github.com/sgratzl/chartjs-chart-boxplot
 * the initial library doesn't match our design requirements, so we have to modify it
 * this file is was copied without any changes
 */

import {registry} from 'chart.js';
import type {ChartComponent, DatasetControllerChartComponent} from 'chart.js';

export default function patchController<T, TYPE>(
  type: TYPE,
  config: T,
  controller: DatasetControllerChartComponent,
  elements: ChartComponent | ChartComponent[] = [],
  scales: ChartComponent | ChartComponent[] = []
): T & {type: TYPE} {
  registry.addControllers(controller);
  if (Array.isArray(elements)) {
    registry.addElements(...elements);
  } else {
    registry.addElements(elements);
  }
  if (Array.isArray(scales)) {
    registry.addScales(...scales);
  } else {
    registry.addScales(scales);
  }
  const c = config as any;
  c.type = type;
  return c;
}
