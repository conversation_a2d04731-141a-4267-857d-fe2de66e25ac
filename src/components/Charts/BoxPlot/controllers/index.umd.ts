/**
 * NOTE: this is a forked file from the https://github.com/sgratzl/chartjs-chart-boxplot
 * the initial library doesn't match our design requirements, so we have to modify it
 * this file is almost without changes, just got rid of the Violin chart export
 */

import {registry} from 'chart.js';

import {BoxPlotController} from '.';
import {BoxAndWhiskers} from '../elements';

registry.addControllers(BoxPlotController);
registry.addElements(BoxAndWhiskers);
