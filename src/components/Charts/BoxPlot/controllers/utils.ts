/**
 * NOTE: this is a forked file from the https://github.com/sgratzl/chartjs-chart-boxplot
 * the initial library doesn't match our design requirements, so we have to modify it
 * this file is has minimal types changes
 */

export interface IBoxPlot {
  /**
   * minimum value in the given data
   */
  readonly min: number;
  /**
   * maximum value in the given data
   */
  readonly max: number;
  /**
   * median value in the given data
   */
  readonly median: number;
  /**
   * 25% quantile
   */
  readonly q1: number;
  /**
   * 75% quantile
   */
  readonly q3: number;

  /**
   * inter quantile range (q3 - q1)
   */
  readonly iqr: number;
  /**
   * whisker / fence below the 25% quantile (lower one)
   * by default is computed as the smallest element that satisfies (e >= q1 - 1.5IQR && e <= q1)
   */
  readonly whiskerLow: number;
  /**
   * whisker / fence above the 75% quantile (upper one)
   * by default is computed as the largest element that satisfies (e <= q3 + 1.5IQR && e >= q1)
   */
  readonly whiskerHigh: number;
  /**
   * outliers that are outside of the whiskers on both ends
   */
  readonly outlier: readonly number[];

  /**
   * arithmetic mean
   */
  readonly mean: number;

  /**
   * variance
   */
  readonly variance: number;

  /**
   * number of missing values (NaN, null, undefined) in the data
   */
  readonly missing: number;
  /**
   * number of values (valid + missing)
   */
  readonly count: number;
  /**
   * array like (array or typed array) of all valid items
   */
  readonly items: ArrayLike<number>;
}

export declare interface QuantileMethod {
  (arr: ArrayLike<number>, length: number): {q1: number; median: number; q3: number};
}

export declare type BoxplotStatsOptions = {
  /**
   * specify the coefficient for the whiskers, use <=0 for getting min/max instead
   * the coefficient will be multiplied by the IQR
   * @default 1.5
   */
  coef?: number;

  /**
   * specify the quantile method to use
   * @default quantilesType7
   */
  quantiles?: QuantileMethod;

  /**
   * defines that it can be assumed that the array is sorted and just contains valid numbers
   * (which will avoid unnecessary checks and sorting)
   * @default false
   */
  validAndSorted?: boolean;

  /**
   * whiskers mode whether to compute the nearest element which is bigger/smaller than low/high whisker or
   * the exact value
   * @default 'nearest'
   */
  whiskersMode?: 'nearest' | 'exact';

  /**
   * delta epsilon to compare
   * @default 10e-3
   */
  eps?: number;
};

function createSortedData(data: readonly number[] | Float32Array | Float64Array) {
  let valid = 0;
  const {length} = data;

  const vs = data instanceof Float64Array ? new Float64Array(length) : new Float32Array(length);

  for (let i = 0; i < length; i += 1) {
    const v = data[i];
    if (v === null || Number.isNaN(v)) {
      // eslint-disable-next-line no-continue
      continue;
    }
    vs[valid] = v;
    valid += 1;
  }

  const missing = length - valid;

  if (valid === 0) {
    return {
      min: Number.NaN,
      max: Number.NaN,
      missing,
      s: [],
    };
  }

  // add comparator since the polyfill doesn't to a real sorting
  const validData = valid === length ? vs : vs.subarray(0, valid);
  // sort in place
  // eslint-disable-next-line no-nested-ternary
  validData.sort((a, b) => (a === b ? 0 : a < b ? -1 : 1));

  // use real number for better precision
  const min = validData[0];
  const max = validData[validData.length - 1];

  return {
    min,
    max,
    missing,
    s: validData,
  };
}

function withSortedData(data: readonly number[] | Float32Array | Float64Array) {
  if (data.length === 0) {
    return {
      min: Number.NaN,
      max: Number.NaN,
      missing: 0,
      s: [],
    };
  }
  const min = data[0];
  const max = data[data.length - 1];

  return {
    min,
    max,
    missing: 0,
    s: data,
  };
}

function computeWhiskers(
  s: ArrayLike<number>,
  valid: number,
  min: number,
  max: number,
  {eps, quantiles, coef, whiskersMode}: Required<BoxplotStatsOptions>
) {
  const same = (a: number, b: number) => Math.abs(a - b) < eps;

  const {median, q1, q3} = quantiles(s, valid);
  const iqr = q3 - q1;
  const isCoefValid = typeof coef === 'number' && coef > 0;

  let whiskerLow = isCoefValid ? Math.max(min, q1 - coef * iqr) : min;
  let whiskerHigh = isCoefValid ? Math.min(max, q3 + coef * iqr) : max;

  const outlierLow: number[] = [];
  // look for the closest value which is bigger than the computed left
  for (let i = 0; i < valid; i += 1) {
    const v = s[i];
    if (v >= whiskerLow || same(v, whiskerLow)) {
      if (whiskersMode === 'nearest') {
        whiskerLow = v;
      }
      break;
    }
    // outlier
    if (outlierLow.length === 0 || !same(outlierLow[outlierLow.length - 1], v)) {
      outlierLow.push(v);
    }
  }
  // look for the closest value which is smaller than the computed right
  const reversedOutlierHigh: number[] = [];
  for (let i = valid - 1; i >= 0; i -= 1) {
    const v = s[i];
    if (v <= whiskerHigh || same(v, whiskerHigh)) {
      if (whiskersMode === 'nearest') {
        whiskerHigh = v;
      }
      break;
    }
    // outlier
    if (
      (reversedOutlierHigh.length === 0 ||
        !same(reversedOutlierHigh[reversedOutlierHigh.length - 1], v)) &&
      (outlierLow.length === 0 || !same(outlierLow[outlierLow.length - 1], v))
    ) {
      reversedOutlierHigh.push(v);
    }
  }
  const outlier = outlierLow.concat(reversedOutlierHigh.reverse());

  return {
    median,
    q1,
    q3,
    iqr,
    outlier,
    whiskerHigh,
    whiskerLow,
  };
}

function computeStats(s: ArrayLike<number>, valid: number) {
  let mean = 0;

  for (let i = 0; i < valid; i++) {
    const v = s[i];
    mean += v;
  }
  mean /= valid;

  let variance = 0;
  for (let i = 0; i < valid; i++) {
    const v = s[i];
    variance += (v - mean) * (v - mean);
  }
  variance /= valid;

  return {
    mean,
    variance,
  };
}

export function boxplot(
  data: readonly number[] | Float32Array | Float64Array,
  options: BoxplotStatsOptions = {}
): IBoxPlot {
  const fullOptions: Required<BoxplotStatsOptions> = {
    coef: 1.5,
    eps: 10e-3,
    quantiles: quantilesType7,
    validAndSorted: false,
    whiskersMode: 'nearest',
    ...options,
  };

  const {missing, s, min, max} = fullOptions.validAndSorted
    ? withSortedData(data)
    : createSortedData(data);

  const invalid: IBoxPlot = {
    min: Number.NaN,
    max: Number.NaN,
    mean: Number.NaN,
    missing,
    iqr: Number.NaN,
    count: data.length,
    whiskerHigh: Number.NaN,
    whiskerLow: Number.NaN,
    outlier: [],
    median: Number.NaN,
    q1: Number.NaN,
    q3: Number.NaN,
    variance: 0,
    items: [],
  };
  const valid = data.length - missing;

  if (valid === 0) {
    return invalid;
  }
  const result: Omit<IBoxPlot, 'kde'> = {
    min,
    max,
    count: data.length,
    missing,
    items: s,
    ...computeStats(s, valid),
    ...computeWhiskers(s, valid, min, max, fullOptions),
  };
  return {
    ...result,
  };
}

export interface QuantilesResult {
  q1: number;
  median: number;
  q3: number;
}

/**
 * computes the boxplot stats using the given interpolation function if needed
 * @param {number[]} arr sorted array of number
 * @param {(i: number, j: number, fraction: number)} interpolate interpolation function
 */
export function quantilesInterpolate(
  arr: ArrayLike<number>,
  length: number,
  interpolate: (i: number, j: number, fraction: number) => number
): QuantilesResult {
  const n1 = length - 1;
  const compute = (q: number) => {
    const index = q * n1;
    const lo = Math.floor(index);
    const h = index - lo;
    const a = arr[lo];

    return h === 0 ? a : interpolate(a, arr[Math.min(lo + 1, n1)], h);
  };

  return {
    q1: compute(0.25),
    median: compute(0.5),
    q3: compute(0.75),
  };
}

/**
 * Uses R's quantile algorithm type=7.
 * https://en.wikipedia.org/wiki/Quantile#Quantiles_of_a_population
 */
export function quantilesType7(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  return quantilesInterpolate(arr, length, (a, b, alpha) => a + alpha * (b - a));
}

/**
 * ‘linear’: i + (j - i) * fraction, where fraction is the fractional part of the index surrounded by i and j.
 * (same as type 7)
 */
export function quantilesLinear(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  return quantilesInterpolate(arr, length, (i, j, fraction) => i + (j - i) * fraction);
}

/**
 * ‘lower’: i.
 */
export function quantilesLower(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  return quantilesInterpolate(arr, length, i => i);
}

/**
 * 'higher': j.
 */
export function quantilesHigher(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  return quantilesInterpolate(arr, length, (_, j) => j);
}

/**
 * ‘nearest’: i or j, whichever is nearest
 */
export function quantilesNearest(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  return quantilesInterpolate(arr, length, (i, j, fraction) => (fraction < 0.5 ? i : j));
}

/**
 * ‘midpoint’: (i + j) / 2
 */
export function quantilesMidpoint(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  return quantilesInterpolate(arr, length, (i, j) => (i + j) * 0.5);
}

/**
 * The hinges equal the quartiles for odd n (where n <- length(x))
 * and differ for even n. Whereas the quartiles only equal observations
 * for n %% 4 == 1 (n = 1 mod 4), the hinges do so additionally
 * for n %% 4 == 2 (n = 2 mod 4), and are in the middle of
 * two observations otherwise.
 */
export function quantilesFivenum(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  // based on R fivenum
  const n = length;

  // assuming R 1 index system, so arr[1] is the first element
  const n4 = Math.floor((n + 3) / 2) / 2;
  const compute = (d: number) => 0.5 * (arr[Math.floor(d) - 1] + arr[Math.ceil(d) - 1]);

  return {
    q1: compute(n4),
    median: compute((n + 1) / 2),
    q3: compute(n + 1 - n4),
  };
}

/**
 * alias for quantilesFivenum
 * @param arr
 * @param length
 */
export function quantilesHinges(arr: ArrayLike<number>, length = arr.length): QuantilesResult {
  return quantilesFivenum(arr, length);
}
