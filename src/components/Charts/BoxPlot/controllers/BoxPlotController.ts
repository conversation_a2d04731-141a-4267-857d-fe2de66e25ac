/**
 * NOTE: this is a forked file from the https://github.com/sgratzl/chartjs-chart-boxplot
 * the initial library doesn't match our design requirements, so we have to modify it
 * this file is almost without changes (only import paths and some types where changed)
 */

import {BarController, CategoryScale, Chart, LinearScale} from 'chart.js';
import type {
  AnimationOptions,
  CartesianScaleTypeRegistry,
  ChartConfiguration,
  ChartItem,
  CommonHoverOptions,
  ControllerDatasetOptions,
  ScriptableAndArrayOptions,
  ScriptableContext,
} from 'chart.js';
import {merge} from 'chart.js/helpers';

import {BoxAndWhiskers} from '../elements';
import type {IBoxAndWhiskersOptions} from '../elements';
import {boxOptionsKeys} from '../elements/BoxAndWhiskers';
import {asBoxPlotStats} from './data';
import type {IBoxPlot, IBoxplotOptions} from './data';
import patchController from './patchController';
import {baseDefaults, StatsBase} from './StatsBase';

export class BoxPlotController extends StatsBase<IBoxPlot, Required<IBoxplotOptions>> {
  /**
   * @internal
   */
  // eslint-disable-next-line class-methods-use-this
  protected _parseStats(value: unknown, config: IBoxplotOptions): IBoxPlot | undefined {
    return asBoxPlotStats(value, config);
  }

  /**
   * @internal
   */
  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
  protected _transformStats<T>(target: any, source: IBoxPlot, mapper: (v: number) => T): void {
    super._transformStats(target, source, mapper);

    const keys: Array<keyof Pick<IBoxPlot, 'whiskerMax' | 'whiskerMin'>> = [
      'whiskerMin',
      'whiskerMax',
    ];

    for (const key of keys) {
      target[key] = mapper(source[key]);
    }
  }

  static readonly id = 'boxplot';

  /**
   * @internal
   */
  static readonly defaults: any = /* #__PURE__ */ merge({}, [
    BarController.defaults,
    baseDefaults(boxOptionsKeys),
    {
      animations: {
        numbers: {
          type: 'number',
          properties: (BarController.defaults as any).animations.numbers.properties.concat(
            ['q1', 'q3', 'min', 'max', 'median', 'whiskerMin', 'whiskerMax', 'mean'],
            boxOptionsKeys.filter(c => !c.endsWith('Color'))
          ),
        },
      },
      dataElementType: BoxAndWhiskers.id,
    },
  ]);

  /**
   * @internal
   */
  // static readonly overrides: any = /* #__PURE__ */ merge({}, [(BarController as any).overrides]);
}

export interface BoxPlotControllerDatasetOptions
  extends ControllerDatasetOptions,
    IBoxplotOptions,
    ScriptableAndArrayOptions<IBoxAndWhiskersOptions, ScriptableContext<'boxplot'>>,
    ScriptableAndArrayOptions<CommonHoverOptions, ScriptableContext<'boxplot'>>,
    AnimationOptions<'boxplot'> {}

export type BoxPlotDataPoint =
  | number[]
  | (Partial<IBoxPlot> & Pick<IBoxPlot, 'min' | 'max' | 'median' | 'q1' | 'q3'>);

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IBoxPlotChartOptions extends IBoxplotOptions {}

declare module 'chart.js' {
  export interface ChartTypeRegistry {
    boxplot: {
      chartOptions: IBoxPlotChartOptions;
      datasetOptions: BoxPlotControllerDatasetOptions;
      defaultDataPoint: BoxPlotDataPoint;
      scales: keyof CartesianScaleTypeRegistry;
      metaExtensions: {};
      parsedDataType: IBoxPlot & ChartTypeRegistry['bar']['parsedDataType'];
    };
  }
}

export class BoxPlotChart<
  DATA extends unknown[] = BoxPlotDataPoint[],
  LABEL = string
> extends Chart<'boxplot', DATA, LABEL> {
  static id = BoxPlotController.id;

  constructor(item: ChartItem, config: Omit<ChartConfiguration<'boxplot', DATA, LABEL>, 'type'>) {
    super(
      item,
      patchController('boxplot', config, BoxPlotController, BoxAndWhiskers, [
        LinearScale,
        CategoryScale,
      ])
    );
  }
}
