﻿import type {CartesianScaleTypeRegistry} from 'chart.js';

import type {
  BoxPlotControllerDatasetOptions,
  BoxPlotDataPoint,
  IBoxPlotChartOptions,
} from './BoxPlotController';
import type {IBoxPlot} from './data';

declare module 'chart.js' {
  export interface ChartTypeRegistry {
    boxplot: {
      chartOptions: IBoxPlotChartOptions;
      datasetOptions: BoxPlotControllerDatasetOptions;
      defaultDataPoint: BoxPlotDataPoint;
      scales: keyof CartesianScaleTypeRegistry;
      metaExtensions: {};
      parsedDataType: IBoxPlot & ChartTypeRegistry['bar']['parsedDataType'];
    };
  }
}
