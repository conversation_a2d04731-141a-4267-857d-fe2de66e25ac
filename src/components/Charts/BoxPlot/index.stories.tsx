import type {Meta, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';

import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {BoxPlotChart} from '.';
import {boxplotStoryData} from './story-data';

type BoxPlotChartProps = ComponentProps<typeof BoxPlotChart>;

export default {
  component: BoxPlotChart,
  args: {
    data: boxplotStoryData,
    redraw: true,
  },
  title: 'components/Charts/BoxPlot Chart (Box and Whiskers)',
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => (
      <ChartStoryContainer>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<BoxPlotChartProps>;

export const Basic: StoryObj<BoxPlotChartProps> = {};
