/**
 * NOTE: this is a forked file from the https://github.com/sgratzl/chartjs-chart-boxplot
 * the initial library doesn't match our design requirements, so we have to modify it
 * this is the main file where changes where applied, mostly in the _drawBoxPlotVertical() method.
 */

import {BarElement} from 'chart.js';
import type {
  ChartType,
  CommonHoverOptions,
  ScriptableAndArrayOptions,
  ScriptableContext,
} from 'chart.js';
import {lighten} from '@mui/system';

import {
  baseDefaults,
  baseOptionKeys,
  baseRoutes,
  StatsBase,
  type IStatsBaseOptions,
  type IStatsBaseProps,
} from './base';

/**
 * @internal
 */
export const boxOptionsKeys = baseOptionKeys.concat(['medianColor', 'lowerBackgroundColor']);

export interface IBoxAndWhiskersOptions extends IStatsBaseOptions {
  /**
   * separate color for the median line
   * @default 'transparent' takes the current borderColor
   * @scriptable
   * @indexable
   */
  medianColor: string;

  /**
   * color the lower half (median-q3) of the box in a different color
   * @default 'transparent' takes the current borderColor
   * @scriptable
   * @indexable
   */
  lowerBackgroundColor: string;
}

export interface IBoxAndWhiskerProps extends IStatsBaseProps {
  q1: number;
  q3: number;
  median: number;
  whiskerMin: number;
  whiskerMax: number;
  mean: number;
}

export class BoxAndWhiskers extends StatsBase<IBoxAndWhiskerProps, IBoxAndWhiskersOptions> {
  /**
   * @internal
   */
  draw(ctx: CanvasRenderingContext2D): void {
    ctx.save();

    ctx.fillStyle = this.options.backgroundColor;
    ctx.strokeStyle = this.options.borderColor;
    ctx.lineWidth = this.options.borderWidth;

    this._drawBoxPlot(ctx);
    this._drawOutliers(ctx);
    this._drawMeanDot(ctx);

    ctx.restore();

    this._drawItems(ctx);
  }

  /**
   * @internal
   */
  protected _drawBoxPlot(ctx: CanvasRenderingContext2D): void {
    if (this.isVertical()) {
      this._drawBoxPlotVertical(ctx);
    } else {
      this._drawBoxPlotHorizontal(ctx);
    }
  }

  /**
   * @internal
   */
  protected _drawBoxPlotVertical(ctx: CanvasRenderingContext2D): void {
    const {options} = this;
    const props = this.getProps(['x', 'width', 'q1', 'q3', 'median', 'whiskerMin', 'whiskerMax']);

    const {x, width, median, q1, q3, whiskerMin, whiskerMax} = props;
    const x0 = x - width / 2;

    // Draw the top box
    ctx.beginPath();

    if (q3 > q1) {
      ctx.roundRect(x0, q1, width, median - q1, [0, 0, 4, 4]);
    } else {
      ctx.roundRect(x0, q3, width, median - q3, [4, 4, 0, 0]);
    }
    ctx.fill();

    // Draw the median line
    if (options.medianColor && options.medianColor !== 'transparent') {
      ctx.strokeStyle = options.medianColor;
    }

    ctx.lineWidth = 3; // median line height
    ctx.beginPath();
    ctx.moveTo(x0, median);
    ctx.lineTo(x0 + width, median);
    ctx.stroke();

    // Draw the bottom box
    if (options.lowerBackgroundColor && options.lowerBackgroundColor !== 'transparent') {
      ctx.fillStyle = options.lowerBackgroundColor;
    } else {
      ctx.fillStyle = lighten(options.backgroundColor, 0.4);
    }

    if (q3 > q1) {
      ctx.roundRect(x0, median, width, q3 - median, [0, 0, 4, 4]);
    } else {
      ctx.roundRect(x0, median, width, q1 - median, [0, 0, 4, 4]);
    }
    ctx.fill();

    // Draw the border around the the whole box
    // ctx.strokeStyle = options.borderColor;
    // ctx.lineWidth = options.borderWidth;

    // if (q3 > q1) {
    //   ctx.strokeRect(x0, q1, width, q3 - q1);
    // } else {
    //   ctx.strokeRect(x0, q3, width, q1 - q3);
    // }

    // Draw the whiskers
    ctx.restore();

    ctx.beginPath();
    ctx.strokeStyle = options.backgroundColor;

    // bottom horizontal whisker
    ctx.moveTo(x - 15, whiskerMin);
    ctx.lineTo(x + 15, whiskerMin);

    // bottom vertical whisker
    ctx.moveTo(x, whiskerMin);
    ctx.lineTo(x, q1 + 2);

    // top vertical whisker
    ctx.moveTo(x - 15, whiskerMax);
    ctx.lineTo(x + 15, whiskerMax);

    // top horizontal whisker
    ctx.moveTo(x, whiskerMax);
    ctx.lineTo(x, q3 - 2);

    ctx.stroke();
  }

  /**
   * @internal
   */
  protected _drawBoxPlotHorizontal(ctx: CanvasRenderingContext2D): void {
    const {options} = this;
    const props = this.getProps(['y', 'height', 'q1', 'q3', 'median', 'whiskerMin', 'whiskerMax']);

    const {y, height, q1, q3, median, whiskerMin, whiskerMax} = props;
    const y0 = y - height / 2;

    // Draw the q1>q3 box
    if (q3 > q1) {
      ctx.fillRect(q1, y0, q3 - q1, height);
    } else {
      ctx.fillRect(q3, y0, q1 - q3, height);
    }

    // Draw the median line
    ctx.save();
    if (options.medianColor && options.medianColor !== 'transparent') {
      ctx.strokeStyle = options.medianColor;
    }
    ctx.beginPath();
    ctx.moveTo(median, y0);
    ctx.lineTo(median, y0 + height);
    ctx.closePath();
    ctx.stroke();
    ctx.restore();

    ctx.save();
    // fill the part below the median with lowerColor
    if (options.lowerBackgroundColor && options.lowerBackgroundColor !== 'transparent') {
      ctx.fillStyle = options.lowerBackgroundColor;
      if (q3 > q1) {
        ctx.fillRect(median, y0, q3 - median, height);
      } else {
        ctx.fillRect(median, y0, q1 - median, height);
      }
    }
    ctx.restore();

    // Draw the border around the main q1>q3 box
    if (q3 > q1) {
      ctx.strokeRect(q1, y0, q3 - q1, height);
    } else {
      ctx.strokeRect(q3, y0, q1 - q3, height);
    }

    // Draw the whiskers
    ctx.beginPath();
    ctx.moveTo(whiskerMin, y0);
    ctx.lineTo(whiskerMin, y0 + height);
    ctx.moveTo(whiskerMin, y);
    ctx.lineTo(q1, y);
    ctx.moveTo(whiskerMax, y0);
    ctx.lineTo(whiskerMax, y0 + height);
    ctx.moveTo(whiskerMax, y);
    ctx.lineTo(q3, y);
    ctx.closePath();
    ctx.stroke();
  }

  /**
   * @internal
   */
  _getBounds(useFinalPosition?: boolean): {
    left: number;
    top: number;
    right: number;
    bottom: number;
  } {
    const vert = this.isVertical();
    if (this.x === null) {
      return {
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
      };
    }

    if (vert) {
      const {x, width, whiskerMax, whiskerMin} = this.getProps(
        ['x', 'width', 'whiskerMin', 'whiskerMax'],
        useFinalPosition
      );
      const x0 = x - width / 2;
      return {
        left: x0,
        top: whiskerMax,
        right: x0 + width,
        bottom: whiskerMin,
      };
    }
    const {y, height, whiskerMax, whiskerMin} = this.getProps(
      ['y', 'height', 'whiskerMin', 'whiskerMax'],
      useFinalPosition
    );
    const y0 = y - height / 2;
    return {
      left: whiskerMin,
      top: y0,
      right: whiskerMax,
      bottom: y0 + height,
    };
  }

  static id = 'boxandwhiskers';

  /**
   * @internal
   */
  static defaults = /* #__PURE__ */ {
    ...BarElement.defaults,
    ...baseDefaults,
    medianColor: 'transparent',
    lowerBackgroundColor: 'transparent',
  };

  /**
   * @internal
   */
  static defaultRoutes = /* #__PURE__ */ {...BarElement.defaultRoutes, ...baseRoutes};
}

declare module 'chart.js' {
  export interface ElementOptionsByType<TType extends ChartType> {
    boxplot: ScriptableAndArrayOptions<
      IBoxAndWhiskersOptions & CommonHoverOptions,
      ScriptableContext<TType>
    >;
  }
}
