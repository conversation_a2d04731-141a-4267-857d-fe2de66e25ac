/**
 * NOTE: this is a forked file from the https://github.com/sgratzl/chartjs-chart-boxplot
 * the initial library doesn't match our design requirements, so we have to modify it
 * changes in the file: only Violin (completely removed chart type) exports were removed
 */
export {type IStatsBaseOptions, type IStatsBaseProps, StatsBase} from './base';
export {
  BoxAndWhiskers,
  type IBoxAndWhiskerProps,
  type IBoxAndWhiskersOptions,
} from './BoxAndWhiskers';
