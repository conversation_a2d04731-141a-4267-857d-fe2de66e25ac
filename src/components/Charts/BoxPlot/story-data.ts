import type {ChartData} from 'chart.js';

function randomValues(count: number, min: number, max: number) {
  const delta = max - min;
  return Array.from({length: count}).map(() => Math.random() * delta + min);
}

export const boxplotStoryData: ChartData<'boxplot'> = {
  labels: ['Category 1', 'Category 2'],
  datasets: [
    {
      label: 'Mocked values',
      data: [randomValues(100, 30, 45), randomValues(85, 20, 35)],
    },
  ],
};
