import type {Meta, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';

import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {HorizontalBarChart} from '.';
import {
  exampleChartData,
  exampleChartDataHorizontalGrouped,
  exampleChartDataHorizontalStacked,
} from './story-data';

type HorizontalBarChartProps = ComponentProps<typeof HorizontalBarChart>;

export default {
  component: HorizontalBarChart,
  args: {
    data: exampleChartData,
    redraw: true,
  },
  title: 'components/Charts/Horizontal Bar Chart',
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => (
      <ChartStoryContainer>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<HorizontalBarChartProps>;

export const Basic: StoryObj<HorizontalBarChartProps> = {};

export const HorizontalStackedBarChart: StoryObj<HorizontalBarChartProps> = {
  args: {
    data: exampleChartDataHorizontalStacked,
    redraw: true,
    stacked: true,
    options: {
      plugins: {
        colorThemes: {
          type: 'tonal-single',
        },
      },
    },
  },
};

export const HorizontalGroupedBarChart: StoryObj<HorizontalBarChartProps> = {
  args: {
    data: exampleChartDataHorizontalGrouped,
    redraw: true,
    options: {
      plugins: {
        colorThemes: {
          type: 'multi-single',
        },
      },
    },
  },
};
