import type {ChartData} from 'chart.js';

export const exampleChartData: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17],
    },
  ],
  labels: [
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
  ],
};

export const exampleChartDataExternalPagination: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17],
    },
    {
      label: 'Total fields 2',
      data: [1088, 342, 50, 25, 117, 3088, 442, 20, 25, 217, 88, 242, 30, 49, 87],
    },
  ],
  labels: [
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
  ],
};

export const exampleChartDataHorizontalStacked: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17],
    },
    {
      label: 'Total fields 2',
      data: [1088, 342, 50, 25, 117, 3088, 442, 20, 25, 217, 88, 242, 30, 49, 87],
    },
  ],
  labels: [
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
  ],
};

export const exampleChartDataHorizontalGrouped: ChartData<'bar'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17, 2088, 742, 70, 45, 17],
      stack: 'stack 1',
    },
    {
      label: 'Total fields 2',
      data: [1088, 342, 50, 25, 117, 3088, 442, 20, 25, 217, 88, 242, 30, 49, 87],
      stack: 'stack 2',
    },
  ],
  labels: [
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
    'soybeans',
    'corn',
    'fallow',
    'cotton',
    'hay',
  ],
};
