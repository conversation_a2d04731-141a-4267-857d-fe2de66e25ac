import type {ChartOptions} from 'chart.js';
import {BarElement, CategoryScale, Chart, Legend, LinearScale, Tooltip} from 'chart.js';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Bar} from 'react-chartjs-2';

import type {BarChartProps} from 'src/components/Charts/BarChart';
import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';

// Chart.js now uses tree-shaking, so we need to import the types we want to use
Chart.register(CategoryScale, LinearScale, BarElement, Legend, Tooltip);

export type HorizontalBarChartProps = BarChartProps;

export const HorizontalBarChart = ({
  data,
  options,
  redraw,
  plugins,
  stacked,
}: HorizontalBarChartProps) => {
  const {
    palette,
    shape: {borderRadius},
  } = useThemeDefaults(Chart);
  const chartPlugins = usePluginDefaults(plugins);

  const chartOptions = useMemo(() => {
    const defaultOptions: ChartOptions<'bar'> = {
      indexAxis: 'y',
      interaction: {
        axis: 'y',
      },
      scales: {
        y: {
          grid: {
            display: false,
          },
          stacked,
        },
        x: {
          title: {
            display: true,
            text: data.datasets[0]?.label,
          },
          stacked,
        },
      },
      datasets: {
        bar: {
          maxBarThickness: 56,
          borderRadius,
        },
      },
      plugins: {
        legend: {
          display: data.datasets.length > 1,
          align: 'center',
        },
        colorThemes: {
          type: 'single',
          palette,
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    return deepMerge(defaultOptions, options);
  }, [options, data.datasets, stacked, palette, borderRadius]);

  return <Bar data={data} options={chartOptions} redraw={redraw} plugins={chartPlugins} />;
};
