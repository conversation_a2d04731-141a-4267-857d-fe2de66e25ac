import type {Meta, StoryObj} from '@storybook/react';

import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {TimeTrendChart} from '.';
import {exampleChartData} from './story-data';

export default {
  component: TimeTrendChart,
  args: {
    data: exampleChartData,
    redraw: true,
    unitLabel: 'hectares',
  },
  title: 'components/Charts/Time Trend Chart',
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => (
      <ChartStoryContainer width={425} height={185}>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<typeof TimeTrendChart>;

export const Basic: StoryObj<typeof TimeTrendChart> = {};
