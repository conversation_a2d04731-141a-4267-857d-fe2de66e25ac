import type {ChartDataset, DefaultDataPoint, TooltipItem} from 'chart.js';
import {
  CategoryScale,
  Chart,
  Filler,
  LinearScale,
  LineElement,
  PointElement,
  Tooltip,
} from 'chart.js';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Line} from 'react-chartjs-2';

import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';
import {AverageLinePlugin as AverageLine} from 'src/components/Charts/plugins/plugin.averageLine';
import type {BaseChartProps} from 'src/components/Charts/types';

// Chart.js now uses tree-shaking, so we need to import the types we want to use
Chart.register(CategoryScale, LinearScale, LineElement, PointElement, Filler, Tooltip);

interface TimeTrendData {
  labels: Array<string>;
  datasets: [ChartDataset<'line', DefaultDataPoint<'line'>>];
}

export interface TimeTrendChartProps extends BaseChartProps<'line'> {
  data: TimeTrendData;
  unitLabel: string;
  formatter?: (v: number) => string;
}

export const TimeTrendChart = (props: TimeTrendChartProps) => {
  const {
    data: {labels},
    data: {
      datasets: {
        0: {data: dataset},
      },
    },
    formatter: optionalFormatter,
    unitLabel,
    redraw,
    options,
    data,
    plugins = [],
  } = props;

  const {palette} = useThemeDefaults(Chart);
  const chartPlugins = usePluginDefaults([...plugins, AverageLine]);

  const chartOptions = useMemo(() => {
    const defaultFormatter = (value: number) =>
      `${Intl.NumberFormat('en-US', {
        notation: 'compact',
      }).format(value)}`;

    const formatter = optionalFormatter || defaultFormatter;

    const formatterWithUnit = (value: number) => `${formatter(value)} ${unitLabel}`;

    const xAxisTickFormatter = (_label: string | number, index: number) => {
      const isFirstOrLast = index === 0 || index === dataset.length - 1;
      return isFirstOrLast ? `${labels?.[index]}` : '';
    };

    const yAxisTickFormatter = (label: string | number) => formatter(Number(label));

    const tooltipFormatter = (label: string, value: number) =>
      `${label}: ${formatterWithUnit(value)}`;

    const defaultOptions: TimeTrendChartProps['options'] = {
      datasets: {
        line: {
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: 'start',
          pointRadius: 4,
          pointBorderWidth: 2,
          pointBorderColor: 'transparent',
          pointBackgroundColor: 'transparent',
          pointHoverBackgroundColor: palette.semanticPalette.surface.main,
          pointHoverBorderColor: palette.categoryPalette[1].chart,
          tension: 0.2,
        },
      },
      scales: {
        x: {
          ticks: {
            callback: xAxisTickFormatter,
          },
          grid: {
            display: false,
          },
        },
        y: {
          title: {
            display: false,
          },
          ticks: {
            callback: yAxisTickFormatter,
          },
        },
      },
      plugins: {
        colorThemes: {
          type: 'single',
          palette,
        },
        averageLine: {
          label: `YoY average`,
          palette,
          formatter: formatterWithUnit,
        },
        tooltip: {
          caretPadding: 8,
          callbacks: {
            label: ({chart, dataIndex, parsed}: TooltipItem<'line'>) =>
              tooltipFormatter((chart.data?.labels?.[dataIndex] ?? '-') as string, parsed.y), // data type is redefined in component type so labels will always be strings or undefined
            title: () => '', // removes title
          },
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    return deepMerge(defaultOptions, options);
  }, [options, dataset.length, labels, optionalFormatter, unitLabel, palette]);

  return <Line data={data} options={chartOptions} redraw={redraw} plugins={chartPlugins} />;
};
