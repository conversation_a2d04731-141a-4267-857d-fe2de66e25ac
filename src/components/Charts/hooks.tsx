import type {ChartData, ChartType} from 'chart.js';
import {useMemo} from 'react';

function getPaginatedData<T>(page: number, data: T, pageSize: number): T {
  if (!data || !Array.isArray(data)) {
    return data;
  }

  return data.length > pageSize ? ([...data].splice(page * pageSize, pageSize) as T) : data;
}

export function useChartPagination<T extends ChartType>({
  data,
  paginated,
  page,
  pageSize,
}: {
  data: ChartData<T>;
  paginated: boolean;
  page: number;
  pageSize: number;
}) {
  const paginatedData = useGetPaginatedChartData(data, page, paginated, pageSize);

  return useMemo(
    () => ({
      page,
      paginatedData,
    }),
    [page, paginated, paginatedData]
  );
}

function useGetPaginatedChartData<T extends ChartType>(
  data: ChartData<T>,
  page: number,
  paginated = false,
  pageSize: number
): ChartData<T> {
  const paginatedData = useMemo((): ChartData<T> => {
    return {
      datasets: data.datasets.map(d => ({
        ...d,
        data: getPaginatedData(page, d.data, pageSize),
      })),
      labels: getPaginatedData(page, data.labels, pageSize),
    };
  }, [data.datasets, data.labels, page, pageSize]);

  return paginated ? paginatedData : data;
}
