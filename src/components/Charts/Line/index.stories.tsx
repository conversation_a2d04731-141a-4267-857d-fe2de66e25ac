import type {Meta, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import {Stack, Typography} from 'src/index';

import {AverageLinePlugin as AverageLine} from 'src/components/Charts/plugins/plugin.averageLine';
import {ChartStoryContainer} from 'src/components/Charts/story-helpers';

import {LineChart} from '.';
import {exampleAverageData, exampleChartData, exampleChartDataMultipleDatasets} from './story-data';

type LineChartProps = ComponentProps<typeof LineChart>;

export default {
  component: LineChart,
  args: {
    data: exampleChartData,
    redraw: true,
  },
  title: 'components/Charts/Line Chart',
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => (
      <ChartStoryContainer>
        <Story />
      </ChartStoryContainer>
    ),
  ],
} as Meta<LineChartProps>;

export const Basic: StoryObj<LineChartProps> = {};

const colors = ['single'] as const;
const multicolors = ['multi-single', 'tonal-single'] as const;

export const LineChartColors = {
  render: () => (
    <Stack gap={25}>
      {colors.map(colorTheme => {
        const props: LineChartProps = {
          data: exampleChartData,
          redraw: true,
          options: {
            plugins: {
              colorThemes: {
                type: colorTheme,
              },
            },
          },
        };

        return (
          <Stack gap={5} height="300px" key={colorTheme}>
            <Typography variant="body2" color="secondary">
              {colorTheme}
            </Typography>
            <LineChart {...props} />
          </Stack>
        );
      })}
      {multicolors.map(colorTheme => {
        const props: LineChartProps = {
          data: exampleChartDataMultipleDatasets,
          redraw: true,
          options: {
            plugins: {
              colorThemes: {
                type: colorTheme,
              },
            },
          },
        };

        return (
          <Stack gap={5} height="300px" key={colorTheme}>
            <Typography variant="body2" color="secondary">
              {colorTheme}
            </Typography>
            <LineChart {...props} />
          </Stack>
        );
      })}
    </Stack>
  ),
};

export const LineChartMultipleDatasets: StoryObj<LineChartProps> = {
  args: {
    data: exampleChartDataMultipleDatasets,
    redraw: true,
    options: {
      plugins: {
        colorThemes: {
          type: 'tonal-single',
        },
      },
    },
  },
};

export const LineChartWithAverageLine: StoryObj<LineChartProps> = {
  args: {
    data: exampleAverageData,
  },
  render: (args, {theme}) => (
    <LineChart
      {...args}
      options={{
        plugins: {
          averageLine: {
            label: 'Average',
            palette: theme.palette,
          },
        },
      }}
      plugins={[AverageLine]}
    />
  ),
};
