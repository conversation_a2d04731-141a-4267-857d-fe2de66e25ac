import type {ChartOptions} from 'chart.js';
import {
  CategoryScale,
  Chart,
  Filler,
  LinearScale,
  LineElement,
  PointElement,
  Tooltip,
} from 'chart.js';
import deepMerge from 'deepmerge';
import {useMemo} from 'react';
import {Line} from 'react-chartjs-2';

import {usePluginDefaults, useThemeDefaults} from 'src/components/Charts/defaults';
import type {BaseChartProps} from 'src/components/Charts/types';

// Chart.js now uses tree-shaking, so we need to import the types we want to use
Chart.register(CategoryScale, LinearScale, LineElement, PointElement, Filler, Tooltip);

export type LineChartProps = BaseChartProps<'line'>;

export const LineChart = ({data, options, redraw, plugins}: LineChartProps) => {
  const {palette} = useThemeDefaults(Chart);
  const chartPlugins = usePluginDefaults(plugins);

  const chartOptions = useMemo(() => {
    const defaultOptions: ChartOptions<'line'> = {
      scales: {
        x: {
          grid: {
            display: false,
          },
        },
        y: {
          title: {
            display: true,
            text: data.datasets[0]?.label,
          },
        },
      },
      datasets: {
        line: {
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: 'start',
          pointRadius: 4,
          pointBorderWidth: 2,
          pointHoverRadius: 6,
          pointHoverBorderWidth: 2,
          // The background colors below are used in conjunction with the ColorThemes plugin
          // Setting the background color to the chart surface color results in the desired "no fill" but not transparent look of line points
          pointBackgroundColor: palette.semanticPalette.surface.main,
          pointHoverBackgroundColor: palette.semanticPalette.surface.main,
          tension: 0.4, // known issue caused by curve-fit tension: https://github.com/chartjs/Chart.js/issues/11310
        },
      },
      plugins: {
        legend: {
          display: data.datasets.length > 1,
          align: 'center',
        },
        colorThemes: {
          type: 'single',
          palette,
        },
      },
    };

    if (!options) {
      return defaultOptions;
    }

    return deepMerge(defaultOptions, options);
  }, [options, data.datasets, palette]);

  return <Line data={data} options={chartOptions} redraw={redraw} plugins={chartPlugins} />;
};
