import type {ChartData} from 'chart.js';

export const exampleChartData: ChartData<'line'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [1, 1, 5, 25, 150, 500, 2000],
    },
  ],
  labels: ['Jun', 'Jul', 'Aug', 'Oct', 'Sep', 'Nov', 'Dec'],
};

export const exampleChartDataMultipleDatasets: ChartData<'line'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [1, 1, 5, 25, 150, 500, 2000],
    },
    {
      label: 'Total fields 2',
      data: [1, 2, 7, 25, 50, 300, 5000],
    },
  ],
  labels: ['Jun', 'Jul', 'Aug', 'Oct', 'Sep', 'Nov', 'Dec'],
};

export const exampleAverageData: ChartData<'line'> = {
  datasets: [
    {
      label: 'Total fields',
      data: [125, 75, 105, 50, 150, 50, 100],
    },
  ],
  labels: ['Jun', 'Jul', 'Aug', 'Oct', 'Sep', 'Nov', 'Dec'],
};
