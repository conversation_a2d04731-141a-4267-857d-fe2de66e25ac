import {styled} from '@mui/material';

export const StyledDatePicker = styled('div')`
  .react-datepicker {
    border: 0;
    box-shadow: ${({theme}) => {
      return theme.shadows[theme.boxShadows.sm];
    }};
  }
  .react-datepicker__input-container .react-datepicker__calendar-icon {
    position: absolute;
    padding: 0.5rem;
    padding: 0;
    top: 6px;
    left: 6px;
  }
  .react-datepicker__month-container {
    padding: ${({theme}) => theme.spacing(1)};
  }

  .react-datepicker__header {
    background-color: transparent;
    border-bottom: none;
    padding: ${({theme}) => theme.spacing(3)} 0 ${({theme}) => theme.spacing(3)};
  }
  .react-datepicker__day--keyboard-selected,
  .react-datepicker__month-text--keyboard-selected,
  .react-datepicker__quarter-text--keyboard-selected,
  .react-datepicker__year-text--keyboard-selected {
    background-color: ${({theme}) => theme.palette.primary.main};
    color: ${({theme}) => theme.palette.primary.contrastText};
    &:hover {
      background-color: ${({theme}) => theme.palette.primary.dark};
    }
  }
  .react-datepicker__day--outside-month {
    color: ${({theme}) => theme.palette.text.disabled};
  }
  .react-datepicker__day-name {
    color: ${({theme}) => theme.palette.text.secondary};
  }
  .react-datepicker__day-name,
  .react-datepicker__day,
  .react-datepicker__time-name {
    width: 2.25rem;
    line-height: 2.25rem;
    border: 1px solid transparent;
  }
  .react-datepicker__day--selected,
  .react-datepicker__day--in-selecting-range,
  .react-datepicker__month-text--selected,
  .react-datepicker__month-text--in-selecting-range,
  .react-datepicker__month-text--in-range,
  .react-datepicker__quarter-text--selected,
  .react-datepicker__quarter-text--in-selecting-range,
  .react-datepicker__quarter-text--in-range,
  .react-datepicker__year-text--selected,
  .react-datepicker__year-text--in-selecting-range,
  .react-datepicker__year-text--in-range {
    background-color: ${({theme}) => theme.palette.primary.main};
  }
  .react-datepicker__day--selected:hover,
  .react-datepicker__day--in-selecting-range:hover,
  .react-datepicker__day--in-range:hover,
  .react-datepicker__month-text--selected:hover,
  .react-datepicker__month-text--in-selecting-range:hover,
  .react-datepicker__month-text--in-range:hover,
  .react-datepicker__quarter-text--selected:hover,
  .react-datepicker__quarter-text--in-selecting-range:hover,
  .react-datepicker__quarter-text--in-range:hover,
  .react-datepicker__year-text--selected:hover,
  .react-datepicker__year-text--in-selecting-range:hover,
  .react-datepicker__year-text--in-range:hover,
  .react-datepicker__day:hover {
    background-color: ${({theme}) => theme.palette.semanticPalette.surface.secondary};
    border: 1px solid ${({theme}) => theme.palette.semanticPalette.stroke.main};
    color: ${({theme}) => theme.palette.semanticPalette.text.main};
  }
  .react-datepicker__day.react-datepicker__day--in-selecting-range,
  .react-datepicker__day--in-range,
  .react-datepicker__day--in-selecting-range,
  .react-datepicker__day--in-selecting-range-start,
  .react-datepicker__day--in-selecting-range-end {
    background-color: ${({theme}) => theme.palette.semanticPalette.surface.success};
    color: ${({theme}) => theme.palette.semanticPalette.text.main};
  }
  .react-datepicker__day.react-datepicker__day--selected.react-datepicker__day--selecting-range-start,
  .react-datepicker__day.react-datepicker__day--selected.react-datepicker__day--range-start,
  .react-datepicker__day--range-end {
    background-color: ${({theme}) => theme.palette.primary.main};
    color: ${({theme}) => theme.palette.semanticPalette.textInverted.main};
  }
`;
