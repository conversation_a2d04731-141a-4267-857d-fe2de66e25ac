import {useState} from 'react';
import type {ReactDatePickerProps} from 'react-datepicker';
import {FormControl, InputAdornment, MenuItem, Select, styled, TextField} from '@mui/material';

import {Icon} from 'src/components/Icon';

import {DatePicker} from '.';

const StyledSelect = styled(Select)`
  font-weight: 600;
  .MuiSelect-select {
    padding: 0.5rem;
  }
`;

export const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const years = [2022, 2023, 2024];

export const SelectExample = ({
  options,
  onChange,
  value,
}: {
  options: string[] | number[];
  onChange: any;
  value: any;
}) => {
  return (
    <FormControl fullWidth>
      <StyledSelect
        id="demo-simple-select"
        value={value}
        onChange={onChange}
        IconComponent={e => {
          return (
            <Icon
              className={e.className}
              type="chevron-down"
              style={{
                top: '25%',
              }}
            />
          );
        }}
      >
        {options.map(option => (
          <MenuItem
            key={option}
            value={option}
            className="date-select-option react-datepicker-ignore-onclickoutside"
          >
            {option}
          </MenuItem>
        ))}
      </StyledSelect>
    </FormControl>
  );
};

export const CustomDatePicker = (props: ReactDatePickerProps) => {
  const [startDate, setStartDate] = useState(props.startDate ?? new Date());
  const [endDate, setEndDate] = useState(props.endDate ?? new Date());
  const [state] = useState(false);
  const onChange = (dates: any) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
  };
  return (
    <DatePicker
      {...props}
      startDate={startDate}
      endDate={endDate}
      onChange={onChange}
      open={state}
      selected={startDate}
      selectsRange={true}
    />
  );
};

export const SingleDatePicker = (props: ReactDatePickerProps) => {
  const [startDate, setStartDate] = useState(props.startDate ?? new Date());
  const [state, setState] = useState(false);
  const onChange = (date: Date) => {
    setStartDate(date);
  };
  return (
    <DatePicker
      {...props}
      startDate={startDate}
      onChange={onChange}
      open={state}
      onInputClick={() => setState(true)}
      selected={startDate}
    />
  );
};

export const CustomInput = (props: any) => {
  return (
    <div>
      <TextField
        onClick={props.onClick}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Icon type="calendar" />
            </InputAdornment>
          ),
        }}
        value={props.value}
        id="outlined-basic"
        variant="outlined"
      />
    </div>
  );
};

export const InlineDatePicker = (props: ReactDatePickerProps) => {
  const [startDate, setStartDate] = useState(props.startDate ?? new Date());
  const [endDate, setEndDate] = useState(props.endDate ?? new Date());
  const onChange = (dates: any) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
  };
  return (
    <DatePicker
      {...props}
      startDate={startDate}
      endDate={endDate}
      onChange={onChange}
      selected={startDate}
      selectsRange={true}
    />
  );
};
