import type {<PERSON>a, StoryObj} from '@storybook/react';
import {getMonth, getYear} from 'date-fns';
import {Box, Button, ButtonGroup, Icon} from 'src/index';

import {DatePicker} from './';
import {
  CustomDatePicker,
  CustomInput,
  InlineDatePicker,
  months,
  SelectExample,
  SingleDatePicker,
  years,
} from './story-helpers';

type Story = StoryObj<typeof DatePicker>;

export default {
  component: DatePicker,
  title: 'components/Inputs/DatePicker',
} as Meta<typeof DatePicker>;

export const Basic: Story = {
  parameters: {
    controls: {exclude: /^component*/},
  },
  args: {
    inline: true,
    startDate: new Date(),
    endDate: new Date(),
    customInput: <CustomInput />,
    selectsRange: true,
    minDate: new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
    maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    renderCustomHeader: ({
      date,
      changeYear,
      changeMonth,
      decreaseMonth,
      increaseMonth,
      prevMonthButtonDisabled,
      nextMonthButtonDisabled,
    }) => {
      return (
        <Box display="flex" px={2} width="100%" justifyContent={'space-between'}>
          <Box display="flex">
            <Box maxWidth={115} minWidth={115} marginRight={2}>
              <SelectExample
                value={months[getMonth(date)]}
                onChange={({target: {value}}: {target: {value: any}}) => {
                  changeMonth(months.indexOf(value));
                }}
                options={months}
              />
            </Box>
            <Box width={80}>
              <SelectExample
                value={getYear(date)}
                onChange={({target: {value}}: {target: {value: any}}) =>
                  changeYear(parseInt(value))
                }
                options={years}
              />
            </Box>
          </Box>
          <Box>
            <ButtonGroup>
              <Button onClick={decreaseMonth} disabled={prevMonthButtonDisabled}>
                <Icon type="chevron-left" />
              </Button>
              <Button onClick={increaseMonth} disabled={nextMonthButtonDisabled}>
                <Icon type="chevron-right" />
              </Button>
            </ButtonGroup>
          </Box>
        </Box>
      );
    },
  },
  render: props => {
    return <CustomDatePicker {...props} />;
  },
};

export const Input: Story = {
  args: {
    inline: false,
    startDate: new Date(),
    endDate: new Date(),
    customInput: <CustomInput />,
    selectsRange: true,
    minDate: new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
    maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    renderCustomHeader: ({
      date,
      changeYear,
      changeMonth,
      decreaseMonth,
      increaseMonth,
      prevMonthButtonDisabled,
      nextMonthButtonDisabled,
    }) => {
      return (
        <Box display="flex" px={2} width="100%" justifyContent={'space-between'}>
          <Box display="flex">
            <Box maxWidth={115} minWidth={115} marginRight={2}>
              <SelectExample
                value={months[getMonth(date)]}
                onChange={({target: {value}}: {target: {value: any}}) => {
                  changeMonth(months.indexOf(value));
                }}
                options={months}
              />
            </Box>
            <Box width={80}>
              <SelectExample
                value={getYear(date)}
                onChange={({target: {value}}: {target: {value: any}}) =>
                  changeYear(parseInt(value))
                }
                options={years}
              />
            </Box>
          </Box>
          <Box>
            <ButtonGroup>
              <Button onClick={decreaseMonth} disabled={prevMonthButtonDisabled}>
                <Icon type="chevron-left" />
              </Button>
              <Button onClick={increaseMonth} disabled={nextMonthButtonDisabled}>
                <Icon type="chevron-right" />
              </Button>
            </ButtonGroup>
          </Box>
        </Box>
      );
    },
  },
  render: props => {
    return (
      <Box height={400}>
        <InlineDatePicker {...props} />
      </Box>
    );
  },
};

export const Single: Story = {
  args: {
    customInput: <CustomInput />,
    inline: true,
    renderCustomHeader: ({
      date,
      changeYear,
      changeMonth,
      decreaseMonth,
      increaseMonth,
      prevMonthButtonDisabled,
      nextMonthButtonDisabled,
    }) => {
      return (
        <Box display="flex" px={2} width="100%" justifyContent={'space-between'}>
          <Box display="flex">
            <Box maxWidth={115} minWidth={115} marginRight={2}>
              <SelectExample
                options={months}
                value={months[getMonth(date)]}
                onChange={changeMonth}
              />
            </Box>
            <Box width={90}>
              <SelectExample
                value={getYear(date)}
                onChange={({target: {value}}: {target: {value: any}}) =>
                  changeYear(parseInt(value))
                }
                options={years}
              />
            </Box>
          </Box>
          <Box>
            <ButtonGroup>
              <Button onClick={decreaseMonth} disabled={prevMonthButtonDisabled}>
                <Icon type="chevron-left" />
              </Button>
              <Button onClick={increaseMonth} disabled={nextMonthButtonDisabled}>
                <Icon type="chevron-right" />
              </Button>
            </ButtonGroup>
          </Box>
        </Box>
      );
    },
  },
  render: props => {
    return <SingleDatePicker {...props} />;
  },
};
