import React, {forwardRef, type ForwardedRef} from 'react';
import {
  menuItemClasses,
  Menu as MUIMenu,
  MenuItem as MUIMenuItem,
  type MenuItemProps,
  type MenuProps,
} from '@mui/material';
import type {Components, Theme} from '@mui/material';

export const MenuItemOverrides: Components<Theme>['MuiMenuItem'] = {
  styleOverrides: {
    root: ({theme, ownerState}) => {
      return {
        '&:hover': {
          backgroundColor: theme.palette.grey[200],
        },
        '&:active': {
          backgroundColor: theme.palette.semanticPalette.highlight.secondary,
        },
        [`&.${menuItemClasses.root}&.${menuItemClasses.selected}`]: {
          backgroundColor: 'transparent',
          fontWeight: theme.typography.h5.fontWeight,
          '&:hover': {
            backgroundColor: theme.palette.grey[200],
          },
          '&:active': {
            backgroundColor: theme.palette.semanticPalette.highlight.secondary,
          },
          '&:focus': {
            backgroundColor: theme.palette.grey[200],
          },
        },
        // If the MenuItem is an option, we want to change the font weight to body1 when the option is selected
        ...(ownerState.role === 'option' && {
          [`&&.${menuItemClasses.root}.${menuItemClasses.selected}`]: {
            fontWeight: theme.typography.body1.fontWeight,
          },
        }),
      };
    },
  },
};

export const MenuOverrides: Components<Theme>['MuiMenu'] = {
  styleOverrides: {
    root: ({theme}) => {
      return {
        padding: theme.spacing(0, 2),
      };
    },
    list: ({theme}) => {
      return {
        padding: theme.spacing(3, 0),
      };
    },
    paper: ({theme}) => {
      return {
        // eslint-disable-next-line no-restricted-properties
        boxShadow: theme.shadows[theme.boxShadows.md],
        marginTop: theme.spacing(1),
      };
    },
  },
};

const MenuComponent = (props: MenuProps, ref: ForwardedRef<HTMLDivElement>) => (
  <MUIMenu ref={ref} {...props} transitionDuration={props.transitionDuration ?? 0} />
);

export const Menu = forwardRef(MenuComponent);

Menu.displayName = 'Menu';

const MenuItemComponent = <C extends React.ElementType>(
  props: MenuItemProps<C, {component?: C}>,
  ref: ForwardedRef<HTMLLIElement>
) => <MUIMenuItem ref={ref} {...props} />;

export const MenuItem = forwardRef(MenuItemComponent);

MenuItem.displayName = 'MenuItem';

export type {MenuItemProps, MenuProps};
