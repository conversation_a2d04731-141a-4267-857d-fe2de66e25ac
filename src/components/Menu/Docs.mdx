import { <PERSON>a, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as MenuStories from './index.stories.tsx'

<Meta of={MenuStories} />

## Overview

A menu displays a list of choices on a temporary surface. It appears when the user interacts with a button, or other control.

[React Menu](https://mui.com/material-ui/react-menu/)

<Canvas of={MenuStories.Basic} />
***TO DO: add prop controls***
