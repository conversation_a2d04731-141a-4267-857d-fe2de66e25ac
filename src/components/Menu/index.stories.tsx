import type {Meta, StoryObj} from '@storybook/react';

import {BasicMenu} from 'src/components/Menu/story-helpers';

import {Menu} from './index';

type Story = StoryObj<typeof Menu>;

const meta: Meta<typeof Menu> = {
  component: Menu,
  title: 'components/Navigation/Menu',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1808-17829&mode=design&t=CLN27TlnJHhO7cSs-0',
    },
  },
};

export default meta;

export const Basic: Story = {
  render: args => {
    return (
      <>
        <BasicMenu {...args} />
      </>
    );
  },
};
