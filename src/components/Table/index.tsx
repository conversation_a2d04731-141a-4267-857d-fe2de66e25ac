import {
  Table as MUITable,
  TableBody as MUITableBody,
  TableCell as <PERSON>UIT<PERSON><PERSON>ell,
  TableContainer as MUITableContainer,
  TableFooter as MUITableFooter,
  TableHead as MUITableHead,
  TablePagination as MUITablePagination,
  TableRow as MUITableRow,
  TableSortLabel as MUITableSortLabel,
  type Components,
  type Theme,
} from '@mui/material';

export const TableCellOverrides: Components<Theme>['MuiTableCell'] = {
  styleOverrides: {
    root: ({theme}) => ({
      fontSize: theme.typography.body1.fontSize,
      borderColor: theme.palette.semanticPalette.stroke.secondary,
    }),
  },
};

export const Table = MUITable;
export const TableBody = MUITableBody;
export const TableCell = MUITableCell;
export const TableContainer = MUITableContainer;
export const TableFooter = MUITableFooter;
export const TableHead = MUITableHead;
export const TablePagination = MUITablePagination;
export const TableRow = MUITableRow;
export const TableSortLabel = MUITableSortLabel;

export type {
  TableProps,
  TableBodyProps,
  TableCellProps,
  TableContainerProps,
  TableFooterProps,
  TableHeadProps,
  TablePaginationProps,
  TableRowProps,
  TableSortLabelProps,
} from '@mui/material';
