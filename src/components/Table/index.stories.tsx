import type {Meta, StoryObj} from '@storybook/react';
import React from 'react';
import {Box, Button, FormControl, Icon, MenuItem} from 'src/index';
import {Select} from '@mui/material';

import {Table, TableBody, TableCell, TableContainer, TableHead, TableRow} from './';

const columns = ['Crop type', 'Crop usage', 'Growing period', 'Yield', 'Residue harvested'];
const rowData = [
  {
    cropType: 'Corn',
    cropUsage: 'Grain',
    growingPeriod: '120 days',
    yield: '200 bu/ac',
    residueHarvested: '50%',
  },
  {
    cropType: 'Corn',
    cropUsage: 'Grain',
    growingPeriod: '120 days',
    yield: '200 bu/ac',
    residueHarvested: '50%',
  },
];

type Story = StoryObj<typeof Table>;

export default {
  component: Table,
  title: 'components/Data Display/Table',
} as Meta<typeof Table>;

const BasicSelect = () => {
  const [age, setAge] = React.useState('Soybeans');

  const handleChange = (event: any) => {
    setAge(event.target.value);
  };

  return (
    <Box minWidth={120}>
      <FormControl fullWidth>
        <Select id="demo-simple-select" value={age} displayEmpty onChange={handleChange}>
          <MenuItem value={'Soybeans'}>Soybeans</MenuItem>
          <MenuItem value={'Corn'}>Corn</MenuItem>
        </Select>
      </FormControl>
    </Box>
  );
};

export const Basic: Story = {
  args: {},
  render: () => {
    return (
      <>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                {columns.map(column => (
                  <TableCell key={column}>{column}</TableCell>
                ))}
                <TableCell key={'Addrow'} />
                <TableCell key={'delete'} />
              </TableRow>
            </TableHead>
            <TableBody>
              {rowData.map(row => (
                <TableRow key={row.cropType}>
                  <TableCell>
                    <BasicSelect />
                  </TableCell>
                  <TableCell>{row.cropUsage}</TableCell>
                  <TableCell>{row.growingPeriod}</TableCell>
                  <TableCell>{row.yield}</TableCell>
                  <TableCell>{row.residueHarvested}</TableCell>
                  <TableCell>
                    <Button variant="outlined" color="secondary">
                      Add Row
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Button variant="text" color="secondary">
                      <Icon type="delete" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </>
    );
  },
};
