import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {Tab, Tabs} from 'src/index';
import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const argTypes = getArgTypes(
  ['sx', 'children', 'classes', 'component', 'onChange', 'slotProps', 'slots', 'sx'],
  {
    ...SYSTEM_ARGTYPES,
    ...FORM_COMPONENT_ARGTYPES,
  }
);

type Story = StoryObj<typeof Tabs>;

export default {
  argTypes: {
    ...argTypes,
    action: {
      description:
        'Callback fired when the component mounts. This is useful when you want to trigger an action programmatically. It supports two actions: updateIndicator() and updateScrollButtons().',
    },
    allowScrollButtonsMobile: {
      description:
        'If true, the scroll buttons arent forced hidden on mobile. By default the scroll buttons are hidden on mobile and takes precedence over scrollButtons.',
    },
    'aria-label': {
      description: 'The label for the Tabs as a string.',
    },
    'aria-labelledby': {
      description: 'An id or list of ids separated by a space that label the Tabs.',
    },
    centered: {
      description: 'If true, the tabs are centered. This prop is intended for large views.',
    },
    indicatorColor: {
      description: 'Determines the color of the indicator',
      type: 'string',
    },
    ScrollButtonComponent: {
      description: 'The component used to render the scroll buttons.',
      defaultValue: 'TabScrollButton',
    },
    scrollButtons: {
      description:
        'Determines behavior of scroll buttons when tabs are set to scroll. auto will only present them when not all the items are visible. true will always present them. false will never present them.',
      type: 'string',
      defaultValue: 'auto',
    },
    selectionFollowsFocus: {
      description:
        'If true the selected tab changes on focus. Otherwise it only changes on activation.',
    },
    TabIndicatorProps: {
      description: 'Props applied to the tab indicator element.',
    },
    TabScrollButtonProps: {
      description: 'Props applied to the TabScrollButton element.',
    },
    textColor: {
      description: 'Determines the color of the Tab.',
      type: 'string',
    },
    value: {
      description:
        'The value of the currently selected Tab. If you dont want any selected Tab, you can set this prop to false.',
    },
    variant: {
      description: 'The variant to use. `scrollable` `fullWidth` or `standard`.',
      type: 'string',
    },
    visibleScrollbar: {
      description:
        'If true, the scrollbar is visible. It can be useful when displaying a long vertical list of tabs.',
    },
  },
  component: Tabs,
} as Meta<typeof Tabs>;

export const Basic: Story = {
  render: args => {
    return (
      <Tabs {...args}>
        <Tab label="Item One" />
        <Tab label="Item Two" />
        <Tab label="Item Three" />
      </Tabs>
    );
  },
};
