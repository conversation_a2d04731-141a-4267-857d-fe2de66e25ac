import type {<PERSON><PERSON>, <PERSON>Obj} from '@storybook/react';
import React from 'react';
import {
  Box,
  SvgIcon,
  Tab,
  TabContext,
  TabList,
  TabPanel,
  Tabs,
  Typography,
  type TabsProps,
} from 'src/index';
import {Stack} from '@mui/system';

type Story = StoryObj<typeof Tab>;

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const {children, value, index, ...other} = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={3}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function BasicTabs<C extends React.ElementType>(props: TabsProps<C>) {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box>
      <Box>
        <Tabs value={value} onChange={handleChange} aria-label="basic tabs example" {...props}>
          <Tab label="Item One" {...a11yProps(0)} />
          <Tab label="Item Two" {...a11yProps(1)} />
          <Tab label="Item Three" {...a11yProps(2)} />
          <Tab label="Item Four" {...a11yProps(3)} />
        </Tabs>
      </Box>
      <CustomTabPanel value={value} index={0}>
        Item One
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        Item Two
      </CustomTabPanel>
      <CustomTabPanel value={value} index={2}>
        Item Three
      </CustomTabPanel>
      <CustomTabPanel value={value} index={3}>
        Item Four
      </CustomTabPanel>
    </Box>
  );
}

function BasicTabsStates<C extends React.ElementType>(props: TabsProps<C>) {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box>
      <Box>
        <Tabs value={value} onChange={handleChange} aria-label="basic tabs example" {...props}>
          <Tab label="Active" {...a11yProps(0)} />
          <Tab label="Inactive" {...a11yProps(1)} />
          <Tab label="Disabled" {...a11yProps(2)} disabled />
          <Tab label="With Icon" {...a11yProps(3)} icon={<SvgIcon type="bell" />} />
        </Tabs>
      </Box>
      <CustomTabPanel value={value} index={0}>
        Item One
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        Item Two
      </CustomTabPanel>
      <CustomTabPanel value={value} index={2}>
        Item Three
      </CustomTabPanel>
    </Box>
  );
}

function MuiLabTabs() {
  const [value, setValue] = React.useState('1');

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValue(newValue);
  };

  return (
    <Box>
      <TabContext value={value}>
        <Box>
          <TabList onChange={handleChange} aria-label="lab API tabs example">
            <Tab label="Item One" value="1" />
            <Tab label="Item Two" value="2" />
            <Tab label="Item Three" value="3" />
          </TabList>
        </Box>
        <TabPanel value="1">Item One</TabPanel>
        <TabPanel value="2">Item Two</TabPanel>
        <TabPanel value="3">Item Three</TabPanel>
      </TabContext>
    </Box>
  );
}

export default {
  component: Tab,
  title: 'components/Navigation/Tabs',
} as Meta<typeof Tab>;

export const Basic: Story = {
  render: () => {
    return <BasicTabs />;
  },
};

export const States: Story = {
  render: () => {
    return <BasicTabsStates />;
  },
};

export const TextColor: Story = {
  render: () => {
    return (
      <Stack spacing={4}>
        <Box>
          <Typography variant="h6">Primary</Typography>
          <BasicTabs textColor="primary" />
        </Box>
        <Box>
          <Typography variant="h6">Secondary</Typography>
          <BasicTabs textColor="secondary" />
        </Box>
        <Box color={theme => theme.palette.semanticPalette.text.error}>
          <Typography variant="h6">Inherit</Typography>
          <BasicTabs textColor="inherit" />
        </Box>
      </Stack>
    );
  },
};

export const IndicatorColor: Story = {
  render: () => {
    return (
      <Stack spacing={4}>
        <Box>
          <Typography variant="h6">Primary</Typography>
          <BasicTabs textColor="primary" indicatorColor="primary" />
        </Box>
        <Box>
          <Typography variant="h6">Secondary</Typography>
          <BasicTabs textColor="secondary" indicatorColor="secondary" />
        </Box>
      </Stack>
    );
  },
};

export const Scrollable: Story = {
  render: () => {
    return (
      <Box maxWidth={380}>
        <BasicTabs variant="scrollable" scrollButtons="auto" />
      </Box>
    );
  },
};

export const Labs: Story = {
  render: () => {
    return <MuiLabTabs />;
  },
};
