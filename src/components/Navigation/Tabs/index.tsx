import {Tab<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabPanel} from '@mui/lab';
import type {TabProps as MuiTabProps, TabsProps as MuiTabsProps} from '@mui/material';
import {
  buttonBaseClasses,
  Tab as MuiTab,
  Tabs as MuiTabs,
  tabClasses,
  type Components,
  type Theme,
} from '@mui/material';

const TabsOverrides: Components<Theme>['MuiTabs'] = {
  styleOverrides: {
    indicator: ({theme, ownerState}) => ({
      ...((ownerState.textColor === 'primary' || ownerState.indicatorColor === 'primary') && {
        backgroundColor: theme.palette.semanticPalette.surfaceInverted.main,
      }),
    }),
    scrollButtons: ({theme}) => ({
      borderBottom: `2px solid ${theme.palette.semanticPalette.stroke.main}`,
      '&.Mui-disabled': {
        opacity: 1,
        '> svg': {
          opacity: 0.5,
        },
      },
    }),
  },
};

const TabOverrides: Components<Theme>['MuiTab'] = {
  defaultProps: {
    iconPosition: 'start',
  },
  styleOverrides: {
    root: ({theme}) => ({
      // eslint-disable-next-line no-restricted-syntax
      fontWeight: theme.typography.fontWeightRegular,
      padding: theme.spacing(1, 2),
      [`&.${tabClasses.selected}`]: {
        // eslint-disable-next-line no-restricted-syntax
        fontWeight: theme.typography.fontWeightBold,
      },
      [`&.${buttonBaseClasses.root}`]: {
        padding: theme.spacing(1, 5),
      },
      borderBottom: `2px solid ${theme.palette.semanticPalette.stroke.main}`,
    }),
    labelIcon: {
      minHeight: 'initial',
    },
    textColorPrimary: ({theme}) => {
      return {
        color: theme.palette.semanticPalette.text.secondary,
        [`&.${tabClasses.selected}`]: {
          color: theme.palette.semanticPalette.text.main,
        },
      };
    },
  },
};

type TabsProps<C extends React.ElementType> = Omit<MuiTabsProps, 'orientation'> & {
  component?: C;
};
type TabProps<C extends React.ElementType> = Omit<MuiTabProps, 'iconPosition'> & {
  component?: C;
};

const Tab = <C extends React.ElementType>(props: TabProps<C>) => <MuiTab {...props} />;
const Tabs = <C extends React.ElementType>(props: TabsProps<C>) => <MuiTabs {...props} />;

Tab.displayName = 'Tab';
Tabs.displayName = 'Tabs';

export {
  TabOverrides,
  TabsOverrides,
  Tab,
  Tabs,
  TabContext,
  TabList,
  TabPanel,
  type TabProps,
  type TabsProps,
};

export type {TabContextProps, TabListProps, TabPanelProps} from '@mui/lab';
