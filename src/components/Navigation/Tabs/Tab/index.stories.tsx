import type {<PERSON>a, StoryObj} from '@storybook/react';
import {Tab, Tabs} from 'src/index';
import {getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

import {SvgIcon} from 'src/components/Icon';

const argTypes = getArgTypes(['sx', 'children', 'disabled'], {
  ...SYSTEM_ARGTYPES,
});

type Story = StoryObj<typeof Tab>;

export default {
  argTypes: {
    ...argTypes,
    classes: {
      description: 'Override or extend the styles applied to the component.',
    },
    disabled: {
      description: 'If `true`, the tab will be disabled.',
    },
    disableFocusRipple: {
      description: 'If `true`, the keyboard focus ripple will be disabled.',
    },
    disableRipple: {
      description: 'If `true`, the ripple effect will be disabled.',
    },
    icon: {
      description: 'The icon to display.',
    },
    label: {
      description: 'The label element.',
    },
    value: {
      description: 'You can provide your own value. If not provided, the index will be used.',
    },
    wrapped: {
      description: 'If `true`, the text will not wrap, but instead will truncate with an ellipsis.',
    },
  },
  component: Tab,
} as Meta<typeof Tab>;

export const Basic: Story = {
  args: {
    label: 'Item One',
  },
  render: args => {
    return <Tab {...args} />;
  },
};

export const Icon: Story = {
  args: {
    label: 'Icon ',
    icon: <SvgIcon type="bell" />,
  },
  render: args => {
    return <Tab {...args} />;
  },
};

export const Disabled: Story = {
  render: () => {
    return (
      <Tabs aria-label="disabled tabs example">
        <Tab label="Active" />
        <Tab label="Disabled" disabled />
        <Tab label="Active" />
      </Tabs>
    );
  },
};
