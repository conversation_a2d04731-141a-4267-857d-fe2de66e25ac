import { <PERSON>a, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as TabStories from './index.stories.tsx'

<Meta of={TabStories} />

 ## Overview

[MUI Tab documentation](https://mui.com/material-ui/api/tab/)

`<Tab />` - the tab element itself. Clicking on a tab displays its corresponding panel.

<Canvas of={TabStories.Basic} />
<Controls of={TabStories.Basic} />


### With Icon
<Canvas of={TabStories.Icon} />