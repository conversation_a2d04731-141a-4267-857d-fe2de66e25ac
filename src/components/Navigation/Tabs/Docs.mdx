import { <PERSON>a, <PERSON><PERSON>, <PERSON><PERSON>, Story } from "@storybook/blocks";
import * as TabStories from './index.stories.tsx'

<Meta of={TabStories} />

 ## Overview

[MUI Tab documentation](https://mui.com/material-ui/react-tabs/)

Tabs are implemented using a collection of related components:

`<Tab />` - the tab element itself. Clicking on a tab displays its corresponding panel.

`<Tabs />` - the container that houses the tabs. Responsible for handling focus and keyboard navigation between tabs.

<Canvas of={TabStories.Basic} />


### States
A tab can be disabled by setting the `disabled` prop.
<Canvas of={TabStories.States} />

### Icon
A tab can display only an icon or both an icon and a label.
<Canvas of={TabStories.States} />

### Text Color
The `textColor` prop can be used to change the color of the text in the tab. The default value is `primary`.
<Canvas of={TabStories.TextColor} />

### Indicator Color
The `indicatorColor` prop can be used to change the color of the indicator in the tabs. The default value is `primary`.
<Canvas of={TabStories.IndicatorColor} />

### Scrollable
Use the `variant="scrollable"` and `scrollButtons="auto"` props to display left and right scroll buttons on desktop that are hidden on mobile.

More scrollable configurations which support mobile and further use cases can be found in the [MUI documentation](https://mui.com/material-ui/react-tabs/#scrollable-tabs).
<Canvas of={TabStories.Scrollable} />


### Experimental API

`@mui/lab` offers utility components that inject props to implement accessible tabs following WAI-ARIA Authoring Practices:


`<TabList /> `- the container that houses the tabs. Responsible for handling focus and keyboard navigation between tabs.

`<TabPanel />` - the card that hosts the content associated with a tab.

`<TabContext />` - the top-level component that wraps the Tab List and Tab Panel components.

<Canvas of={TabStories.Labs} />