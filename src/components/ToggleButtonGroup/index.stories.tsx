import type {<PERSON><PERSON>, <PERSON>Obj} from '@storybook/react';
import {Box, Icon, ToggleButton, ToggleButtonGroup} from 'src/index';

const meta: Meta<typeof ToggleButtonGroup> = {
  component: ToggleButtonGroup,
  title: 'components/Inputs/ToggleButtonGroup',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=6083-43136&mode=design&t=rejAV8NnsgnKgIO4-0',
    },
  },
};

// TODO - storybook props not working again

export default meta;
type Story = StoryObj<typeof ToggleButtonGroup>;

export const Basic: Story = {
  args: {},
  render: args => {
    return (
      <ToggleButtonGroup value={2} {...args}>
        <ToggleButton value={1}>Button</ToggleButton>
        <ToggleButton value={2}>Selected</ToggleButton>
        <ToggleButton value={3}>Button</ToggleButton>
        <ToggleButton value={4} disabled>
          Disabled
        </ToggleButton>
      </ToggleButtonGroup>
    );
  },
};

export const Color: Story = {
  args: {},
  render: args => {
    return (
      <>
        <Box mb={4}>
          <ToggleButtonGroup value={2} {...args}>
            <ToggleButton value={1}>standard</ToggleButton>
            <ToggleButton value={2}>standard</ToggleButton>
            <ToggleButton value={3}>standard</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        <Box mb={4}>
          <ToggleButtonGroup value={2} color="primary" {...args}>
            <ToggleButton value={1}>primary</ToggleButton>
            <ToggleButton value={2}>primary</ToggleButton>
            <ToggleButton value={3}>primary</ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </>
    );
  },
};
export const Backgrounds: Story = {
  args: {},
  render: args => {
    return (
      <>
        <Box mb={4} p={8} bgcolor="semanticPalette.surface.secondary">
          <ToggleButtonGroup value={2} {...args}>
            <ToggleButton value={1}>standard</ToggleButton>
            <ToggleButton value={2}>standard</ToggleButton>
            <ToggleButton value={3}>standard</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        <Box mb={4} p={8} bgcolor="semanticPalette.surface.secondary">
          <ToggleButtonGroup value={2} color="primary" {...args}>
            <ToggleButton value={1}>primary</ToggleButton>
            <ToggleButton value={2}>primary</ToggleButton>
            <ToggleButton value={3}>primary</ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </>
    );
  },
};

export const Sizes: Story = {
  args: {},
  render: args => {
    return (
      <>
        <Box mb={4}>
          <ToggleButtonGroup value={2} size="medium" {...args}>
            <ToggleButton value={1}>medium</ToggleButton>
            <ToggleButton value={2}>medium</ToggleButton>
            <ToggleButton value={3}>medium</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        <Box mb={4}>
          <ToggleButtonGroup value={2} size="small" {...args}>
            <ToggleButton value={1}>small</ToggleButton>
            <ToggleButton value={2}>small</ToggleButton>
            <ToggleButton value={3}>small</ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </>
    );
  },
};

export const Orientations: Story = {
  render: args => (
    <ToggleButtonGroup orientation="vertical" value="1" {...args}>
      <ToggleButton value="1">
        <Icon type="home" />
      </ToggleButton>
      <ToggleButton value="2">
        <Icon type="calendar" />
      </ToggleButton>
      <ToggleButton value="2">
        <Icon type="chart" />
      </ToggleButton>
      <ToggleButton value="2">
        <Icon type="chat" />
      </ToggleButton>
    </ToggleButtonGroup>
  ),
};

export const ExclusiveSelect: Story = {
  render: args => (
    <ToggleButtonGroup value="lock-open" exclusive {...args}>
      <ToggleButton value="lock">
        <Icon type="lock" />
      </ToggleButton>
      <ToggleButton value="lock-open">
        <Icon type="lock-open" />
      </ToggleButton>
    </ToggleButtonGroup>
  ),
};
