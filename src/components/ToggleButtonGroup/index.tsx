import {forwardRef, type ForwardedRef} from 'react';
import {
  ToggleButtonGroup as MUIToggleButtonGroup,
  toggleButtonGroupClasses,
  type ToggleButtonGroupProps,
} from '@mui/material';
import type {Components, Theme} from '@mui/material/styles';

// Note: This component has augmented props. See src/muiTheme.d.ts for prop overrides

export const ToggleButtonGroupOverrides: Components<Theme>['MuiToggleButtonGroup'] = {
  styleOverrides: {
    grouped: ({theme}) => ({
      [`&.${toggleButtonGroupClasses.grouped}`]: {
        borderColor: theme.palette.semanticPalette.stroke.main,
      },
    }),
  },
};

const ToggleButtonGroupComponent = (
  props: ToggleButtonGroupProps,
  ref: ForwardedRef<HTMLDivElement>
) => <MUIToggleButtonGroup ref={ref} {...props} />;

export const ToggleButtonGroup = forwardRef(ToggleButtonGroupComponent);

ToggleButtonGroup.displayName = 'ToggleButtonGroup';

export type {ToggleButtonGroupProps};
