import { Meta, Canvas, Controls } from "@storybook/blocks";
import * as ToggleButtonGroupStories from './index.stories.tsx'


<Meta of={ToggleButtonGroupStories} />

 ## Overview

A Toggle Button can be used to group related options.
To emphasize groups of related Toggle buttons, a group should share a common container. 
The ToggleButtonGroup controls the selected state of its child buttons when given its own value prop.

[React Toggle Button Group](https://mui.com/material-ui/api/toggle-button-group/)

<Canvas of={ToggleButtonGroupStories.Basic} />
{/* TODO: Fix controls. 
Currently non operable - being tracked in https://discordapp.com/channels/486522875931656193/1154570816181243924/1154570816181243924 
<Controls of={ToggleButtonGroupStories.Basic}/>
*/}

## Colors
To adjust the color of the group use the `color` prop.
<Canvas of={ToggleButtonGroupStories.Color} />

## On Varied Backgrounds
Demonstrating usage on varied surface background colors
<Canvas of={ToggleButtonGroupStories.Backgrounds} />

## Sizes
To adjust the size of the group use the `size` prop.
<Canvas of={ToggleButtonGroupStories.Sizes} />

## Orientations
To adjust the orientation of the group use the `orientation` prop.
<Canvas of={ToggleButtonGroupStories.Orientations} />

## Exclusive Select
With exclusive selection, selecting one option deselects any other. Enable this using the `exclusive` prop.

Note: Exclusive selection does not enforce that a button must be active.
<Canvas of={ToggleButtonGroupStories.ExclusiveSelect} />