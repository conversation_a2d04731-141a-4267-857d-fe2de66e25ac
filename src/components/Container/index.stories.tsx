import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import React from 'react';
import {Box, Container, Paper, Stack, Typography} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';
import {SIZE_KEYS} from 'src/tokens/constants';

type Story = StoryObj<typeof Container>;

const containerArgTypeCategories = categorizeArgTypes(['component', 'ref'], 'MUI System Props');

export default {
  argTypes: {
    ...containerArgTypeCategories,
  },
  component: Container,
  title: 'components/Layout/Container',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16749',
    },
  },
} as Meta<ComponentProps<typeof Container>>;

export const Basic: Story = {
  render: props => (
    <Container {...props}>
      <Box bgcolor="semanticPalette.surface.secondary">
        <Typography variant="h1">Hello World!</Typography>
      </Box>
    </Container>
  ),
};

export const Widths: Story = {
  render: (props, {theme}) => (
    <>
      {SIZE_KEYS.map(width => (
        <Stack key={width} my={2}>
          <Container {...props} maxWidth={width}>
            <Paper bgcolor="semanticPalette.surface.secondary">
              <Box p={4}>
                <Typography variant="h5">{width}</Typography>
                <Typography variant="h5">{theme.fixedWidths[width]}px</Typography>
              </Box>
            </Paper>
          </Container>
        </Stack>
      ))}
    </>
  ),
};
