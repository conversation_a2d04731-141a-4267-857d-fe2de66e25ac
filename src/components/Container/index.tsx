import {forwardRef, type ForwardedRef} from 'react';
import {Container as <PERSON><PERSON><PERSON><PERSON><PERSON>, type ContainerProps as MUIContainerProps} from '@mui/material';

type ContainerProps<C extends React.ElementType> = MUIContainerProps & {
  component?: C;
};
const ContainerComponent = <C extends React.ElementType>(
  props: ContainerProps<C>,
  ref: ForwardedRef<HTMLDivElement>
) => <MUIContainer ref={ref} {...props} />;

export const Container = forwardRef(ContainerComponent);

Container.displayName = 'Container';

export type {ContainerProps};
