import { Meta, Canvas, Controls, Story } from "@storybook/blocks";
import { Container } from "src/index";
import * as ContainerStories from './index.stories.tsx'

<Meta of={ContainerStories}/>

## Overview
The container is the most basic layout element and functions to center your content horizontally, both with fixed and fluid widths. 
It should only be used for this layout purpose. For adding additional styles (ex. backgrounds, borders, shadows, spacing) a nested Box component should be used or Stack.

You can learn more here: [https://mui.com/material-ui/react-container/](https://mui.com/material-ui/react-container/)

<Canvas of={ContainerStories.Basic} />
<Controls of={ContainerStories.Basic}/>

## Max Widths
<Canvas of={ContainerStories.Widths} />
