import {forwardRef, type ForwardedRef} from 'react';
import {
  accordionClasses,
  Paper as MUIPaper,
  type Components,
  type PaperProps as MUIPaperProps,
  type Theme,
} from '@mui/material';
import type {SystemProps} from '@mui/system';

export const PaperOverrides: Components<Theme>['MuiPaper'] = {
  defaultProps: {
    elevation: 2,
  },
  styleOverrides: {
    root: ({theme}) => ({
      [`&:has(> .${accordionClasses.rounded})`]: {
        borderRadius: theme.shape.borderRadius * theme.borderRadii.sm, // adjusts borderRadius on Paper component with nested rounded Accordions to prevent shadow misalignment and color bleed through
      },
    }),
    rounded: ({theme}) => ({
      borderRadius: theme.shape.borderRadius * theme.borderRadii.md,
    }),
  },
};

type PaperProps<C extends React.ElementType> = MUIPaperProps & {
  component?: C;
  bgcolor?: SystemProps['bgcolor'];
  fullHeight?: boolean;
  fullWidth?: boolean;
};

const PaperComponent = <C extends React.ElementType>(
  props: PaperProps<C>,
  ref: ForwardedRef<HTMLDivElement>
) => {
  const {bgcolor: backgroundColor, fullHeight, fullWidth, ...paperProps} = props;
  return (
    <MUIPaper
      ref={ref}
      {...paperProps}
      sx={{
        ...(props.sx ?? {}),
        ...(backgroundColor ? {backgroundColor} : {}),
        ...(fullHeight ? {height: 1} : {}),
        ...(fullWidth ? {width: 1} : {}),
      }}
    />
  );
};

export const Paper = forwardRef(PaperComponent);

Paper.displayName = 'Paper';

export type {PaperProps};
