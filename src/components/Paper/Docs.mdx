import { Meta, <PERSON>vas, <PERSON>s, Story } from "@storybook/blocks";
import * as PaperStories from './index.stories.tsx'

<Meta of={PaperStories}/>

## Overview
Paper is a surface component used to create a distinct section from other components via a shadow or outline.

You can learn more here: [https://mui.com/material-ui/react-paper/](https://mui.com/material-ui/react-paper/)

<Canvas of={PaperStories.Basic} />
<Controls of={PaperStories.Basic}/>

## Variants
If you need an outlined surface, use the variant prop.
<Canvas of={PaperStories.Variants} />

## Elevation
The elevation can be used to establish a hierarchy between other content. 
In practical terms, the elevation controls the size of the shadow applied to the surface. 
In dark mode, raising the elevation also makes the surface lighter.
The elevation prop should only reference values from `theme.boxShadow`. 
To access the theme in your component, the `useTheme` hook is available.
<Canvas of={PaperStories.Elevation} />

## Paper with a surface color
<Canvas of={PaperStories.PaperWithSurfaceColor} />

## Paper with full height and full width
<Canvas of={PaperStories.PaperWithFullHeightFullWidth} />