import type {Meta, StoryObj} from '@storybook/react';
import React from 'react';
import {Box, Paper, Stack, Typography} from 'src/index';

import {ClickToCopy} from 'src/storybook-utils/story-components';

type Story = StoryObj<typeof Paper>;

const meta: Meta<typeof Paper> = {
  argTypes: {
    bgcolor: {
      description:
        'The <code>bgcolor</code> property can receive a string, which represents the path in theme.palette or a color string',
    },
  },
  component: Paper,
  title: 'components/Surfaces/Paper',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=633-20640&mode=design&t=LNB6ovuW5WXurBK7-0',
    },
  },
};

export default meta;

export const Basic: Story = {
  render: props => (
    <Stack spacing={8} direction="row">
      <Paper {...props}>
        <SampleBox />
      </Paper>
    </Stack>
  ),
};

export const Variants: Story = {
  render: () => (
    <Stack spacing={8} direction="row">
      <Paper variant="elevation">
        <SampleBox>
          <Typography variant="body1">default</Typography>
        </SampleBox>
      </Paper>
      <Paper variant="elevation" square>
        <SampleBox>
          <Typography variant="body1">default square</Typography>
        </SampleBox>
      </Paper>
      <Paper variant="outlined">
        <SampleBox>
          <Typography variant="body1">outlined</Typography>
        </SampleBox>
      </Paper>
      <Paper variant="outlined" square>
        <SampleBox>
          <Typography variant="body1">outlined square</Typography>
        </SampleBox>
      </Paper>
    </Stack>
  ),
};

export const Elevation: Story = {
  render: () => (
    <Stack spacing={8} direction="row">
      <Paper elevation={2}>
        <SampleBox>
          <ClickToCopy>theme.boxShadow.sm</ClickToCopy>
        </SampleBox>
      </Paper>
      <Paper elevation={4}>
        <SampleBox>
          <ClickToCopy>theme.boxShadow.md</ClickToCopy>
        </SampleBox>
      </Paper>
      <Paper elevation={8}>
        <SampleBox>
          <ClickToCopy>theme.boxShadow.lg</ClickToCopy>
        </SampleBox>
      </Paper>
    </Stack>
  ),
};

export const PaperWithSurfaceColor: Story = {
  render: props => (
    <Box width={200} height={200}>
      <Paper {...props} bgcolor="semanticPalette.surface.error" fullWidth fullHeight>
        <SampleBox>
          <Typography>
            Using <code>bgcolor</code>
          </Typography>
        </SampleBox>
      </Paper>
    </Box>
  ),
};

export const PaperWithFullHeightFullWidth: Story = {
  render: props => (
    <Box width={theme => theme.fixedWidths.md} height={300}>
      <Paper {...props} fullHeight fullWidth>
        <Box display="flex" alignItems="center" justifyContent="center" height={1}>
          <Typography>
            Using <code>fullHeight</code> & <code>fullWidth</code>
          </Typography>
        </Box>
      </Paper>
    </Box>
  ),
};

const SampleBox: React.FC = ({children}) => (
  <Box height={200} width={200} display="flex" alignItems="center" justifyContent="center">
    {children}
  </Box>
);
