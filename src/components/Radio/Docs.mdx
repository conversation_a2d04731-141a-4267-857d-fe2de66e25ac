import { <PERSON>a, <PERSON><PERSON>, <PERSON><PERSON>, Story } from "@storybook/blocks";
import * as RadioStories from './index.stories.tsx'

<Meta of={RadioStories} />

 ## Overview
`Radio` should be composed with `FormControlLabel` and `RadioGroup`.

[MUI Radio](https://mui.com/material-ui/react-radio-button/)

<Canvas of={RadioStories.Basic} />
<Controls of={RadioStories.Basic}/>

## States
<Canvas of={RadioStories.States} />

## Colors
Note, default and primary result in the same colors.
<Canvas of={RadioStories.Colors} />

## Sizes
<Canvas of={RadioStories.Sizes} />

## Edges
<Canvas of={RadioStories.Edges} />