import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {FormControlLabel, FormGroup, Radio, Stack} from 'src/index';
import type {RadioProps} from 'src/index';
import {controlsExclude} from 'src/storybook-utils/argTypes';

import argTypes from './argTypes';

type Story = StoryObj<typeof Radio>;

export default {
  component: Radio,
  title: 'components/Inputs/Radio',
  argTypes,
  parameters: {
    controls: {
      exclude: controlsExclude,
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=1808%3A17831',
    },
  },
} as Meta<RadioProps>;

export const Basic: StoryObj<RadioProps> = {
  render: () => <Radio />,
};

export const States: Story = {
  render: args => (
    <FormGroup>
      <Stack gap={2}>
        <FormControlLabel label="Selected" control={<Radio checked {...args} />} />
        <FormControlLabel label="Unselected" control={<Radio {...args} />} />
        <FormControlLabel
          label="Disabled Default Selected"
          control={<Radio disabled defaultChecked {...args} />}
        />
      </Stack>
    </FormGroup>
  ),
};

export const Colors: Story = {
  render: args => (
    <FormGroup>
      <Stack gap={2}>
        <FormControlLabel label="primary (default)" control={<Radio checked {...args} />} />
        <FormControlLabel label="primary (default)" control={<Radio {...args} />} />
        <FormControlLabel
          label="primary (default)"
          control={<Radio disabled defaultChecked {...args} />}
        />
        <FormControlLabel label="primary (default)" control={<Radio disabled {...args} />} />

        <FormControlLabel label="error" control={<Radio checked color="error" {...args} />} />
        <FormControlLabel label="error" control={<Radio color="error" {...args} />} />
        <FormControlLabel
          label="error"
          control={<Radio checked disabled color="error" {...args} />}
        />
        <FormControlLabel label="error" control={<Radio disabled color="error" {...args} />} />
      </Stack>
    </FormGroup>
  ),
};

export const Sizes: Story = {
  render: args => (
    <FormGroup>
      <Stack gap={2}>
        <FormControlLabel label="medium (default)" control={<Radio checked {...args} />} />
        <FormControlLabel label="small" control={<Radio size="small" checked {...args} />} />
      </Stack>
    </FormGroup>
  ),
};

export const Edges: Story = {
  render: args => (
    <FormGroup>
      <Stack gap={2}>
        <FormControlLabel label="edge default" control={<Radio checked {...args} />} />
        <FormControlLabel label="edge start" control={<Radio checked edge="start" {...args} />} />

        <FormControlLabel
          label="no edge defined"
          labelPlacement="start"
          control={<Radio checked {...args} />}
        />

        <FormControlLabel
          label="edge end"
          labelPlacement="start"
          control={<Radio checked edge="end" {...args} />}
        />
      </Stack>
    </FormGroup>
  ),
};
