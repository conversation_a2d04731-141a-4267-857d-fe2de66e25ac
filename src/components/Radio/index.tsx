import {isStateColor} from 'src/utils/typeGuards';
import {Radio, radioClasses, type Components, type RadioProps, type Theme} from '@mui/material';

const leafRadioClasses = {
  checkedMark: 'leaf__checked-circle-mark',
  bgCircle: 'leaf__background-circle',
} as const;

const DefaultIcon = (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="10" cy="10" r="9.5" fill="white" className={leafRadioClasses.bgCircle} />
  </svg>
);

const CheckedIcon = (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="10" cy="10" r="9.5" className={leafRadioClasses.bgCircle} />
    <circle cx="10" cy="10" r="4" className={leafRadioClasses.checkedMark} />
  </svg>
);

export const RadioOverrides: Components<Theme>['MuiRadio'] = {
  defaultProps: {
    color: 'info',
    disableRipple: true,
    disableTouchRipple: true,
    disableFocusRipple: true,
    checkedIcon: CheckedIcon,
    icon: DefaultIcon,
  },
  styleOverrides: {
    root: ({theme, ownerState: {color = 'info'}}) => ({
      fill: 'none',
      stroke:
        theme.palette.semanticPalette.stroke[
          isStateColor(color) && color !== 'info' ? color : 'main'
        ],
      alignSelf: 'flex-start',
      marginTop: theme.spacing(0.5),
      [`& .${leafRadioClasses.bgCircle}`]: {
        fill: theme.palette.semanticPalette.surface.main,
      },

      [`&.${radioClasses.checked}`]: {
        color: theme.palette.semanticPalette.stroke[isStateColor(color) ? color : 'success'],

        [`& .${leafRadioClasses.checkedMark}`]: {
          stroke: theme.palette.semanticPalette.stroke[isStateColor(color) ? color : 'success'],
        },
      },

      [`&.${radioClasses.disabled}`]: {
        color: theme.palette.semanticPalette.text,
        stroke: theme.palette.semanticPalette.stroke.main,
        [`& .${leafRadioClasses.bgCircle}`]: {
          fill: theme.palette.semanticPalette.surface.secondary,
        },

        [`& .${leafRadioClasses.checkedMark}`]: {
          color: theme.palette.grey[400],
          stroke: theme.palette.semanticPalette.stroke.secondary,
        },
      },

      [`&.${radioClasses.root}:has(> svg[font-size="medium"])`]: {
        padding: `0 ${theme.spacing(2)}`,
        [`> svg`]: {
          height: 20,
          width: 20,
        },
      },
      [`&.${radioClasses.root}:has(> svg[font-size="small"])`]: {
        padding: `0 ${theme.spacing(2)}`,
        [`> svg`]: {
          height: 16,
          width: 16,
        },
      },
    }),
  },
};

export {Radio, type RadioProps};

// @ts-expect-error storybook only name
Radio.displayName = 'Radio';
