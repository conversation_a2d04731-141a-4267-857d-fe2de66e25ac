import {forwardRef, type ForwardedRef} from 'react';
import {Stack as MUIStack, type StackProps as MUIStackProps} from '@mui/material';

type StackProps<C extends React.ElementType> = MUIStackProps & {
  component?: C;
};

const StackComponent = <C extends React.ElementType>(
  props: StackProps<C>,
  ref: ForwardedRef<HTMLDivElement>
) => <MUIStack ref={ref} {...props} />;

export const Stack = forwardRef(StackComponent);

Stack.displayName = 'Stack';

export type {StackProps};
