import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as StackStories from './index.stories.tsx'

<Meta of={StackStories}/>

## Overview
Stack is a container component for arranging elements vertically or horizontally.
The Stack component packages [all the style functions](https://mui.com/system/properties/) that are exposed in @mui/system.

You can learn more here: [https://mui.com/material-ui/react-stack/](https://mui.com/material-ui/react-stack/)

<Canvas of={StackStories.Basic} />
<Controls of={StackStories.Basic}/>

## Direction
<Canvas of={StackStories.Direction} />

## Responsive
Adjust the preview viewport control to see the responsive behavior.
<Canvas of={StackStories.Responsive} />