import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import React from 'react';
import {Box, Container, Divider, Stack, Typography} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';

type Story = StoryObj<typeof Stack>;

const stackArgTypeCategories = categorizeArgTypes(['sx', 'component', 'ref'], 'MUI System Props');

const DIRECTIONS = ['column', 'column-reverse', 'row', 'row-reverse'] as const;

export default {
  argTypes: {
    ...stackArgTypeCategories,
  },
  component: Stack,
  title: 'components/Layout/Stack',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16749',
    },
  },
} as Meta<ComponentProps<typeof Stack>>;

export const Basic: Story = {
  args: {
    maxWidth: 'xs',
  },
  render: props => (
    <Container maxWidth="sm" fixed>
      <Stack {...props} spacing={2}>
        {[1, 2, 3].map(v => (
          <SampleBox key={v}>Item {v}</SampleBox>
        ))}
      </Stack>
    </Container>
  ),
};

export const Direction: Story = {
  render: props => (
    <Container maxWidth="sm" fixed>
      {DIRECTIONS.map(direction => (
        <Box key={direction}>
          <Divider textAlign="left">
            <Typography>{direction}</Typography>
          </Divider>
          <Box mt={4} mb={8}>
            <Stack {...props} spacing={2} direction={direction}>
              {[1, 2, 3].map(v => (
                <SampleBox key={v}>Item {v}</SampleBox>
              ))}
            </Stack>
          </Box>
        </Box>
      ))}
    </Container>
  ),
};

export const Responsive: Story = {
  render: props => (
    <Stack {...props} spacing={{xs: 2, md: 4}} direction={{xs: 'column', md: 'row'}} mt={4}>
      {[1, 2, 3].map(v => (
        <SampleBox key={v}>Item {v}</SampleBox>
      ))}
    </Stack>
  ),
};

const SampleBox: React.FC = ({children}) => (
  <Box p={4} border={1} borderRadius={1} borderColor="semanticPalette.stroke.main" boxShadow={2}>
    <Typography>{children}</Typography>
  </Box>
);
