import {
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Step<PERSON>onnect<PERSON>,
  Step<PERSON>ontent,
  StepIcon,
  stepIconClasses,
  stepLabelClasses,
  type StepLabelProps as MuiStepLabelProps,
  type StepperProps as MUIStepperProps,
  type StepButtonProps,
  type StepConnectorProps,
  type StepContentProps,
  type StepIconProps,
  type StepProps,
} from '@mui/material';
import {type Components, type Theme} from '@mui/material/styles';

import {SvgIcon} from 'src/components/Icon';

export const StepOverrides: Components<Theme>['MuiStep'] = {
  styleOverrides: {
    root: ({ownerState}) => {
      if (ownerState.onClick) {
        return {
          cursor: 'pointer',
        };
      }
    },
  },
};

export const StepLabelOverrides: Components<Theme>['MuiStepLabel'] = {
  defaultProps: {
    StepIconComponent: ({completed, icon, active}) => {
      return completed ? <SvgIcon type="check-mark" /> : <StepIcon icon={icon} active={active} />;
    },
  },
  styleOverrides: {
    root: ({theme}) => ({
      [`&&& .${stepLabelClasses.label}.${stepLabelClasses.disabled}`]: {
        color: theme.palette.grey[400],
      },
    }),
    label: ({theme}) => ({
      fontSize: theme.typography.h5.fontSize,
      maxWidth: theme.spacing(30),
      textOverflow: 'ellipsis',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
    }),
    iconContainer: ({theme}) => ({
      fontSize: theme.typography.h3.fontSize,
      borderRadius: theme.borderRadii.pill,
      padding: 0,
      marginRight: theme.spacing(2),
      color: theme.palette.semanticPalette.surface.main,
      backgroundColor: theme.palette.grey[400],
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      '&.Mui-active': {
        backgroundColor: theme.palette.primary.main,
      },
      '&.Mui-completed': {
        backgroundColor: theme.palette.primary.main,
      },
      '&.Mui-disabled': {
        backgroundColor: theme.palette.grey[300],
      },
      svg: {
        [`&.${stepIconClasses.root}`]: {
          circle: {
            color: 'transparent',
          },
          backgroundColor: 'transparent',
        },
        width: 16,
        height: 16,
      },
    }),
    vertical: () => ({
      alignItems: 'flex-start',
      [`.${stepLabelClasses.label}`]: {
        maxWidth: 'none',
        whiteSpace: 'pre-wrap',
      },
    }),
  },
};

export const StepIconOverrides: Components<Theme>['MuiStepIcon'] = {
  styleOverrides: {
    root: {
      backgroundColor: 'currentcolor',
    },
  },
};

export const StepButtonOverrides: Components<Theme>['MuiStepButton'] = {
  styleOverrides: {},
};

export const StepConnectorOverrides: Components<Theme>['MuiStepConnector'] = {
  styleOverrides: {
    line: ({theme}) => ({
      borderTopWidth: 2,
      borderColor: theme.palette.semanticPalette.stroke.main,
    }),
    lineVertical: () => ({
      borderLeftWidth: 2,
      marginLeft: -1, //Moves the line one pixel to center it to the step icon
    }),
  },
};

export const StepContentOverrides: Components<Theme>['MuiStepContent'] = {
  styleOverrides: {
    root: ({theme}) => ({
      marginLeft: 11, //Necessary for making the line be centered in the line (so borderLeftWidth: 2 can happen)
      borderLeftWidth: 2,
      borderColor: theme.palette.semanticPalette.stroke.main,
    }),
  },
};

type StepperProps<C extends React.ElementType> = Omit<
  MUIStepperProps,
  'elevation' | 'square' | 'variant'
> & {
  component?: C;
};

export function Stepper<C extends React.ElementType>(props: StepperProps<C>) {
  return <MUIStepper {...props} />;
}

type StepLabelProps = {
  disabled?: boolean;
} & MuiStepLabelProps;

export function StepLabel(props: StepLabelProps) {
  const disabled = props.disabled;
  return <MuiStepLabel {...props} icon={disabled ? <SvgIcon type="lock" /> : props.icon} />;
}

Stepper.displayName = 'Stepper';

// @ts-expect-error storybook only name
Step.displayName = 'Step';

// StepLabel.displayName = 'StepLabel';

// @ts-expect-error storybook only name
StepIcon.displayName = 'StepIcon';

// @ts-expect-error storybook only name
StepButton.displayName = 'StepButton';

// @ts-expect-error storybook only name
StepConnector.displayName = 'StepConnector';

// @ts-expect-error storybook only name
StepContent.displayName = 'StepContent';

export type {
  StepperProps,
  StepProps,
  StepButtonProps,
  StepLabelProps,
  StepIconProps,
  StepContentProps,
  StepConnectorProps,
};
export {Step, StepButton, StepIcon, StepConnector, StepContent};
