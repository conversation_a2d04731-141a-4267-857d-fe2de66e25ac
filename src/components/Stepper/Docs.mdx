import { <PERSON>a, <PERSON><PERSON>, <PERSON><PERSON>, Story } from "@storybook/blocks";
import * as StepperStories from './index.stories.tsx'

<Meta of={StepperStories}/>

## Overview
Steppers display progress through a sequence of logical and numbered steps. They may also be used for navigation. Steppers may display a transient feedback message after a step is saved.

You can learn more here: [https://mui.com/material-ui/react-stepper/](https://mui.com/material-ui/react-stepper/)

<Canvas of={StepperStories.Basic} />
<Controls of={StepperStories.Basic}/>

## States
The `Stepper` component can be in one of the following states:
- active
- completed
- disabled
- (stateless)

The last item in this example shows a truncated label.
<Canvas of={StepperStories.States} />


## Interactive Default Icons
Below is an example of creating a `Stepper` component instance using default Icons.

This example showcases how to implement a stepper which uses forward/next buttons to navigate between steps.

<Canvas of={StepperStories.InteractiveDefault} />


## Interactive Custom Icons
Below is an example of creating a `Stepper` component instance using custom Icons.

This example showcases how to implement a stepper which uses forward/next buttons to navigate between steps and has a click event attached to each step button.

<Canvas of={StepperStories.InteractiveIcons} />


## Skipping Steps
Below is an example of creating a `Stepper` component instance where a user is allowed to skip a step.

<Canvas of={StepperStories.Skip} />



## Vertical
Vertical steppers are designed for narrow screen sizes. They are ideal for mobile. All the features of the horizontal stepper can be implemented.

<Canvas of={StepperStories.Vertical} />


