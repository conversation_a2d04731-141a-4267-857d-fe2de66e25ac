import type {<PERSON>a, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import React from 'react';
import {
  Box,
  Button,
  Step,
  StepContent,
  StepLabel,
  Stepper,
  SvgIcon,
  Typography,
  type IconType,
  type StepLabelProps,
} from 'src/index';
import {upperCaseFirstLetter} from 'src/utils/string';

type Story = StoryObj<typeof Stepper>;

export default {
  component: Stepper,
  title: 'components/Navigation/Stepper',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=3819-48387&mode=design&t=OXx3QaTXTvwQroov-0',
    },
  },
  argTypes: {
    elevation: {
      table: {
        disable: true,
      },
    },
    variant: {
      table: {
        disable: true,
      },
    },
    square: {
      table: {
        disable: true,
      },
    },
  },
} as Meta<ComponentProps<typeof Stepper>>;

const steps = ['tillage', 'irrigation', 'crop'];

const states = ['completed', 'active', 'inactive', 'disabled', 'very long title that overflows'];

export const Basic: Story = {
  args: {
    nonLinear: true,
    activeStep: 0,
  },
  render: args => (
    <Stepper {...args}>
      {steps.map(label => {
        const stepProps: {completed?: boolean; disabled?: boolean} = {};
        const labelProps: StepLabelProps = {
          icon: <SvgIcon type={label as IconType} />,
        };

        return (
          <Step key={label} {...stepProps}>
            <StepLabel {...labelProps}>
              <Typography variant="h5" component="span" color="secondary.main">
                {upperCaseFirstLetter(label)}
              </Typography>
              {label === 'crop' && (
                <Typography variant="body2" component="span" color="secondary.main">
                  {' '}
                  25%
                </Typography>
              )}
            </StepLabel>
          </Step>
        );
      })}
    </Stepper>
  ),
};

export const States: Story = {
  render: () => (
    <Stepper activeStep={1}>
      {states.map(label => {
        const stepProps: {completed?: boolean; disabled?: boolean} = {
          completed: label === 'completed',
          disabled: label === 'disabled',
        };
        const labelProps: StepLabelProps = {
          icon: <SvgIcon type="tillage" />,
          disabled: label === 'disabled',
        };
        return (
          <Step key={label} {...stepProps}>
            <StepLabel {...labelProps}>
              <Typography variant="h5" component="span">
                {upperCaseFirstLetter(label)}
              </Typography>
            </StepLabel>
          </Step>
        );
      })}
    </Stepper>
  ),
};

export const Vertical: Story = {
  args: {
    orientation: 'vertical',
  },
  render: args => (
    <Stepper {...args}>
      {steps.map(label => {
        const stepProps: {completed?: boolean; disabled?: boolean} = {};
        const labelProps: StepLabelProps = {
          icon: <SvgIcon type={label as IconType} />,
        };
        return (
          <Step key={label} {...stepProps}>
            <StepLabel {...labelProps}>
              <Typography variant="h5" color="secondary.main">
                {upperCaseFirstLetter(label)}
              </Typography>
            </StepLabel>
            <StepContent>
              <Typography>Content</Typography>
            </StepContent>
          </Step>
        );
      })}
      <Step>
        <StepLabel icon={<SvgIcon type="quantification" />}>
          <Typography variant="h5" color="secondary.main">
            For Vertical stepper labels can be really long and won't cut off
          </Typography>
        </StepLabel>
      </Step>
    </Stepper>
  ),
};

const InteractiveDefaultStepperExample = () => {
  const [activeStep, setActiveStep] = React.useState(0);

  const handleNext = () => {
    setActiveStep(prevActiveStep => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep(prevActiveStep => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  return (
    <Box width="100%">
      <Stepper activeStep={activeStep}>
        {steps.map(label => {
          const stepProps: {completed?: boolean} = {};
          return (
            <Step key={label} {...stepProps}>
              <StepLabel>
                <Typography variant="h5" component="span" color="secondary.main">
                  {upperCaseFirstLetter(label)}
                </Typography>
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
      {activeStep === steps.length ? (
        <React.Fragment>
          <Typography>All steps completed - you&apos;re finished</Typography>
          <Box display="flex" flexDirection="row" pt={2}>
            <Box />
            <Button onClick={handleReset}>Reset</Button>
          </Box>
        </React.Fragment>
      ) : (
        <React.Fragment>
          <Typography>Step {activeStep + 1}</Typography>
          <Box display="flex" flexDirection="row" pt={2} justifyContent="space-between">
            <Button color="primary" disabled={activeStep === 0} onClick={handleBack}>
              Back
            </Button>
            <Box />
            <Button onClick={handleNext}>
              {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
            </Button>
          </Box>
        </React.Fragment>
      )}
    </Box>
  );
};

export const InteractiveDefault: Story = {
  render: () => <InteractiveDefaultStepperExample />,
};

const InteractiveStepperExample = () => {
  const [activeStep, setActiveStep] = React.useState(0);

  const handleNext = () => {
    setActiveStep(prevActiveStep => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep(prevActiveStep => prevActiveStep - 1);
  };

  const handleStepClick = (step: number) => {
    setActiveStep(step);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  return (
    <Box width={'100%'}>
      <Stepper activeStep={activeStep}>
        {steps.map((label, index) => {
          const stepProps: {completed?: boolean; disabled?: boolean} = {
            disabled: false,
          };
          const labelProps: StepLabelProps = {
            icon: <SvgIcon type={label as IconType} />,
          };
          return (
            <Step key={label} {...stepProps} onClick={() => handleStepClick(index)}>
              <StepLabel {...labelProps}>
                <Typography variant="h5" component="span" color="secondary.main">
                  {upperCaseFirstLetter(label)}
                </Typography>
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
      {activeStep === steps.length ? (
        <React.Fragment>
          <Typography>All steps completed - you&apos;re finished</Typography>
          <Box display="flex" flexDirection="row" pt={2}>
            <Box />
            <Button onClick={handleReset}>Reset</Button>
          </Box>
        </React.Fragment>
      ) : (
        <React.Fragment>
          <Box display="flex" flexDirection="row" pt={2} justifyContent="space-between">
            <Button color="primary" disabled={activeStep === 0} onClick={handleBack}>
              Back
            </Button>
            <Box />
            <Button onClick={handleNext}>
              {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
            </Button>
          </Box>
        </React.Fragment>
      )}
    </Box>
  );
};

export const InteractiveIcons: Story = {
  render: () => <InteractiveStepperExample />,
};

const SkipStepperExample = () => {
  const [activeStep, setActiveStep] = React.useState(0);

  const handleNext = () => {
    setActiveStep(prevActiveStep => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep(prevActiveStep => prevActiveStep - 1);
  };

  const handleStepClick = (step: number) => {
    setActiveStep(step);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  return (
    <Box width="100%">
      <Stepper activeStep={activeStep}>
        {steps.map((label, index) => {
          const stepProps: {completed?: boolean; disabled?: boolean} = {
            disabled: false,
          };
          const labelProps: StepLabelProps = {
            icon: <SvgIcon type={label as IconType} />,
          };
          return (
            <Step key={label} {...stepProps} onClick={() => handleStepClick(index)}>
              <StepLabel {...labelProps}>
                <Typography variant="h5" component="span" color="secondary.main">
                  {upperCaseFirstLetter(label)}
                </Typography>
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
      {activeStep === steps.length ? (
        <React.Fragment>
          <Typography>All steps completed - you&apos;re finished</Typography>
          <Box display="flex" flexDirection="row" pt={2}>
            <Box />
            <Button onClick={handleReset}>Reset</Button>
          </Box>
        </React.Fragment>
      ) : (
        <React.Fragment>
          <Typography>Step {activeStep + 1}</Typography>
          <Box display="flex" flexDirection="row" pt={2} justifyContent="space-between">
            <Button color="primary" disabled={activeStep === 0} onClick={handleBack}>
              Back
            </Button>
            <Box />
            <Button onClick={handleNext}>
              {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
            </Button>
          </Box>
        </React.Fragment>
      )}
    </Box>
  );
};

export const Skip: Story = {
  render: () => <SkipStepperExample />,
};
