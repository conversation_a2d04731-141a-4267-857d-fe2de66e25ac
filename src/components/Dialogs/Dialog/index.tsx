import * as React from 'react';
import {styled} from '@mui/material';
import Box from '@mui/material/Box';
import type {BoxProps as MUIBoxProps} from '@mui/material/Box';
import MUIDialog from '@mui/material/Dialog';
import type {DialogProps as MUIDialogProps} from '@mui/material/Dialog';
import MUIDialogActions from '@mui/material/DialogActions';
import type {DialogActionsProps as MUIDialogActionsProps} from '@mui/material/DialogActions';
import MUIDialogContent from '@mui/material/DialogContent';
import type {DialogContentProps as MUIDialogContentProps} from '@mui/material/DialogContent';
import MUIIconButton from '@mui/material/IconButton';
import MUITypography from '@mui/material/Typography';

import {Icon} from 'src/components/Icon';

export type DialogProps = Omit<MUIDialogProps, 'onClose' | 'title' | 'fullScreen'> & {
  DialogActionsProps?: MUIDialogActionsProps;
  DialogContentProps?: MUIDialogContentProps;
  DialogHeaderProps?: Omit<MUIBoxProps, 'children'>;
  hasCloseIcon?: boolean;
  hasDividers?: boolean;
  onClose: (event: React.SyntheticEvent, reason: DialogCloseReason) => void;
  title?: React.ReactNode | string;
  subtitle?: string;
};

export type DialogCloseReason = 'backdropClick' | 'escapeKeyDown' | 'iconClick';

/**
 *
 * @deprecated Use `SimpleDialog` from `src/components/Dialog/SimpleDialog` instead.
 */
export const OldDialog = (props: DialogProps) => {
  const {
    DialogActionsProps,
    DialogContentProps,
    DialogHeaderProps,
    children,
    hasCloseIcon,
    hasDividers,
    onClose,
    subtitle,
    title,
    ...rest
  } = props;

  const {className: DialogHeaderClassName, ...restDialogHeaderProps} = DialogHeaderProps ?? {};

  const titleNode = (titleProp => {
    switch (typeof titleProp) {
      case undefined:
        return undefined;
      case 'string':
        return (
          <MUITypography className="RegrowDialogTitle" variant="h3">
            {title}
          </MUITypography>
        );
      default:
        return titleProp;
    }
  })(title);

  const hasTitle = !!titleNode || !!subtitle;
  const hasHeader = hasCloseIcon || hasTitle;
  const hasSubtitle = !!subtitle;
  const hasFooter = !!DialogActionsProps?.children;

  return (
    <StyledDialog
      onClose={onClose}
      {...rest}
      $hasDividers={!!hasDividers}
      $hasFooter={hasFooter}
      $hasHeader={hasHeader}
      $hasTitle={hasTitle}
      $hasSubtitle={hasSubtitle}
      $hasCloseIcon={!!hasCloseIcon}
    >
      {hasHeader && (
        <Box
          {...restDialogHeaderProps}
          className={`RegrowDialogHeader${
            DialogHeaderClassName ? ` ${DialogHeaderClassName}` : ''
          }`}
        >
          {!!titleNode && titleNode}
          {!!subtitle && (
            <MUITypography className="RegrowDialogSubtitle" variant="body1">
              {subtitle}
            </MUITypography>
          )}
          {hasCloseIcon && (
            <MUIIconButton className="RegrowDialogCloseIcon" onClick={e => onClose(e, 'iconClick')}>
              <Icon type="cross" />
            </MUIIconButton>
          )}
        </Box>
      )}
      <MUIDialogContent dividers={hasDividers} {...DialogContentProps}>
        {children}
      </MUIDialogContent>
      {DialogActionsProps && <MUIDialogActions {...DialogActionsProps} />}
    </StyledDialog>
  );
};

const StyledDialog = styled(MUIDialog)<{
  $hasCloseIcon: boolean;
  $hasDividers: boolean;
  $hasFooter: boolean;
  $hasHeader: boolean;
  $hasSubtitle: boolean;
  $hasTitle: boolean;
}>(({theme, $hasCloseIcon, $hasDividers, $hasFooter, $hasHeader, $hasSubtitle, $hasTitle}) => {
  // in theme spacing units
  const iconSize = 5;
  const iconPadding = 2;
  const iconPosition = 3;

  return {
    '& .MuiPaper-root': {borderRadius: '8px'},
    ...($hasHeader
      ? {
          '& .RegrowDialogHeader': {
            minHeight:
              $hasCloseIcon && !$hasTitle
                ? theme.spacing(iconSize + iconPosition * 2 + iconPadding * 2)
                : 'none',
            position: 'relative',
            padding: (($hasTitle_: boolean, $hasCloseIcon_: boolean) => {
              switch (true) {
                case $hasTitle_ && $hasCloseIcon_:
                  return theme.spacing(5, iconSize + iconPadding * 2 + iconPosition * 2, 5, 5);
                case $hasTitle_:
                  return theme.spacing(5);
                default:
                  return 0;
              }
            })($hasTitle, $hasCloseIcon),
            ...($hasDividers ? {boxShadow: '0px 0px 4px rgba(0, 0, 0, 0.25)'} : {}),
          },
        }
      : {}),
    ...($hasSubtitle ? {'& .RegrowDialogSubtitle': {paddingTop: theme.spacing(2)}} : {}),
    ...($hasCloseIcon
      ? {
          '& .RegrowDialogCloseIcon ': {
            padding: theme.spacing(iconPadding),
            position: 'absolute',
            right: theme.spacing(iconPosition),
            top: theme.spacing(iconPosition),

            '& svg': {
              height: theme.spacing(iconSize),
              minWidth: 'unset',
              minHeight: 'unset',
              width: theme.spacing(iconSize),
            },
          },
        }
      : {}),
    '& .MuiDialogContent-root': {
      paddingBottom: theme.spacing(5),
      paddingLeft: theme.spacing(5),
      paddingRight: theme.spacing(5),
      paddingTop: !$hasHeader || ($hasHeader && $hasDividers) ? theme.spacing(5) : 0,
    },
    '& .MuiDialogContent-dividers': {
      ...(!$hasHeader ? {borderTop: 'none'} : {}),
      ...(!$hasFooter ? {borderBottom: 'none'} : {}),
    },
    ...($hasFooter
      ? {
          '& .MuiDialogActions-root': {
            padding: theme.spacing(5),
            ...($hasDividers ? {boxShadow: '0px 0px 4px rgba(0, 0, 0, 0.25)'} : {}),
          },
        }
      : {}),
  };
});

export default OldDialog;
