import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import {Box, Button, Divider, Typography} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';
import type {Theme} from '@mui/material';

const DIVIDER_VARIANTS = ['fullWidth', 'inset', 'middle'] as const;
const DIVIDER_ORIENTATIONS = ['horizontal', 'vertical'] as const;

const dividerArgTypeCategories = categorizeArgTypes(
  ['children', 'component', 'ref'],
  'MUI System Props'
);

const meta = {
  argTypes: {
    ...dividerArgTypeCategories,
  },
  component: Divider,
  title: 'components/Data Display/Divider',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16749',
    },
  },
} as Meta<ComponentProps<typeof Divider>>;

export default meta;
type Story = StoryObj<typeof Divider>;

export const Basic: Story = {
  render: props => (
    <Divider {...props}>
      <Typography>divider</Typography>
    </Divider>
  ),
};

export const Common: Story = {
  render: props => {
    return (
      <Box
        bgcolor="semanticPalette.surface.main"
        borderRadius="2"
        color="semanticPalette.text.main"
        p={8}
      >
        <Typography variant="h1">Hello World!</Typography>
        <Divider {...props} />
        <Box py={2}>
          <Typography variant="body1">
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic molestiae rem harum dolor
            quas, magni eius. Esse obcaecati debitis rerum ratione reiciendis amet cumque architecto
            officia fugit ipsa, accusamus molestias!
          </Typography>
        </Box>
      </Box>
    );
  },
};

export const Text: Story = {
  render: () => {
    return (
      <Box
        bgcolor="semanticPalette.surface.main"
        p={8}
        color="semanticPalette.text.main"
        borderRadius={2}
      >
        <Box py={2}>
          <Divider textAlign="center">
            <Typography>Center</Typography>
          </Divider>
        </Box>
        <Box py={2}>
          <Divider textAlign="left">
            <Typography>Left</Typography>
          </Divider>
        </Box>
        <Box py={2}>
          <Divider textAlign="right">
            <Typography>Right</Typography>
          </Divider>
        </Box>
      </Box>
    );
  },
};

export const List: Story = {
  render: props => {
    return (
      <ul>
        <li>1</li>
        <Divider {...props}>
          <Typography>divider</Typography>{' '}
        </Divider>
        <li>2</li>
        <Divider {...props}>
          <Typography>divider</Typography>{' '}
        </Divider>
      </ul>
    );
  },
};

export const Variants: Story = {
  render: props => {
    return (
      <>
        {DIVIDER_VARIANTS.map(variant => (
          <Box
            bgcolor="semanticPalette.surface.main"
            p={8}
            color="semanticPalette.text.main"
            borderRadius={2}
          >
            <Typography variant="h4">{variant}</Typography>
            <Divider
              {...props}
              variant={variant}
              sx={{
                backgroundColor: (theme: Theme) =>
                  theme.palette.semanticPalette.surfaceInverted.main,
              }}
            />
          </Box>
        ))}
      </>
    );
  },
};

export const Orientations: Story = {
  render: props => {
    return (
      <>
        {DIVIDER_ORIENTATIONS.map(orientation => (
          <Box
            bgcolor="semanticPalette.surface.main"
            p={8}
            color="semanticPalette.text.main"
            borderRadius={2}
            display={orientation === 'vertical' ? 'flex' : 'block'}
          >
            <Button color="secondary">{orientation}</Button>
            <Button color="secondary">{orientation}</Button>
            <Divider
              {...props}
              orientation={orientation}
              flexItem={orientation === 'vertical'}
              sx={{
                backgroundColor: (theme: Theme) =>
                  theme.palette.semanticPalette.surfaceInverted.main,
              }}
            />
            <Button color="secondary">{orientation}</Button>
            <Button color="secondary">{orientation}</Button>
          </Box>
        ))}
      </>
    );
  },
};
