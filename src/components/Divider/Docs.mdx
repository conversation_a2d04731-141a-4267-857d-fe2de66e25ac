import { Meta, <PERSON>vas, <PERSON>s, Story } from "@storybook/blocks";
import * as DividerStories from './index.stories.tsx'

<Meta of={DividerStories}/>

 ## Overview
A divider is a thin line that groups content in lists and layouts.
You can learn more here: [https://mui.com/material-ui/react-divider/](https://mui.com/material-ui/react-divider/)

<Canvas of={DividerStories.Basic} />
<Controls of={DividerStories.Basic}/>


## Usage

### Dividers are rendered as an hr by default and commonly used to separate content
<Canvas of={DividerStories.Common} />

### Dividers can be rendered with children
<Canvas of={DividerStories.Text} />

### Dividers can used to separate items in a list 

In a list, you should ensure the Divider is rendered as an `li` to match the HTML5
specification. The examples below show two ways of achieving this.
<Canvas of={DividerStories.List} />


## Variants
<Canvas of={DividerStories.Variants} />


## Orientations
Dividers can be rendered with different orientations.
You can also render a divider vertically using the orientation prop. When using a vertical Divider, you may need to add the flexItem prop to accomodate a parent flex container.
<Canvas of={DividerStories.Orientations} />
