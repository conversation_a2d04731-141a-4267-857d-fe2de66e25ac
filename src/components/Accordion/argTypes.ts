import {
  ANIMATION_ARGTYPES,
  FORM_COMPONENT_ARGTYPES,
  getArgTypes,
  SYSTEM_ARGTYPES,
} from 'src/storybook-utils/argTypes';
import {CATEGORY_KEYS} from 'src/tokens/constants';

const accordionArgTypeKeys = [
  'color',
  'size',
  'elevation',
  'square',
  'defaultExpanded',
  'expanded',
  'disabled',
  'onChange',
  'TransitionComponent',
  'TransitionProps',
  'classes',
  'children',
  'sx',
];

const ACCORDION_ARGTYPES = {
  color: {
    table: {
      type: {
        summary: 'enum',
      },
      defaultValue: {
        summary: 'undefined',
      },
    },
    control: 'select',
    options: ['default', ...CATEGORY_KEYS, undefined],
    description: 'The color of the component.',
  },
  size: {
    table: {
      type: {
        summary: 'enum',
      },
      defaultValue: {
        summary: 'medium',
      },
    },
    control: 'inline-radio',
    options: ['medium', 'small'],
    description: 'The size of the component.',
  },
  elevation: {
    description: 'Control the box shadow of the accordion',
    table: {
      defaultValue: {summary: 0},
    },
    control: {type: 'radio'},
    options: [0, 1],
  },
  expanded: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If <code>true</code>, the component is expanded.',
  },
  defaultExpanded: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'The default expanded state.',
  },
  square: {
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {
        summary: false,
      },
    },
    control: 'boolean',
    description: 'If <code>true</code>, rounded corners are disabled.',
  },
  onChange: {
    ...FORM_COMPONENT_ARGTYPES['onChange'],
    description:
      'Callback fired when the state is changed. <br /> <code>function(event: React.ChangeEvent, expanded: boolean) => void</code><br /><code>event</code> The event source of the callback.<br /><code>expanded</code>The <code>expanded</code> state of the accordion.',
  },
};

const argTypes = getArgTypes(accordionArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...ANIMATION_ARGTYPES,
  ...ACCORDION_ARGTYPES,
});

export default argTypes;
