import type {CategoryPalette} from 'src/tokens/themeTypes';
import {isCategoryColorKey, isDefined} from 'src/utils/typeGuards';
import {
  AccordionActions,
  accordionClasses,
  AccordionDetails,
  accordionDetailsClasses,
  AccordionSummary,
  accordionSummaryClasses,
  avatarClasses,
  listClasses,
  listItemAvatarClasses,
  listItemButtonClasses,
  listItemClasses,
  listItemIconClasses,
  listItemTextClasses,
  Accordion as MUIAccordion,
  type AccordionActionsProps,
  type AccordionDetailsProps,
  type AccordionSummaryProps,
  type AccordionProps as MUIAccordionProps,
} from '@mui/material';
import type {Components, Theme} from '@mui/material/styles';

import {SvgIcon} from 'src/components/Icon';

export const AccordionOverrides: Components<Theme>['MuiAccordion'] = {
  defaultProps: {
    disableGutters: true,
    elevation: 0,
    square: false,
  },
  styleOverrides: {
    root: ({theme, ownerState: {color, size}}) => ({
      border: `1px solid ${theme.palette.divider}`,
      borderLeft: 0,
      borderRight: 0,
      borderTop: 0,
      '&:last-child': {
        borderBottom: 0,
      },
      '&:before': {
        display: 'none',
      },
      [`&.${accordionClasses.disabled}`]: {
        backgroundColor: theme.palette.background.paper,
        color: theme.palette.text.disabled,
      },
      // Category Accordion Details Styles
      ...(isDefined(color) && {
        [`&.${accordionClasses.rounded}`]: {
          overflow: 'hidden',
        },
      }),
      // Accordion Summary Styles
      [`& .${accordionSummaryClasses.root}`]: {
        minHeight: theme.spacing(12),
        padding: theme.spacing(2, 3),
        // Category Accordion Summary Styles
        ...(isDefined(color) && {
          padding: theme.spacing(3),
          ...(color === 'default' && {
            backgroundColor: theme.palette.semanticPalette.surface.secondary,
            borderLeft: `${theme.spacing(2)} solid ${theme.palette.semanticPalette.stroke.main}`,
          }),
          ...(isCategoryColorKey(color) && {
            backgroundColor: theme.palette.categoryPalette[color].surface,
            borderLeft: `${theme.spacing(2)} solid ${
              theme.palette.categoryPalette[color].highlight
            }`,
          }),
        }),
        ...(size === 'small' && {
          minHeight: theme.spacing(6),
          padding: theme.spacing(1, 2),
        }),
        [`& .${accordionSummaryClasses.content}`]: {
          margin: 0,
        },
        [`& .${accordionSummaryClasses.expandIconWrapper}`]: {
          color: theme.palette.semanticPalette.text.main,
        },
      },
      // Accordion Details Styles
      [`& .${accordionDetailsClasses.root}`]: {
        padding: theme.spacing(2, 3, 4, 3),
        // Category Accordion Details Styles
        ...(isDefined(color) && {
          padding: theme.spacing(5),
          // Category Accordion List Styles
          [`& .${listClasses.root}`]: {
            margin: theme.spacing(-5),
            padding: 0,
            [`& .${listItemButtonClasses.root}, & .${listItemClasses.root}`]: {
              padding: theme.spacing(2.5, 3, 2.5, 5),
              height: theme.spacing(13),
              '&:hover': {
                backgroundColor: theme.palette.grey[200],
              },
            },
            [`& .${listItemButtonClasses.selected}`]: {
              backgroundColor: theme.palette.semanticPalette.surface.info,
            },
            [`& .${listItemTextClasses.root}`]: {
              marginBottom: 0,
              marginTop: 0,
            },
            [`& .${listItemIconClasses.root}, & .${listItemAvatarClasses.root}`]: {
              minWidth: 'auto',
              marginRight: theme.spacing(2),
            },
            [`& .${listItemAvatarClasses.root} .${avatarClasses.root}`]: {
              backgroundColor: theme.palette.semanticPalette.text.brand,
              height: theme.spacing(6),
              width: theme.spacing(6),
              svg: {
                fontSize: theme.typography.body2.lineHeight,
              },
            },
          },
        }),
        ...(size === 'small' && {
          padding: theme.spacing(1, 2, 3, 2),
        }),
      },
      // Note: MUI's form control label sets a negative left margin of 11px which breaks the alignment of the accordion summary.
      // We are overriding this here to fix the alignment.
      '.MuiFormControlLabel-root': {
        marginLeft: '-8px',
      },
    }),
  },
};

export const AccordionSummaryOverrides: Components<Theme>['MuiAccordionSummary'] = {
  defaultProps: {
    expandIcon: <SvgIcon type="chevron-down" />,
  },
};

type AccordionProps = Omit<MUIAccordionProps, 'variant' | 'disableGutters'> & {
  color?: keyof CategoryPalette | 'default';
  size?: 'small' | 'medium';
  elevation?: 0 | 1;
};

const AccordionComponent = (props: AccordionProps) => <MUIAccordion {...props} />;
AccordionComponent.displayName = 'Accordion';

export const Accordion = AccordionComponent;
Accordion.displayName = 'Accordion';

// @ts-expect-error storybook only name
AccordionDetails.displayName = 'AccordionDetails';

// @ts-expect-error storybook only name
AccordionSummary.displayName = 'AccordionSummary';

export {AccordionActions, AccordionDetails, AccordionSummary};
export type {AccordionProps, AccordionActionsProps, AccordionDetailsProps, AccordionSummaryProps};
