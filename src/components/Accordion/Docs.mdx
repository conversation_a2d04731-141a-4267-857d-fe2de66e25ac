import { Meta, <PERSON>vas, <PERSON>s, Story } from "@storybook/blocks";
import * as AccordionStories from './index.stories.tsx'

<Meta of={AccordionStories} />

## Overview
The `Accordion` component allows the user to show and hide sections of related content on a page.
The user determines what the content of the `Accordion` will be. 
**Note, for rounding to occur correctly, your Accordion or set of Accordions, need to be the only children within a containing element. 
To accomplish this, you can wrap your accordions in a `Box` component.**

The `Accordion` component can be controlled or uncontrolled.
- A component is controlled when it's managed by its parent using props.
- A component is uncontrolled when it's managed by its own local state

The Accordion is used in conjunction with:
- `AccordionSummary`
- `AccordionDetails`
- `AccordionActions`

[MUI Accordion](https://mui.com/material-ui/react-accordion/)

<Canvas of={AccordionStories.Basic} />
<Controls of={AccordionStories.Basic}/>

## Sizes
Accordions can be small or medium and are medium by default.
<Canvas of={AccordionStories.Sizes} />

## Props available via Paper (Elevations & Square)
To apply a shadow to the `Accordion` component, use the `elevation` prop alongside our `boxShadow` tokens
<Canvas of={AccordionStories.Elevations} />

To square the edges of an accordion with elevation, add the `square` prop.
<Canvas of={AccordionStories.Square} />

## Category Colors
<Canvas of={AccordionStories.CategoryColors} />

## Disabled
The `disabled` prop should be appiled as a `prop` to all children of `AccordionDetails`  and `AccordionActions` directly to ensure `disabled` behavior and styles appear on those child elements
<Canvas of={AccordionStories.Disabled} />

## Accordion Menu
Lists and List components can be used alongside Accordion components to generate a fixed navigation menu. For full customization controls, see docs on the full [MUI List components APIs](https://mui.com/material-ui/react-list/#api)
<Canvas of={AccordionStories.AccordionMenu} />

## Example Usages
The content of both the `AccordionSummary` and `AccordionDetails` are customizable. Below are examples of common use cases.
<Canvas of={AccordionStories.Examples} />

