import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Avatar,
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  FormGroup,
  List,
  ListItemAvatar,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Paper,
  Stack,
  SvgIcon,
  Switch,
  Typography,
} from 'src/index';
import {controlsExclude} from 'src/storybook-utils/argTypes';

import argTypes from './argTypes';

const meta: Meta<typeof Accordion> = {
  component: Accordion,
  title: 'components/Surfaces/Accordion',
  argTypes,
  parameters: {
    controls: {
      exclude: controlsExclude,
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=3801-75671&mode=design&t=RYqDy5fJmxt0rEjY-0',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Accordion>;

export const Basic: Story = {
  render: args => (
    <Box maxWidth={theme => theme.fixedWidths.md}>
      <Accordion {...args}>
        <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
          <Typography variant="h5">Accordion Header</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body1">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
            sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </Box>
  ),
};

export const Sizes: Story = {
  render: args => (
    <Box maxWidth={theme => theme.fixedWidths.md}>
      <Box mb={6}>
        <Box mb={2}>
          <Typography variant="h5" color="secondary">
            Small
          </Typography>
        </Box>
        <Box>
          <Accordion size="small" {...args}>
            <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
              <Typography variant="h6">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
                ex, sit amet blandit leo lobortis eget.
              </Typography>
            </AccordionDetails>
          </Accordion>
          <Accordion size="small" {...args}>
            <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
              <Typography variant="h6">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
                ex, sit amet blandit leo lobortis eget.
              </Typography>
            </AccordionDetails>
          </Accordion>
        </Box>
      </Box>
      <Box mb={6}>
        <Box mb={2}>
          <Typography variant="h5" color="secondary">
            Medium
          </Typography>
        </Box>
        <Box>
          <Accordion>
            <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
              <Typography variant="h5">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
                ex, sit amet blandit leo lobortis eget.
              </Typography>
            </AccordionDetails>
          </Accordion>
          <Accordion>
            <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
              <Typography variant="h5">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
                ex, sit amet blandit leo lobortis eget.
              </Typography>
            </AccordionDetails>
          </Accordion>
        </Box>
      </Box>
    </Box>
  ),
};

export const Elevations: Story = {
  render: (args, {theme}) => (
    <Box maxWidth={theme.fixedWidths.md} mb={6}>
      <Box mb={4}>
        <Typography variant="h5" color="secondary">
          Using elevation 1
        </Typography>
        <Typography>
          To achieve elevation 1 boxShadow, utilize the elevation prop directly on the Accordion.
        </Typography>
      </Box>
      <Box mb={4}>
        <Accordion elevation={1} {...args}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Accordion - elevation 1</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
        <Accordion elevation={1} {...args}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Accordion - elevation 1</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box mb={4}>
        <Typography variant="h5" color="secondary">
          Using elevations greater than 1 / our boxShadows tokens.
        </Typography>
      </Box>
      <Box mb={4}>
        <Typography>
          To achieve larger boxShadows, wrap your accordions (or set of accordions) in a Paper
          element. Then, utilize the elevation prop on the wrapper Paper element. This demonstrates
          a "medium" box shadow. This is done to prevent MUI box shadows from appearing between
          elements.
        </Typography>
      </Box>
      <Paper elevation={theme.boxShadows.md}>
        <Accordion>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Accordion - <code>{`theme.boxShadows.md`}</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
        <Accordion>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Accordion - <code>{`theme.boxShadow.md`}</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Paper>
    </Box>
  ),
};

export const Square: Story = {
  render: args => (
    <Box maxWidth={theme => theme.fixedWidths.md}>
      <Box mb={2}>
        <Typography variant="h5" color="secondary">
          Rounded (default)
        </Typography>
      </Box>
      <Box mb={2}>
        <Accordion elevation={1} {...args}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Accordion</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
        <Accordion elevation={1}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Accordion</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box my={2}>
        <Typography variant="h5" color="secondary">
          Square
        </Typography>
      </Box>
      <Box mb={2}>
        <Accordion square elevation={1}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Accordion - <code>square</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
        <Accordion square elevation={1}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Accordion - <code>square</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Box>
  ),
};

export const CategoryColors: Story = {
  render: (_args, {theme}) => (
    <Stack gap={5}>
      <Box>
        <Paper elevation={theme.boxShadows.sm}>
          <Accordion color="default">
            <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
              <Typography variant="h6">
                Color <code>default</code>
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
                ex, sit amet blandit leo lobortis eget.
              </Typography>
            </AccordionDetails>
          </Accordion>
        </Paper>
      </Box>
      <Box>
        <Accordion color="default" elevation={1}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>default</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="default">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>default</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="default" size="small">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h6">
              Color <code>default</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="1">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>1</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="2">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>2</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="3">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>3</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="4">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>4</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="5">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>5</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="6">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>6</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="7">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>7</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion color="8">
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">
              Color <code>8</code>
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Stack>
  ),
};

export const Disabled: Story = {
  render: args => (
    <Box maxWidth={theme => theme.fixedWidths.md} mb={6}>
      <Box>
        <Accordion disabled expanded {...args}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Accordion - disabled</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
            <AccordionActions>
              <Button variant="contained" color="primary" disabled>
                Button
              </Button>
            </AccordionActions>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box mt={4}>
        <Accordion color="1" disabled expanded {...args}>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Accordion - disabled</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus
              ex, sit amet blandit leo lobortis eget.
            </Typography>
            <AccordionActions>
              <Button variant="contained" color="primary" disabled>
                Button
              </Button>
            </AccordionActions>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Box>
  ),
};

export const CustomIcon: Story = {
  render: args => (
    <Box maxWidth={theme => theme.fixedWidths.md} mb={6}>
      <Accordion {...args}>
        <AccordionSummary
          aria-controls="panel1a-content"
          id="panel1a-header"
          expandIcon={<SvgIcon type="arrow-down" />}
        >
          <Typography variant="h5">Accordion - custom expand icon</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body1">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex,
            sit amet blandit leo lobortis eget.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </Box>
  ),
};

export const AccordionMenu: Story = {
  render: (args, {theme}) => (
    <Stack maxWidth={theme.fixedWidths.sm}>
      <Box mb={8}>
        <Accordion elevation={theme.boxShadows.sm} color="default" expanded>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Reporting Year 2023</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItemButton>
                <ListItemText id="switch-list-label-1" primary="Enrollment" secondary="#155" />
                <Switch
                  edge="end"
                  inputProps={{
                    'aria-labelledby': 'switch-list-label-1',
                  }}
                />
              </ListItemButton>
              <ListItemButton>
                <ListItemText id="switch-list-label-2" primary="Measurement" secondary="#156" />
                <Switch
                  edge="end"
                  inputProps={{
                    'aria-labelledby': 'switch-list-label-2',
                  }}
                />
              </ListItemButton>
              <ListItemButton selected>
                <ListItemText id="switch-list-label-3" primary="Dashboard" />
                <Switch
                  edge="end"
                  inputProps={{
                    'aria-labelledby': 'switch-list-label-3',
                  }}
                />
              </ListItemButton>
            </List>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box mb={8}>
        <Accordion elevation={theme.boxShadows.sm} color="default" expanded>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Agricultural Practices</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItemButton>
                <ListItemAvatar>
                  <Avatar>
                    <SvgIcon type="crop" />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText id="switch-list-label-field-1">
                  <Typography variant="h5">Crops</Typography>
                </ListItemText>
              </ListItemButton>
              <ListItemButton>
                <ListItemAvatar>
                  <Avatar>
                    <SvgIcon type="tillage" />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText id="switch-list-label-field-2">
                  <Typography variant="h5">Tillage</Typography>
                </ListItemText>
              </ListItemButton>
              <ListItemButton selected>
                <ListItemAvatar>
                  <Avatar>
                    <SvgIcon type="irrigation" />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText id="switch-list-label-field-3">
                  <Typography variant="h5">Irrigation</Typography>
                </ListItemText>
              </ListItemButton>
            </List>
          </AccordionDetails>
        </Accordion>
      </Box>
      <Box>
        <Accordion elevation={theme.boxShadows.sm} color="2" expanded>
          <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
            <Typography variant="h5">Farm 1</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItemButton>
                <ListItemIcon>
                  <SvgIcon type="map" />
                </ListItemIcon>
                <ListItemText id="switch-list-label-field-1">
                  <Typography variant="h5">Field 1</Typography>
                </ListItemText>
                <Typography variant="body2" color="secondary">
                  15 ac
                </Typography>
              </ListItemButton>
              <ListItemButton>
                <ListItemIcon>
                  <SvgIcon type="map" />
                </ListItemIcon>
                <ListItemText id="switch-list-label-field-2">
                  <Typography variant="h5">Field 2</Typography>
                </ListItemText>
                <Typography variant="body2" color="secondary">
                  13 ac
                </Typography>
              </ListItemButton>
              <ListItemButton>
                <ListItemIcon>
                  <SvgIcon type="map" />
                </ListItemIcon>
                <ListItemText id="switch-list-label-field-3">
                  <Typography variant="h5">Field 3</Typography>
                </ListItemText>
                <Typography variant="body2" color="secondary">
                  11 ac
                </Typography>
              </ListItemButton>
            </List>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Stack>
  ),
};

export const Examples: Story = {
  render: () => (
    <Box maxWidth={theme => theme.fixedWidths.md}>
      <Box mb={6}>
        <Box my={2}>
          <Typography variant="h5" color="secondary">
            Content Area
          </Typography>
        </Box>
        <Box>
          <Accordion>
            <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
              <Typography variant="h5">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1">
                This is an example of an accordion with a content area as opposed to multiple
                content items.
              </Typography>
            </AccordionDetails>
          </Accordion>
          <Accordion>
            <AccordionSummary aria-controls="panel2a-content" id="panel2a-header">
              <Typography variant="h5">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1" color="secondary">
                This is an example of an accordion with a content area as opposed to multiple
                content items.
              </Typography>
            </AccordionDetails>
          </Accordion>
        </Box>
      </Box>
      <Box mb={6}>
        <Box my={2}>
          <Typography variant="h5" color="secondary">
            Checkboxes
          </Typography>
        </Box>
        <Box>
          <Accordion>
            <AccordionSummary aria-controls="panel1a-content" id="panel1a-header">
              <Typography variant="body1">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormGroup>
                <Stack gap={2}>
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                </Stack>
              </FormGroup>
            </AccordionDetails>
          </Accordion>
          <Accordion>
            <AccordionSummary aria-controls="panel2a-content" id="panel2a-header">
              <Typography variant="body1">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormGroup>
                <Stack gap={2}>
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                </Stack>
              </FormGroup>
            </AccordionDetails>
          </Accordion>
          <Accordion>
            <AccordionSummary aria-controls="panel2a-content" id="panel2a-header">
              <Typography variant="body1">Accordion Header</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormGroup>
                <Stack gap={2}>
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                  <FormControlLabel control={<Checkbox />} label="Accordion Item" />
                </Stack>
              </FormGroup>
            </AccordionDetails>
          </Accordion>
        </Box>
      </Box>
    </Box>
  ),
};
