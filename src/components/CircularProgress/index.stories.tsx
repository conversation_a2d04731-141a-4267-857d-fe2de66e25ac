import type {<PERSON>a, StoryObj} from '@storybook/react';
import {CircularProgress} from 'src/index';

const meta: Meta<typeof CircularProgress> = {
  component: CircularProgress,
  title: 'components/Feedback/CircularProgress',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=3801-75678&node-type=canvas&t=H7558FdqP84ljvGb-0',
    },
  },
};

export default meta;
type Story = StoryObj<typeof CircularProgress>;

export const Basic: Story = {
  render: args => <CircularProgress {...args} />,
};
