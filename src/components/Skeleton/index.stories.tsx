import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {TypographyProps} from 'src/index';
import {Box, Stack, Typography} from 'src/index';
import {Avatar, Grid} from '@mui/material';

import {Skeleton, type SkeletonProps} from '.';

const meta: Meta<typeof Skeleton> = {
  component: Skeleton,
  title: 'components/Feedback/Skeleton',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1543%3A16394&t=zYDQB11EvLWX7O2Y-4',
    },
  },
};

export default meta;

export const Basic: StoryObj<SkeletonProps> = {
  args: {variant: 'rectangular', width: '250px', height: '50px'},

  render: args => {
    return (
      <Stack spacing={1}>
        <Skeleton {...args} />
      </Stack>
    );
  },
};

export const Variants: StoryObj<SkeletonProps> = {
  render: () => {
    return (
      <Box>
        <Typography>
          The component supports 4 shape variants:
          <ul>
            <li>
              <code>text</code> (default): represents a single line of text (you can adjust the
              height via font size).
            </li>
            <li>
              <code>circular</code>, <code>rectangular</code>, and <code>rounded</code>: come with
              different border radius to let you take control of the size.
            </li>
          </ul>
        </Typography>
        <Stack spacing={1}>
          <Skeleton variant="text" width={210} />
          <Skeleton variant="circular" width={40} height={40} />
          <Skeleton variant="rectangular" width={210} height={60} />
          <Skeleton variant="rounded" width={210} height={60} />
        </Stack>
      </Box>
    );
  },
};

const typographyVariants = [
  'h1',
  'h3',
  'body1',
  'caption',
] as readonly TypographyProps<any>['variant'][];

export const Dimensions: StoryObj<SkeletonProps> = {
  render: () => {
    return (
      <Box>
        <Box mb={5}>
          <Typography typography="p">
            In addition to accepting <code>width</code> and <code>height</code> props, the component
            can also infer the dimensions.
          </Typography>
        </Box>
        <Grid container spacing={8}>
          <Grid item xs>
            {typographyVariants.map(variant => (
              <Typography component="div" key={variant} variant={variant}>
                {<Skeleton />}
              </Typography>
            ))}
            <Skeleton variant="circular">
              <Avatar />
            </Skeleton>
          </Grid>
          <Grid item xs>
            {typographyVariants.map(variant => (
              <Typography component="div" key={variant} variant={variant}>
                {variant}
              </Typography>
            ))}
            <Avatar />
          </Grid>
        </Grid>
      </Box>
    );
  },
};
