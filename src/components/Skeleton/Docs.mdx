import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as SkeletonStories from './index.stories.tsx'

<Meta of={SkeletonStories} />

## Overview
The `Skeleton` component allows for displaying a placeholder preview of your content before the data gets loaded to reduce load-time frustration.

You can learn more here: [https://mui.com/material-ui/react-skeleton/](https://mui.com/material-ui/react-skeleton/)

<Canvas of={SkeletonStories.Basic} />
<Controls of={SkeletonStories.Basic}/>

## Skeleton Variants
<Canvas of={SkeletonStories.Variants} />

## Skeleton Dimensions
<Canvas of={SkeletonStories.Dimensions} />
