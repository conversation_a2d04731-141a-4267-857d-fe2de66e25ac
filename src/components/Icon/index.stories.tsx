import type {<PERSON>a, StoryObj} from '@storybook/react';
import {<PERSON>, Stack, Tooltip, Typography} from 'src/index';
import {ICON_CROPS_MAP, ICON_MAP} from 'src/tokens/icons';

import {SvgIcon, type IconType, type SvgIconProps} from 'src/components/Icon';
import {Icon as LegacyIcon} from 'src/components/Icon/Icon';
import {StoryGrid, StoryWrapper} from 'src/storybook-utils/story-components';

const meta: Meta<typeof SvgIcon> = {
  component: SvgIcon,
  title: 'components/Data Display/SvgIcon',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1543%3A16394&t=zYDQB11EvLWX7O2Y-4',
    },
  },
};

export default meta;

const sortedIcons = Object.keys(ICON_MAP).toSorted((a, b) => a.localeCompare(b)) as IconType[];
const sortedCropIcons = Object.keys(ICON_CROPS_MAP).toSorted((a, b) =>
  a.localeCompare(b)
) as IconType[];

export const Basic: StoryObj<SvgIconProps> = {
  args: {type: sortedIcons[0], color: 'inherit', fontSize: 'large'},
  argTypes: {
    type: {
      description: 'The name of the icon.',
      control: {type: 'select', labels: sortedIcons},
      options: sortedIcons,
    },
    color: {
      description:
        'The color of the component. It supports those theme colors that make sense for this component. Defaults to inherit.',
      control: {type: 'select'},
      options: ['primary', 'secondary', 'success', 'action', 'inherit', 'disabled'],
      table: {
        defaultValue: {
          summary: 'inherit',
        },
      },
    },
    fontSize: {
      description: 'The size applied to the icon. Defaults to inherit.',
      control: {type: 'select'},
      options: [
        'inherit',
        'large',
        'medium',
        'small',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'body1',
        'body2',
      ],
      table: {
        defaultValue: {
          summary: 'inherit',
        },
      },
    },
  },
  parameters: {
    layout: 'centered',
  },
  render: (args: any) => {
    return <SvgIcon type={sortedIcons[0]} {...args} />;
  },
};

export const Sizes: StoryObj<SvgIconProps> = {
  render: () => {
    return (
      <Stack gap={4}>
        <Typography variant="h1">
          <SvgIcon type="search" fontSize="h1" /> h1 font size example
        </Typography>
        <Typography variant="h2">
          <SvgIcon type="search" fontSize="h2" /> h2 font size example
        </Typography>
        <Typography variant="h3">
          <SvgIcon type="search" fontSize="h3" /> h3 font size example
        </Typography>
        <Typography variant="h4">
          <SvgIcon type="search" fontSize="h4" /> h4 font size example
        </Typography>
        <Typography variant="h5">
          <SvgIcon type="search" fontSize="h5" /> h5 font size example
        </Typography>
        <Typography variant="h5">
          <SvgIcon type="search" fontSize="h6" /> h6 font size example
        </Typography>
        <Typography variant="body1">
          <SvgIcon type="search" fontSize="body1" /> body1 font size example
        </Typography>
        <Typography variant="body2">
          <SvgIcon type="search" fontSize="body2" /> body2 font size example
        </Typography>

        <Box mr={4}>
          <SvgIcon type="farm" fontSize="small" />
          <Typography variant="body1">Small</Typography>
        </Box>
        <Box mr={4}>
          <SvgIcon type="farm" fontSize="medium" />
          <Typography variant="body1">medium</Typography>
        </Box>
        <Box mr={4}>
          <SvgIcon type="farm" fontSize="large" />
          <Typography variant="body1">Large</Typography>
        </Box>
        <Typography>
          Note, when the fontSize of an SvgIcon is set to inherit within a Typography element, it
          inherits the parent Typography component's <strong>font-size</strong>, rather than the
          line-height. As such the icon's vertical alignment will likely need adjustment.
        </Typography>
        <Typography variant="body1" component="span">
          <SvgIcon type="search" fontSize="inherit" /> inherit font size example
        </Typography>
      </Stack>
    );
  },
};

export const Colors: StoryObj<SvgIconProps> = {
  render: () => (
    <Stack>
      <Box>
        <SvgIcon type="farm" fontSize="medium" color="inherit" />
        <Typography variant="body1">inherit</Typography>
      </Box>
      <Box>
        <SvgIcon type="farm" fontSize="medium" color="primary" />
        <Typography variant="body1">primary</Typography>
      </Box>
      <Box>
        <SvgIcon type="farm" fontSize="medium" color="secondary" />
        <Typography variant="body1">secondary</Typography>
      </Box>
      <Box>
        <SvgIcon type="farm" fontSize="medium" color="error" />
        <Typography variant="body1">error</Typography>
      </Box>
      <Box>
        <SvgIcon type="farm" fontSize="medium" color="disabled" />
        <Typography variant="body1">disabled</Typography>
      </Box>
    </Stack>
  ),
};

export const IconWithTooltip: StoryObj<SvgIconProps> = {
  parameters: {
    layout: 'centered',
  },
  render: () => (
    <Tooltip
      title="To show a tooltip on an SvgIcon, it must be wrapped within another element (div, span, Box, etc)"
      id=""
    >
      <Box>
        <SvgIcon type="question-circled" fontSize="h3" />
      </Box>
    </Tooltip>
  ),
};

export const CropIcons: StoryObj<SvgIconProps> = {
  render: () => {
    return (
      <StoryGrid>
        {sortedCropIcons.map((type: any) => (
          <WrappedIcon key={type} type={type} fontSize="h3" />
        ))}
      </StoryGrid>
    );
  },
};

export const AllIcons: StoryObj<SvgIconProps> = {
  render: () => {
    return (
      <StoryGrid>
        {sortedIcons.map((type: any) => (
          <WrappedIcon key={type} type={type} fontSize="h3" />
        ))}
      </StoryGrid>
    );
  },
};

function WrappedIcon(props: SvgIconProps) {
  return (
    <StoryWrapper>
      <SvgIcon {...props} />
      <Box mt={3}>
        <Typography variant="body1" color="secondary">
          {props.type}
        </Typography>
      </Box>
    </StoryWrapper>
  );
}

export const LegacyIconBasic = () => {
  return <LegacyIcon type="accreditation" size={48} />;
};
