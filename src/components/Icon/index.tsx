import {TYPOGRAPHY_VARIANTS} from 'src/tokens/constants';
import {type IconType} from 'src/tokens/themeTypes';
import {
  SvgIcon as MuiSvgIcon,
  type SvgIconProps as MuiSvgIconProps,
  type TypographyProps,
} from '@mui/material/';
import type {Components, Theme} from '@mui/material/styles';

import {Icon} from 'src/components/Icon/Icon';
import {Path} from 'src/components/Icon/Path';

export const isTypographyVariant = (x: unknown): x is typeof TYPOGRAPHY_VARIANTS[number] =>
  TYPOGRAPHY_VARIANTS.includes(x as typeof TYPOGRAPHY_VARIANTS[number]);

export const SVGIconOverrides: Components<Theme>['MuiSvgIcon'] = {
  defaultProps: {
    fontSize: 'inherit',
    color: 'inherit',
  },
  styleOverrides: {
    root: ({ownerState: {fontSize}, theme}) => ({
      '&.MuiSvgIcon-colorMain': {
        color: theme.palette.text.primary,
      },
      ...(isTypographyVariant(fontSize) && {
        fontSize: theme.typography[fontSize].lineHeight,
        verticalAlign: 'bottom',
      }),
    }),
    fontSizeSmall: ({theme}) => ({
      fontSize: theme.typography.h5.lineHeight,
    }),
    fontSizeMedium: ({theme}) => ({
      fontSize: theme.typography.h4.lineHeight,
    }),
    fontSizeLarge: ({theme}) => ({
      fontSize: theme.typography.h3.lineHeight,
    }),
    colorDisabled: ({theme}) => ({
      color: theme.palette.text.disabled,
    }),
  },
};

type SvgIconProps = MuiSvgIconProps & {
  type: IconType;
  fontSize?: TypographyProps['variant'] | 'inherit' | 'large' | 'medium' | 'small';
};

const SvgIcon = (props: SvgIconProps) => (
  <MuiSvgIcon {...props}>
    <Path type={props.type} />
  </MuiSvgIcon>
);

export {SvgIcon, type SvgIconProps, Icon, IconType};
