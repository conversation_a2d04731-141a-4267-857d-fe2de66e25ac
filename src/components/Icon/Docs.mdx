import { <PERSON>a, <PERSON><PERSON>, <PERSON><PERSON>, Story } from "@storybook/blocks";
import * as IconStories from './index.stories.tsx'

<Meta of={IconStories} />

## Overview
The `SvgIcon` component replaces the deprecated `Icon` component allowing us to use theme aware props when implementing icons.

There are a different sets of icons available as we transition themes from Flurosense to Regrow.

<Canvas of={IconStories.Basic} />
<Controls of={IconStories.Basic}/>

## Icon Sizing
By default, the `SvgIcon` component will inherit the font size of the parent element.
          
To align an icon properly with text, set the `fontSize` property to be the value of its parent `Typography` element.
For example, if you have an icon inside of an h3 element the `fontSize` property on the
`SvgIcon` should be h3 (`fontSize="h3"`). This sets the `lineHeight` of the icon to be the `fontSize` of the
parent text which aligns the icon correctly.

There are three size options that exist in addition to type sizes: small, medium and
large. These should be used in cases where an icon exists outside of a type component and needs to have a specific size.

<Canvas of={IconStories.Sizes} />

## Icon Color
<Canvas of={IconStories.Colors} />

## @deprecated `Icon`
The usage of the `Icon` component is deprecated and replaced by the `SvgIcon` component. The `SvgIcon` component is theme aware and allows us to use theme aware props when implementing icons.
`Icon` is still available for use but will be removed after all icons have been migrated to `SvgIcon`.
<Canvas of={IconStories.LegacyIconBasic} />
