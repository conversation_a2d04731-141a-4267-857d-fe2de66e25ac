import type {CSSProperties} from 'react';
import {type IconType} from 'src/tokens/themeTypes';
import {useTheme} from '@mui/material';

import {Path, SVG} from 'src/components/Icon/Path';

export type IconProps = {
  className?: string;
  /**
   * @default "black"
   */
  color?: string;
  /**
   * @default 20
   */
  size?: number;
  type: IconType;
  style?: CSSProperties;
};
/**
 * @deprecated use SvgIcon instead. SvgIcon is theme aware.
 * Generally when using an Icon inline with text, the size should equal the line-height of the text.
 */
function Icon({className, color, size = 20, type, style}: IconProps): JSX.Element {
  const theme = useTheme();
  return (
    <SVG
      fontSize={size}
      aria-hidden={true}
      className={className}
      style={style}
      fill={color ?? theme.palette.text.primary}
      height={size}
      width={size}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      size={size}
    >
      <Path type={type} />
    </SVG>
  );
}

export {Icon};
