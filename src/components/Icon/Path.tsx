import {THEME_KEYS} from 'src/tokens/constants';
import {FLURO_ICONS} from 'src/tokens/Flurosense/icons';
import {ICON_MAP} from 'src/tokens/icons';
import {type IconMapType, type IconType} from 'src/tokens/themeTypes';
import {styled, useTheme} from '@mui/material';

import type {IconProps} from 'src/components/Icon/Icon';

type PathProps = {
  type: IconType;
};

export function Path({type}: PathProps): JSX.Element | null {
  const DEFAULT_ICON_MAP: Record<IconType, JSX.Element> = ICON_MAP;
  const FLURO_ICON_MAP: Record<IconType, JSX.Element> = {...ICON_MAP, ...FLURO_ICONS};

  const ICON_MAPS: Record<THEME_KEYS, IconMapType> = {
    [THEME_KEYS.FLUROSENSE_LEGACY]: FLURO_ICON_MAP,
    [THEME_KEYS.REGROW_DEFAULT]: DEFAULT_ICON_MAP,
  };

  const getIconMaps = (themeKey: THEME_KEYS) => {
    return ICON_MAPS[themeKey] ?? DEFAULT_ICON_MAP;
  };

  const {name} = useTheme();

  const themeIconPaths = getIconMaps(name);

  return themeIconPaths[type];
}

export const SVG = styled('svg')<{size: IconProps['size']}>`
  transform-box: fill-box;
  /* Prevent line-height from influencing parent element */
  vertical-align: top;

  ${({size}) => `
      min-width: ${size}px;
      min-height: ${size}px;
    `};
`;
