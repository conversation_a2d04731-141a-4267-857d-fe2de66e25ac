import type {ForwardedRef} from 'react';
import {forwardRef} from 'react';
import {
  Switch as MuiSwitch,
  switchClasses,
  type Components,
  type SwitchProps as MuiSwitchProps,
  type Theme,
} from '@mui/material';

export const SwitchOverrides: Components<Theme>['MuiSwitch'] = {
  defaultProps: {
    disableRipple: true,
    disableFocusRipple: true,
    disableTouchRipple: true,
  },
  styleOverrides: {
    root: ({theme}) => ({
      width: 45,
      height: 24,
      padding: 0,
      marginLeft: theme.spacing(2),
      marginRight: theme.spacing(2),
      [`&.${switchClasses.edgeStart}`]: {
        marginLeft: 0,
      },
      [`&.${switchClasses.edgeEnd}`]: {
        marginRight: 0,
      },
      [`& .${switchClasses.switchBase}`]: {
        padding: theme.spacing(0),
        margin: theme.spacing(0.5),
        minWidth: 20,
        transitionDuration: '300ms',
        '&:hover': {
          backgroundColor: 'transparent',
        },
        [`&.${switchClasses.disabled}`]: {
          [`.${switchClasses.thumb}`]: {
            color: theme.palette.semanticPalette.surface.main,
          },
          [`+ .${switchClasses.track}`]: {
            backgroundColor: theme.palette.semanticPalette.highlight.main,
            opacity: 0.5,
          },
        },
        [`&.${switchClasses.checked}`]: {
          transform: 'translateX(21px)',
          color: theme.palette.semanticPalette.surface.main,
          '&:hover': {
            backgroundColor: 'transparent',
          },
          [`+ .${switchClasses.track}`]: {
            backgroundColor: theme.palette.primary.main,
            opacity: 1,
            border: 0,
          },
          [`&.${switchClasses.disabled}`]: {
            [`.${switchClasses.thumb}`]: {
              color: theme.palette.semanticPalette.textInverted.main,
            },
            [`+ .${switchClasses.track}`]: {
              backgroundColor: theme.palette.semanticPalette.text.success,
              opacity: 0.5,
            },
          },
        },
      },
    }),
    track: ({theme}) => ({
      borderRadius: theme.borderRadii.pill,
      backgroundColor: theme.palette.grey[300],
      opacity: 1,
    }),
  },
};

type SwitchProps = Omit<MuiSwitchProps, 'checkedIcon' | 'icon'>;

const SwitchComponent = (props: SwitchProps, ref: ForwardedRef<HTMLButtonElement>) => (
  <MuiSwitch ref={ref} {...props} />
);

export const Switch = forwardRef(SwitchComponent);
export type {SwitchProps};
