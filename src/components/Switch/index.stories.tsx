import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {FormControlLabel, Stack, Switch} from 'src/index';
import {controlsExclude} from 'src/storybook-utils/argTypes';

import argTypes from './argTypes';

type Story = StoryObj<typeof Switch>;

const meta: Meta<typeof Switch> = {
  component: Switch,
  title: 'components/Inputs/Switch',
  // Note: Auto-generated by props are not working properly here. This is our alternative for now.
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=3812%3A44832&mode=dev',
    },
    controls: {
      exclude: controlsExclude,
    },
  },
};

export default meta;

export const Basic: Story = {
  render: args => <Switch {...args} />,
};

export const States = {
  render: () => (
    <Stack gap={4}>
      <FormControlLabel control={<Switch defaultChecked />} label="True & Enabled" />
      <FormControlLabel control={<Switch />} label="False & Enabled" />
      <FormControlLabel control={<Switch defaultChecked disabled />} label="True & Disabled" />
      <FormControlLabel control={<Switch disabled />} label="False & Disabled" />
    </Stack>
  ),
};

export const LabelPlacement = {
  render: () => (
    <>
      <FormControlLabel value="end" control={<Switch />} label="End" labelPlacement="end" />
      <FormControlLabel value="top" control={<Switch />} label="Top" labelPlacement="top" />
      <FormControlLabel
        value="bottom"
        control={<Switch />}
        label="Bottom"
        labelPlacement="bottom"
      />
      <FormControlLabel value="start" control={<Switch />} label="Start" labelPlacement="start" />
    </>
  ),
};

export const EdgeUsage = {
  render: () => (
    <Stack gap={4}>
      <FormControlLabel value="end" control={<Switch />} label="no edge defined" />
      <FormControlLabel value="top" control={<Switch edge="start" />} label="start" />
      <FormControlLabel
        value="bottom"
        control={<Switch />}
        labelPlacement="start"
        label="no edge defined"
      />
      <FormControlLabel
        value="bottom"
        control={<Switch edge="end" />}
        labelPlacement="start"
        label="end"
      />
    </Stack>
  ),
};
