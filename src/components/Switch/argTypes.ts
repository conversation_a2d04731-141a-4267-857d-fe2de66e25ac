import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const switchArgTypeKeys = [
  'checked',
  'classes',
  'color',
  'defaultChecked',
  'disabled',
  'edge',
  'id',
  'inputProps',
  'inputRef',
  'onChange',
  'required',
  'size',
  'sx',
  'value',
];

const SWITCH_ARGTYPES = {
  color: {
    description:
      'The color of the component. Restricted to support only the `default` color per the figma designs',
    options: ['default'],
    control: {
      type: 'radio',
    },
    defaultValue: 'default',
  },
  onChange: {
    ...FORM_COMPONENT_ARGTYPES.onChange,
    description:
      'Callback fired when the state is changed. <br /> <code>function(event: React.ChangeEvent) => void</code><br /><code>event</code> The event source of the callback. You can pull out the new checked state by accessing <br /><code>event.target.checked</code>',
  },
  size: {
    description: 'The size of the component. Supports `medium` per design system requirements.',
    options: ['medium'],
    control: {
      type: 'radio',
    },
  },
  value: {
    description:
      'The value of the component. The DOM API casts this to a string. The browser uses "on" as the default value.',
    options: ['on', 'off'],
    control: {
      type: 'radio',
    },
  },
};

const argTypes = getArgTypes(switchArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...SWITCH_ARGTYPES,
});

export default argTypes;
