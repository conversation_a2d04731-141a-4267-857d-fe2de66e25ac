import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as SwitchStories from './index.stories.tsx'

<Meta of={SwitchStories}/>

## Overview
`Switch` should be composed with `FormControlLabel`.

[MUI Switch](https://mui.com/material-ui/react-switch/)

<Canvas of={SwitchStories.Basic} />
<Controls of={SwitchStories.Basic}/>


## States

<Canvas of={SwitchStories.States} />

## Label placement usage

<Canvas of={SwitchStories.LabelPlacement} />

## Edge usage

The edge property can be used to remove the left or right margin from the switch control to facilitate better alignment.
<Canvas of={SwitchStories.EdgeUsage} />
