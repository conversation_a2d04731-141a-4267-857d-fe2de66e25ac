import {DataGridOverrides} from 'src/lab/DataGrid';
import type {ThemeOptions} from '@mui/material';

import {AccordionOverrides, AccordionSummaryOverrides} from 'src/components/Accordion';
import {AlertOverrides, AlertTitleOverrides} from 'src/components/Alert';
import {ButtonOverrides, IconButtonOverrides, LoadingButtonOverrides} from 'src/components/Button';
import {ButtonBaseOverrides} from 'src/components/ButtonBase';
import {ButtonGroupOverrides} from 'src/components/ButtonGroup';
import {CheckboxOverrides} from 'src/components/Checkbox';
import {ChipOverrides} from 'src/components/Chip';
import {SVGIconOverrides} from 'src/components/Icon';
import {FilledInputOverrides} from 'src/components/Inputs/FilledInput';
import {FormHelperTextOverrides} from 'src/components/Inputs/FormHelperText';
import {FormLabelOverrides} from 'src/components/Inputs/FormLabel';
import {InputAdornmentOverrides} from 'src/components/Inputs/InputAdornment';
import {InputBaseOverrides} from 'src/components/Inputs/InputBase';
import {OutlinedInputOverrides} from 'src/components/Inputs/OutlinedInput';
import {SelectOverrides} from 'src/components/Inputs/Select';
import {TextFieldOverrides} from 'src/components/Inputs/TextField';
import {LinkOverrides} from 'src/components/Link';
import {MenuItemOverrides, MenuOverrides} from 'src/components/Menu';
import {TabOverrides, TabsOverrides} from 'src/components/Navigation/Tabs';
import {PaperOverrides} from 'src/components/Paper';
import {
  DialogActionsOverrides,
  DialogContentOverrides,
  DialogOverrides,
  DialogTitleOverrides,
} from 'src/components/SimpleDialog';
import {
  StepButtonOverrides,
  StepConnectorOverrides,
  StepContentOverrides,
  StepIconOverrides,
  StepLabelOverrides,
  StepOverrides,
} from 'src/components/Stepper';
import {SwitchOverrides} from 'src/components/Switch';
import {TableCellOverrides} from 'src/components/Table';
import {ToggleButtonOverrides} from 'src/components/ToggleButton';
import {ToggleButtonGroupOverrides} from 'src/components/ToggleButtonGroup';
import {TooltipOverrides} from 'src/components/Tooltip';

import {AutocompleteOverrides} from './Autocomplete/AutocompleteOverrides';
import {RadioOverrides} from './Radio';

export const componentThemeOverrides: ThemeOptions['components'] = {
  MuiButton: ButtonOverrides,
  MuiButtonBase: ButtonBaseOverrides,
  MuiButtonGroup: ButtonGroupOverrides,
  MuiCheckbox: CheckboxOverrides,
  MuiToggleButton: ToggleButtonOverrides,
  MuiToggleButtonGroup: ToggleButtonGroupOverrides,
  MuiIconButton: IconButtonOverrides,
  MuiLoadingButton: LoadingButtonOverrides,
  MuiChip: ChipOverrides,
  MuiInputAdornment: InputAdornmentOverrides,
  MuiInputBase: InputBaseOverrides,
  MuiFilledInput: FilledInputOverrides,
  MuiOutlinedInput: OutlinedInputOverrides,
  MuiTooltip: TooltipOverrides,
  MuiTextField: TextFieldOverrides,
  MuiSelect: SelectOverrides,
  MuiFormLabel: FormLabelOverrides,
  MuiFormHelperText: FormHelperTextOverrides,
  MuiMenu: MenuOverrides,
  MuiMenuItem: MenuItemOverrides,
  MuiPaper: PaperOverrides,
  MuiTableCell: TableCellOverrides,
  MuiSwitch: SwitchOverrides,
  MuiAccordion: AccordionOverrides,
  MuiAccordionSummary: AccordionSummaryOverrides,
  MuiDataGrid: DataGridOverrides,
  MuiSvgIcon: SVGIconOverrides,
  MuiStep: StepOverrides,
  MuiStepLabel: StepLabelOverrides,
  MuiStepIcon: StepIconOverrides,
  MuiStepButton: StepButtonOverrides,
  MuiStepConnector: StepConnectorOverrides,
  MuiStepContent: StepContentOverrides,
  MuiDialog: DialogOverrides,
  MuiDialogTitle: DialogTitleOverrides,
  MuiDialogContent: DialogContentOverrides,
  MuiDialogActions: DialogActionsOverrides,
  MuiAlert: AlertOverrides,
  MuiAlertTitle: AlertTitleOverrides,
  MuiTab: TabOverrides,
  MuiTabs: TabsOverrides,
  MuiAutocomplete: AutocompleteOverrides,
  MuiRadio: RadioOverrides,
  MuiLink: LinkOverrides,
};
