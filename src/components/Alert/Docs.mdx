
import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import { <PERSON><PERSON> } from "./index";
import * as AlertStories from './index.stories'

<Meta of={AlertStories}/>

## Overview
Alerts display brief messages for the user without interrupting their use of the app.

[MUI Alert](https://mui.com/material-ui/api/alert/)

<Canvas of={AlertStories.Basic} />
<Controls of={AlertStories.Basic}/>

## Severities
The severity of the alert can be set using the `severity` prop. The severity can be one of the following: `error`, `info`, `success`, `warning`.
<Canvas of={AlertStories.Severities} />


## With title
Title can be used to give better intention to your alert. You can add it by using the `AlertTitle` component. 
<Canvas of={AlertStories.WithTitle}/> 

## With action
Add an action to your Alert with the `action` prop. This lets you insert any element—an HTML tag, an SVG icon, or a React component such as a Button—after the <PERSON><PERSON>'s message, justified to the right.

If you add a `onClose` prop, by default it'll add the close button to the right of the action. See the Dismissible section for more details.
<Canvas of={AlertStories.WithAction}/>

## Dismissible
Dismissible alerts can be closed by the user. You can enable this feature by using the `onClose` prop.
<Canvas of={AlertStories.Dismissible}/>