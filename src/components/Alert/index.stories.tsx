import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Button, Stack} from 'src/index';

import argTypes from './argTypes';

type Story = StoryObj<typeof Alert>;

const meta: Meta<typeof Alert> = {
  argTypes,
  component: Alert,
  title: 'components/Feedback/Alert',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=634%3A14382',
    },
    layout: 'centered',
  },
};

export default meta;

export const Basic: Story = {
  args: {
    severity: 'success',
  },
  render: args => (
    <Stack width={theme => theme.fixedWidths.md} gap={2}>
      <Alert {...args}>This is a {args.severity} message</Alert>
    </Stack>
  ),
};

export const Severities: Story = {
  render: args => (
    <Stack width={theme => theme.fixedWidths.md} gap={2}>
      <Alert {...args} severity="success">
        This is a success message
      </Alert>
      <Alert {...args} severity="warning">
        This is a warning message
      </Alert>
      <Alert {...args} severity="error">
        This is an error message
      </Alert>
      <Alert {...args} severity="info">
        This is an info message
      </Alert>
    </Stack>
  ),
};

export const WithTitle: Story = {
  render: args => (
    <Stack width={theme => theme.fixedWidths.md} gap={2}>
      <Alert {...args} severity="success">
        <AlertTitle>Success</AlertTitle>
        This is a success message
      </Alert>
      <Alert {...args} severity="success">
        <AlertTitle>Info</AlertTitle>
        This is an info message with super large text. Lorem ipsum dolor sit amet, consectetur
        adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </Alert>
    </Stack>
  ),
};

export const WithAction: Story = {
  args: {
    action: (
      <Button size="small" variant="outlined">
        Undo
      </Button>
    ),
  },
  render: args => (
    <Stack width={theme => theme.fixedWidths.md} gap={2}>
      <Alert {...args} severity="success">
        This is a success message with an action
      </Alert>
    </Stack>
  ),
};

export const Dismissible: Story = {
  args: {
    onClose: () => {},
    title: 'Dismissible Alert',
  },
  render: args =>
    (() => {
      const [open, setOpen] = React.useState(true);
      return (
        <Stack width={theme => theme.fixedWidths.md} gap={2}>
          {open && (
            <Alert {...args} onClose={() => setOpen(false)}>
              You can click this x button to dismiss this alert
            </Alert>
          )}
          <Button onClick={() => setOpen(true)} disabled={open}>
            Reopen Alert
          </Button>
        </Stack>
      );
    })(),
};
