import type {AlertTitleProps, Components, AlertProps as MuiAlertProps, Theme} from '@mui/material';
import {AlertTitle, Alert as <PERSON><PERSON><PERSON><PERSON><PERSON>} from '@mui/material';

import {SvgIcon} from 'src/components/Icon';

const DEFAULT_SEVERITY = 'success';

export const AlertOverrides: Components<Theme>['MuiAlert'] = {
  defaultProps: {
    variant: 'outlined',
    severity: DEFAULT_SEVERITY,
    iconMapping: {
      success: <SvgIcon type="check-mark-circled" />,
      warning: <SvgIcon type="warning-triangled" />,
      error: <SvgIcon type="cross-circled" />,
      info: <SvgIcon type="info-circled" />,
    },
  },
  styleOverrides: {
    root: ({theme}) => ({
      padding: theme.spacing(2, 3),
      display: 'flex',
    }),
    outlined: ({theme, ownerState: {severity = DEFAULT_SEVERITY}}) => ({
      borderColor: theme.palette.semanticPalette.stroke[severity],
      backgroundColor: theme.palette.semanticPalette.surface[severity],
    }),
    icon: () => ({
      padding: 0,
    }),
    message: ({theme, ownerState: {severity = DEFAULT_SEVERITY}}) => ({
      color: theme.palette.semanticPalette.text[severity],
      fontSize: theme.typography.body1.fontSize,
      lineHeight: theme.typography.body1.lineHeight,
      padding: `${theme.spacing(0.5)} 0`,
      overflow: 'unset',
    }),
    action: ({theme}) => ({
      paddingTop: 0,
      paddingRight: theme.spacing(1),
    }),
  },
};

export const AlertTitleOverrides: Components<Theme>['MuiAlertTitle'] = {
  styleOverrides: {
    root: ({theme}) => ({
      margin: 0,
      paddingRight: theme.spacing(1),
      display: 'inline',
    }),
  },
};

type AlertProps = Omit<MuiAlertProps, 'iconMapping' | 'variant' | 'elevation' | 'square'>;
const Alert = (props: AlertProps) => <MuiAlert {...props} />;

Alert.displayName = 'Alert';
// @ts-expect-error storybook only name
AlertTitle.displayName = 'AlertTitle';

export {Alert, AlertTitle, type AlertProps, type AlertTitleProps};
