import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

// Define component specific argtypes / argtype overrides
const ALERT_ARGTYPES = {
  action: {
    description: 'The action to display. It renders after the message, at the end of the alert.',
    control: {
      type: 'node',
    },
  },
  icon: {
    description:
      'Override the icon displayed before the children. Unless provided, the icon is mapped to the value of the `severity` prop. Set to `false` to remove the `icon`.',
    control: {
      type: 'node',
    },
  },
  closeText: {
    description: 'Override the default label for the close popup icon button.',
    table: {
      defaultValue: {
        summary: 'Close',
      },
    },
    control: {
      type: 'text',
    },
  },
  color: {
    description:
      'The color of the component. Unless provided, the value is taken from the severity prop.',
    control: {
      type: 'select',
    },
    options: ['success', 'warning', 'error', 'info'],
  },
  role: {
    description: 'The ARIA role of the alert.',
    table: {
      defaultValue: {
        summary: 'alert',
      },
    },
    control: {
      type: 'text',
    },
  },
  severity: {
    description: 'The severity of the alert. This defines the color of the alert.',
    control: {
      type: 'select',
    },
    table: {
      defaultValue: {
        summary: 'success',
      },
    },
    options: ['success', 'warning', 'error', 'info'],
  },
};

// Define component argtype keys (these are keys that are defined via shared argtypes and component specific argtypes)
const componentNameArgTypes = [
  ...Object.keys(ALERT_ARGTYPES),
  'classes',
  'children',
  'onClose',
  'components',
  'componentsProps',
  'slotProps',
  'slots',
  'sx',
];

const argTypes = getArgTypes(componentNameArgTypes, {
  ...FORM_COMPONENT_ARGTYPES,
  ...SYSTEM_ARGTYPES,
  ...ALERT_ARGTYPES,
});

export default argTypes;
