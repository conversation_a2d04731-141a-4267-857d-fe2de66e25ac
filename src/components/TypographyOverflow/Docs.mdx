import { Meta, <PERSON>vas, Story, Controls, Description } from "@storybook/blocks";
import * as TypographyOverflowStories from './index.stories.tsx';

<Meta of={TypographyOverflowStories}/>

## Overview

TypographyOverflow is an extension of the Typography component to add a `clampLines` property. `clampLines` defaults to 1. If the content overflows that number of lines, it will cut off with an ellipsis, and show a tooltip with the full content.
*Note: to behave correctly, the parent must provide a width.*

<Canvas of={TypographyOverflowStories.Basic} />
<Controls of={TypographyOverflowStories.Basic}/>

## LineClamp

TypographyOverflow can be used to clamp to a single line, or a specific number of lines.

<Canvas of={TypographyOverflowStories.LineClamp} />

## Tooltip

The TypographyOverflow tooltip will only show if the text overflows its container. The tooltip can be customized with special tooltip content, by passing TooltipProps, or using the default Mui Tooltip component. 

<Canvas of={TypographyOverflowStories.Tooltip} />