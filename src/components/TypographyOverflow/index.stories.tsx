import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {Box, Button, Stack, Typography} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';
import {Tooltip as MUITooltip} from '@mui/material';

import {TypographyOverflow} from '../TypographyOverflow';

const typographyArgTypeCategories = {
  ...categorizeArgTypes(['children', 'component', 'ref'], 'MUI System Props'),
  ...categorizeArgTypes(
    [
      'color',
      'variant',
      'fontWeight',
      'fontFamily',
      'fontStyle',
      'fontSize',
      'typography',
      'letterSpacing',
      'noWrap',
      'textTransform',
      'variantMapping',
      'lineHeight',
    ],
    'Typography Props'
  ),
};

const meta: Meta<typeof TypographyOverflow> = {
  argTypes: typographyArgTypeCategories,
  component: TypographyOverflow,
  title: 'components/Data Display/TypographyOverflow',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=1778-16706',
    },
  },
};

export default meta;
type Story = StoryObj<typeof TypographyOverflow>;

export const Basic: Story = {
  args: {
    clampLines: 1,
    variant: 'h5',
    children: 'Scale your impact with Resilient Agriculture',
  },
  render: args => (
    <Box display={'flex'} border={'1px solid'} width={100}>
      <TypographyOverflow {...args} />
    </Box>
  ),
};

export const LineClamp: Story = {
  args: {
    clampLines: 2,
    children: 'Scale your impact with Resilient Agriculture',
  },
  render: args => (
    <Stack gap={2}>
      <Box display={'flex'} border={'1px solid'} width={100}>
        <TypographyOverflow {...args} />
      </Box>
      <Box display={'flex'} border={'1px solid'} width={100}>
        <TypographyOverflow clampLines={6}>
          This is an input with some supercalifragilisticespialidociously long words in it.{' '}
        </TypographyOverflow>
      </Box>
      <Box display={'flex'} border={'1px solid'} width={150}>
        <TypographyOverflow
          {...args}
          children={
            <>
              <Typography component={'span'}>I even work on </Typography>
              <Typography fontStyle="italic" component={'span'}>
                ReactNode{' '}
              </Typography>
              <Typography component={'span'}>children that overflow</Typography>
            </>
          }
        />
      </Box>
    </Stack>
  ),
};

export const Tooltip: Story = {
  render: () => {
    return (
      <Stack gap={2}>
        <Stack gap={1}>
          <Typography variant="h4">Custom Tooltip Content</Typography>
          <Box display={'flex'} border={'1px solid'} width={100}>
            <TypographyOverflow
              clampLines={1}
              tooltipContent={
                <div>
                  <Button>Click me!</Button> I'm actually special content!
                </div>
              }
              children={'Scale your impact with Resilient Agriculture'}
            />
          </Box>
        </Stack>
        <Stack gap={1}>
          <Typography variant="h4">Tooltip does not show when no overflow</Typography>
          <Box display={'flex'} border={'1px solid'} width={500}>
            <TypographyOverflow
              clampLines={1}
              children={'Scale your impact with Resilient Agriculture'}
            />
          </Box>
        </Stack>
        <Stack gap={1}>
          <Typography variant="h4">MuiTooltip Component</Typography>
          <Box display={'flex'} border={'1px solid'} width={100}>
            <TypographyOverflow
              clampLines={1}
              TooltipComponent={MUITooltip}
              children={'Scale your impact with Resilient Agriculture'}
            />
          </Box>
        </Stack>
      </Stack>
    );
  },
};
