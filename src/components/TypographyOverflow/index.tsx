import type {ElementType, ReactNode} from 'react';
import {useLayoutEffect, useMemo, useRef, useState} from 'react';
import {useDimensions} from 'src/hooks/useDimensions';
// Allowing Mui Tooltips until we replace our styled Tooltip with a themed one
import type {Tooltip as MUITooltip, SxProps, Theme} from '@mui/material';

import {Tooltip, type TooltipProps} from '../Tooltip';
import type {TypographyProps} from '../Typography';
import {Typography} from '../Typography';

export type TypographyOverflowProps<C extends ElementType = 'span'> = TypographyProps<C> & {
  /** Number of lines of text before an ellipsis on overflow */
  clampLines?: number;
  tooltipContent?: ReactNode;
  TooltipProps?: Partial<TooltipProps>;
  TooltipComponent?: typeof MUITooltip | typeof Tooltip;
};

/**
 * Extends the Typography component to allow a `clampLines` property. `clampLines` defaults to 1.
 * If the content overflows that number of lines, it will cut off with an ellipsis,
 * and show a tooltip with the full content.
 *
 * **Note: to behave correctly, the parent must provide a width.
 */
export const TypographyOverflow = <C extends ElementType = 'span'>({
  clampLines = 1,
  tooltipContent,
  TooltipComponent = Tooltip,
  TooltipProps = {},
  ...typographyProps
}: TypographyOverflowProps<C>) => {
  const typographyRef = useRef<HTMLSpanElement>(null);

  const [showDefaultTitle, setShowDefaultTitle] = useState(false);

  // Monitor element resize
  const {height, width} = useDimensions(typographyRef.current);

  useLayoutEffect(() => {
    /*
     * If lines are clamped to 1, manage overflow tooltip based on width.
     * Otherwise, manage based on height
     */
    const current = typographyRef.current;
    if (current) {
      if (clampLines === 1) {
        setShowDefaultTitle(current.offsetWidth < current.scrollWidth);
      } else {
        setShowDefaultTitle(current.clientHeight < current.scrollHeight);
      }
    }
  }, [clampLines, width, height, typographyRef]);

  const displayTitle = useMemo(() => {
    if (!showDefaultTitle) {
      return ''; // empty strings do not render MuiTooltips
    }
    return tooltipContent ?? typographyProps.children ?? '';
  }, [showDefaultTitle, typographyProps.children, tooltipContent]);

  const styles = useMemo(() => (clampLines === 1 ? ellipsis : lineClamp(clampLines)), [clampLines]);

  return (
    <TooltipComponent title={displayTitle} id={String(typographyProps.children)} {...TooltipProps}>
      <Typography
        component={'span'}
        variant="body1"
        sx={styles}
        {...typographyProps}
        ref={typographyRef}
      />
    </TooltipComponent>
  );
};

const ellipsis: SxProps<Theme> = {
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  maxWidth: '100%',
};

// As of 2023, this is still the recommended way in the w3c spec until the line-clamp is supported
const lineClamp = (lines: number | 'none'): SxProps<Theme> => ({
  display: '-webkit-box',
  '-webkit-line-clamp': `${lines}`,
  '-webkit-box-orient': 'vertical',
  overflow: 'hidden',
  height: 'fit-content',
  whiteSpace: 'normal',
  wordBreak: 'break-word',
});
