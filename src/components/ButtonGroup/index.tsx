import {forwardRef, type ForwardedRef} from 'react';
import {ButtonGroup as MUIButtonGroup, type ButtonGroupProps} from '@mui/material';
import type {Components, Theme} from '@mui/material/styles';

// Note: This component has augmented props. See src/muiTheme.d.ts for prop overrides

export const ButtonGroupOverrides: Components<Theme>['MuiButtonGroup'] = {
  defaultProps: {
    disableRipple: true,
    disableFocusRipple: true,
    disableElevation: true,
    color: 'secondary',
  },
};

// Todo: remove me when we replace with ToggleButtonGroup
const ButtonGroupComponent = (props: ButtonGroupProps, ref: ForwardedRef<HTMLDivElement>) => (
  <MUIButtonGroup ref={ref} {...props} />
);

export const ButtonGroup = forwardRef(ButtonGroupComponent);

ButtonGroup.displayName = 'ButtonGroup';

export type {ButtonGroupProps};
