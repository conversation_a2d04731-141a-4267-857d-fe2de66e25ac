import type {<PERSON><PERSON>, <PERSON>Obj} from '@storybook/react';
import {Box, Button, ButtonGroup, Icon} from 'src/index';

const BUTTON_GROUP_VARIANTS = ['outlined'] as const;
const BUTTON_GROUP_SIZES = ['medium', 'small'] as const;

const meta: Meta<typeof ButtonGroup> = {
  component: ButtonGroup,
  title: 'components/Inputs/ButtonGroup',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?type=design&node-id=2915-29876',
    },
  },
};

export default meta;
type Story = StoryObj<typeof ButtonGroup>;

export const Basic: Story = {
  args: {
    children: [<Button>Button</Button>, <Button>Button</Button>, <Button>Button</Button>],
  },
  render: args => {
    return <ButtonGroup {...args}>{args.children}</ButtonGroup>;
  },
};

export const Variants: Story = {
  render: args => (
    <>
      {BUTTON_GROUP_VARIANTS.map((variant, i) => (
        <Box display="block" mb={4}>
          <ButtonGroup key={i} {...args} variant={variant}>
            <Button>{variant}</Button>
            <Button>{variant}</Button>
            <Button>{variant}</Button>
          </ButtonGroup>
        </Box>
      ))}
    </>
  ),
};

export const Sizes: Story = {
  render: args => (
    <>
      {BUTTON_GROUP_SIZES.map((size, i) => (
        <Box display="block" mb={4} key={i}>
          <ButtonGroup {...args} size={size}>
            <Button>{size}</Button>
            <Button>{size}</Button>
            <Button>{size}</Button>
          </ButtonGroup>
        </Box>
      ))}
    </>
  ),
};

export const Disabled: Story = {
  render: args => (
    <ButtonGroup {...args} disabled>
      <Button>disabled</Button>
      <Button>disabled</Button>
      <Button>disabled</Button>
    </ButtonGroup>
  ),
};

export const Icons: Story = {
  render: args => (
    <>
      <Box display="block" mb={4}>
        <ButtonGroup {...args}>
          <Button>
            <Icon type="plus" />
          </Button>
          <Button>
            <Icon type="plus" />
          </Button>
          <Button>
            <Icon type="plus" />
          </Button>
        </ButtonGroup>
      </Box>
      <Box display="block" mb={4}>
        <ButtonGroup {...args} size="small">
          <Button>
            <Icon type="plus" />
          </Button>
          <Button>
            <Icon type="plus" />
          </Button>
          <Button>
            <Icon type="plus" />
          </Button>
        </ButtonGroup>
      </Box>
    </>
  ),
};
