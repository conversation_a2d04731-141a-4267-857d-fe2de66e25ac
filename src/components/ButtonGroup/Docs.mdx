import { Meta, Canvas, Controls } from "@storybook/blocks";
import * as ButtonGroupStories from './index.stories.tsx'


<Meta of={ButtonGroupStories} />

 ## Overview

Buttons allow users to trigger an action or event with a single click.
For example, you can use a button for allowing the functionality of submitting a form, opening a dialog, canceling an action,
or performing a delete operation.

[React Button Group](https://mui.com/material-ui/react-button-group/)

<Canvas of={ButtonGroupStories.Basic} />
<Controls of={ButtonGroupStories.Basic}/>

## Variants
<Canvas of={ButtonGroupStories.Variants} />

### Sizes
<Canvas of={ButtonGroupStories.Sizes} />

### Buttons Icons
<Canvas of={ButtonGroupStories.Icons} />