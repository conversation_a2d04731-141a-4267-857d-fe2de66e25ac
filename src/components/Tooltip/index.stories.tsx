import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {Box, Button, IconButton, Stack, SvgIcon, Tooltip, Typography} from 'src/index';

type Story = StoryObj<typeof Tooltip>;

const meta: Meta<typeof Tooltip> = {
  component: Tooltip,
  title: 'components/Feedback/Tooltip',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=1808%3A17859',
    },
    layout: 'centered',
  },
};

export default meta;

export const Basic: Story = {
  args: {
    title: "I'm a tooltip!",
    placement: 'top',
  },
  argTypes: {
    title: {
      control: {
        type: 'text',
      },
    },
    placement: {
      control: {
        type: 'radio',
      },
      options: ['top', 'left', 'right', 'bottom'],
    },
  },
  render: args => (
    <Box>
      <Typography>
        What did the tooltip say to the confused user
        <Tooltip {...args}>
          <IconButton>
            <SvgIcon type="question" />
          </IconButton>
        </Tooltip>
      </Typography>
    </Box>
  ),
};

export const Placement: StoryObj<typeof Tooltip> = {
  render: () => {
    return (
      <>
        <Box>
          <Tooltip open title="I'm on the top!" placement="top">
            <IconButton>
              <SvgIcon type="arrow-up" />
            </IconButton>
          </Tooltip>
        </Box>
        <Box>
          <Tooltip open title="I'm on the left!" placement="left">
            <IconButton>
              <SvgIcon type="arrow-left" />
            </IconButton>
          </Tooltip>
        </Box>
        <Box>
          <Tooltip open title="I'm on the right!" placement="right">
            <IconButton>
              <SvgIcon type="arrow-right" />
            </IconButton>
          </Tooltip>
        </Box>
        <Box>
          <Tooltip open title="I'm on the bottom!" placement="bottom">
            <IconButton>
              <SvgIcon type="arrow-down" />
            </IconButton>
          </Tooltip>
        </Box>
      </>
    );
  },
};

export const Disabled: StoryObj<typeof Tooltip> = {
  render: () => (
    <Stack gap={2}>
      <Box>
        <Tooltip title="tooltip on disabled button with span" placement="top">
          <span>
            <Button disabled>Disabled w/ span</Button>
          </span>
        </Tooltip>
      </Box>
      <Box>
        <Tooltip title="tooltip on disabled button without span" placement="top">
          <Button disabled>Disabled w/o span</Button>
        </Tooltip>
      </Box>
    </Stack>
  ),
};
