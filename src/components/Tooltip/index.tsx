import {Fade, Tooltip, type Components, type Theme, type TooltipProps} from '@mui/material';

export const TooltipOverrides: Components<Theme>['MuiTooltip'] = {
  defaultProps: {
    placement: 'top',
    arrow: true,
    TransitionComponent: Fade,
    enterDelay: 0,
    enterTouchDelay: 0,
  },
  styleOverrides: {
    tooltip: ({theme}) => ({
      backgroundColor: theme.palette.semanticPalette.surfaceInverted.main,
      fontSize: theme.typography.body2.fontSize,
      fontWeight: theme.typography.body2.fontWeight,
      padding: theme.spacing(2),
    }),
    arrow: ({theme}) => ({
      color: theme.palette.semanticPalette.surfaceInverted.main,
    }),
  },
};

export {Tooltip, type TooltipProps};
