import { <PERSON>a, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import { Tooltip } from "./index";
import * as TooltipStories from './index.stories'

<Meta of={TooltipStories}/>

## Overview
Tooltips display informative text when users hover over, focus on, or tap an element.

Note, the child of a Material-UI Tooltip must be able to hold a ref. React `functional components` do *NOT* accept refs.<br />

The following can hold a ref:
- DOM (or host) components e.g. `span` or `div`, etc
- React.forwardRef components
- React.memo components
- React.lazy components
- class components i.e. React.Component or React.PureComponent
- Any Material-UI component (known issues / mixed results with MUI components)

If you find your tooltip is not showing because of this, the simplest solution may be to wrap your component in a `span` or `div`.

[MUI Tooltip](https://mui.com/material-ui/api/tooltip/)

<Canvas of={TooltipStories.Basic} />
<Controls of={TooltipStories.Basic}/>

## Placement
<Canvas of={TooltipStories.Placement} />

## Disabled
By default disabled elements like `button` do not trigger user interactions so a Tooltip will not activate on normal events like hover. To accommodate disabled elements, add a simple wrapper element, such as a `span`.
<Canvas of={TooltipStories.Disabled} />


