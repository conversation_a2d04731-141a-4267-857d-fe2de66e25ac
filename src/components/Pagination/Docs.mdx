import { <PERSON>a, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as PaginationStories from './index.stories.tsx'

<Meta of={PaginationStories} />

## Overview
The `Pagination` component enables the user to select a specific page from a range of pages.
`Pagination` is built on top of the `usePagination()` hook from MUI

This component takes the primary props:
* `page`__*__ -   The zero-based index of the current page.
* `count`__*__- The total number of rows.
* `onSetPage`__*__ - Callback fired when the page is changed.
    * `page: number` - The page selected.
* `rowsPerPageOptions` - The list of options to for the `Rows per page SelectField`. Together with `onSetRowsPerPage` is required to display the `Rows per page SelectField`.
* `onSetRowsPerPage` - Callback fired when the page is changed. Together with `rowsPerPageOptions` is required to display the `Rows per page SelectField`.
    * To access the value use `event.target.value`.
* `showFirstButton`- If `true`, show the first-page button.
* `showLastButton`- If `true`, show the last-page button.
* `showPages`- If `true`, show pages buttons.
* `showDisplayedRowsLabel`- If `true`, the `Showing ${from} - ${to} out of ${totalRows}` text is displayed.
* `disabled`- If `true`, the component is disabled.
* `size`- `small` | `medium`.  The size of the component. The default value is `small`.
* `labelRowsPerPage`- A prop for localizing the Rows per page SelectField's label.
* `labelDisplayedRows`- Customize/localize the displayed rows label. Invoked with a `{ from, to, count }` object. The default is `({from, to, count}) => Showing ${from} - ${to} out of ${count}`;





[MUI Pagination](https://mui.com/material-ui/react-pagination/)

<Canvas of={PaginationStories.Basic} />
<Controls of={PaginationStories.Basic}/>

### Variants
<Canvas of={PaginationStories.Variants} />

### Sizes 
<Canvas of={PaginationStories.Sizes} />


### Localization and custom labels 
<Canvas of={PaginationStories.LocalizationAndCustomLabels} />
