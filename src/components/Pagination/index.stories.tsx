import type {Meta, StoryObj} from '@storybook/react';
import {useState} from 'react';
import {Box, Pagination as PaginationComponent, Typography, type PaginationProps} from 'src/index';

const MockOptions = [
  {label: 10, value: 10},
  {label: 25, value: 25},
  {label: 50, value: 50},
];

export default {
  component: PaginationComponent,
  title: 'components/Navigation/Pagination',
  parameters: {},
} as Meta<PaginationProps>;

export const Basic: StoryObj<PaginationProps> = {
  args: {
    page: 0,
    count: 30,
  },
};

const InteractivePagination = (props: Omit<PaginationProps, 'onSetRowsPerPage' | 'onSetPage'>) => {
  const [currentPage, setCurrentPage] = useState(props.page);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  return (
    <PaginationComponent
      {...props}
      page={currentPage}
      onSetPage={setCurrentPage}
      rowsPerPage={rowsPerPage}
      onSetRowsPerPage={setRowsPerPage}
    />
  );
};

export const Sizes: StoryObj<PaginationProps> = {
  render: () => {
    return (
      <Box display="flex" flexDirection={'column'} gap={10}>
        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>Size "small" (default)</Typography>
          <InteractivePagination
            page={0}
            count={200}
            showPages
            showFirstButton
            showLastButton
            showDisplayedRowsLabel
            rowsPerPageOptions={MockOptions}
          />
        </Box>
        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>Size "medium" </Typography>
          <InteractivePagination
            size="medium"
            page={0}
            count={200}
            showPages
            showFirstButton
            showLastButton
            showDisplayedRowsLabel
            rowsPerPageOptions={MockOptions}
          />
        </Box>
      </Box>
    );
  },
};

export const Variants: StoryObj<PaginationProps> = {
  render: () => {
    return (
      <Box display="flex" flexDirection={'column'} gap={10}>
        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>Minimal (default)</Typography>

          <InteractivePagination page={0} count={200} />
        </Box>
        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>With first and last page buttons </Typography>
          <InteractivePagination page={0} count={200} showFirstButton showLastButton />
        </Box>
        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>With pages</Typography>
          <InteractivePagination page={0} count={200} showFirstButton showLastButton showPages />
        </Box>

        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>With displayed rows</Typography>
          <InteractivePagination
            page={0}
            count={200}
            showFirstButton
            showLastButton
            showPages
            showDisplayedRowsLabel
          />
        </Box>

        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>With rows per page selector (full)</Typography>
          <InteractivePagination
            page={0}
            count={200}
            showFirstButton
            showLastButton
            showPages
            showDisplayedRowsLabel
            rowsPerPageOptions={MockOptions}
          />
        </Box>
      </Box>
    );
  },
};

export const LocalizationAndCustomLabels: StoryObj<PaginationProps> = {
  render: () => {
    return (
      <Box display="flex" flexDirection={'column'} gap={10}>
        <Box display="flex" flexDirection={'column'} gap={2}>
          <Typography fontWeight={600}>Localization to Ukrainian</Typography>
          <InteractivePagination
            size="medium"
            page={0}
            count={200}
            showPages
            showFirstButton
            showLastButton
            showDisplayedRowsLabel
            rowsPerPageOptions={MockOptions}
            labelRowsPerPage={'Користувачів на сторінку'}
            labelDisplayedRows={({from, to, count}) => `Показано ${from} - ${to} з ${count}`}
          />
        </Box>
      </Box>
    );
  },
};
