import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {DesignSystemProvider} from 'src/design-system-provider';

import {Pagination} from './index';

const MockOptions = [
  {label: 10, value: 10},
  {label: 25, value: 25},
  {label: 50, value: 50},
];

describe('Pagination', () => {
  it('should render the simple Pagination with only two buttons', () => {
    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination page={0} count={200} rowsPerPage={10} onSetPage={() => {}} />
      </DesignSystemProvider>
    );

    expect(screen.getAllByRole('button')).toHaveLength(2);
  });

  it('should render the Pagination with the first and last page buttons', () => {
    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination
          page={0}
          count={200}
          rowsPerPage={10}
          showFirstButton
          showLastButton
          onSetPage={() => {}}
        />
      </DesignSystemProvider>
    );

    expect(screen.getAllByRole('button')).toHaveLength(4);
  });

  it('should render the simple Pagination with the pages', () => {
    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination page={1} count={200} rowsPerPage={10} showPages onSetPage={() => {}} />
      </DesignSystemProvider>
    );

    expect(screen.getAllByRole('button')).toHaveLength(9);

    // the Page 10 should be in the document
    expect(screen.getByRole('button', {name: '20'})).toBeInTheDocument();
  });

  it('should render the Pagination with the per page label', () => {
    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination
          page={0}
          count={50}
          rowsPerPage={10}
          showDisplayedRowsLabel
          onSetPage={() => {}}
        />
      </DesignSystemProvider>
    );

    expect(screen.getByText('Showing 1 - 10 out of 50')).toBeInTheDocument();
  });

  it('should render the second page Pagination with the per page label', () => {
    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination
          page={1}
          count={50}
          rowsPerPage={10}
          showDisplayedRowsLabel
          onSetPage={() => {}}
        />
      </DesignSystemProvider>
    );

    expect(screen.getByText('Showing 11 - 20 out of 50')).toBeInTheDocument();
  });

  it('should render the Pagination with rows per page selector', async () => {
    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination
          page={0}
          count={50}
          rowsPerPage={10}
          onSetRowsPerPage={() => {}}
          rowsPerPageOptions={MockOptions}
          onSetPage={() => {}}
        />
      </DesignSystemProvider>
    );

    await userEvent.click(await screen.findByRole('button', {name: '10'}));

    expect(await screen.findByRole('option', {name: '25'})).toBeInTheDocument();
    expect(await screen.findByRole('option', {name: '50'})).toBeInTheDocument();
  });

  it('should render the localized Pagination', async () => {
    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination
          page={0}
          count={50}
          rowsPerPage={10}
          onSetRowsPerPage={() => {}}
          rowsPerPageOptions={MockOptions}
          onSetPage={() => {}}
          showDisplayedRowsLabel
          labelRowsPerPage={'Користувачів на сторінку'}
          labelDisplayedRows={({from, to, count}) => `Показано ${from} - ${to} з ${count}`}
        />
      </DesignSystemProvider>
    );

    expect(screen.getByText('Користувачів на сторінку')).toBeInTheDocument();
    expect(screen.getByText('Показано 1 - 10 з 50')).toBeInTheDocument();
  });

  it('should reset to the first page, when rows per page changes', async () => {
    const setPage = jest.fn();

    render(
      <DesignSystemProvider muiThemeKey={0}>
        <Pagination
          page={1}
          count={50}
          rowsPerPage={10}
          onSetRowsPerPage={() => {}}
          rowsPerPageOptions={MockOptions}
          onSetPage={setPage}
        />
      </DesignSystemProvider>
    );

    await userEvent.click(await screen.findByRole('button', {name: '10'}));
    await userEvent.click(await screen.findByRole('option', {name: '50'}));

    expect(setPage).toHaveBeenCalledWith(0);
  });
});
