import React, {useMemo} from 'react';
import {
  buttonBaseClasses,
  inputAdornmentClasses,
  inputBaseClasses,
  outlinedInputClasses,
  selectClasses,
  svgIconClasses,
  useTheme,
} from '@mui/material';
import usePagination, {type UsePaginationProps} from '@mui/material/usePagination';
import {styled, type SxProps} from '@mui/system';

import {Box} from 'src/components/Box';
import {Button} from 'src/components/Button';
import {ButtonGroup} from 'src/components/ButtonGroup';
import {SvgIcon, type IconType} from 'src/components/Icon';
import {SelectField} from 'src/components/Inputs/SelectField';
import {Typography} from 'src/components/Typography';

const iconTypes: Record<string, IconType> = {
  previous: 'chevron-left',
  next: 'chevron-right',
  first: 'first-page',
  last: 'last-page',
};

const regularButtonsTypes = ['previous', 'next', 'first', 'last'];

export type PaginationProps = UsePaginationProps & {
  size?: 'small' | 'medium';
  /**
   * Number of elements to show on one page.
   * */
  rowsPerPage?: number;
  /**
   * Total number of elements/rows.
   * */
  count: number;
  /**
   * Zero-based index of the current page.
   * */
  page: number;
  /**
   * If `true`, show pages buttons.
   * */
  showPages?: boolean;
  /**
   * If `true`, the `Showing ${from} - ${to} out of ${totalRows}` text is displayed.
   * */
  showDisplayedRowsLabel?: boolean;
  /**
   * The list of options to for the `Rows per page SelectField`. Together with `onSetRowsPerPage` is required to display the `Rows per page SelectField`
   * */
  rowsPerPageOptions?: Array<{
    label: string | number | JSX.Element;
    value: number;
  }>;
  /**
   * Callback fired when the page is changed. Together with `rowsPerPageOptions` is required to display the `Rows per page SelectField`
   * */
  onSetRowsPerPage?: (rowsPerPage: number) => void;
  /**
   * A prop for localizing the Rows per page SelectField's label
   * */
  labelRowsPerPage?: string | JSX.Element | React.ReactNode;
  /**
   * Customize/localize the displayed rows label. Invoked with a `{ from, to, count }` object. The default is `({from, to, count}) => Showing ${from} - ${to} out of ${count}`
   * */
  labelDisplayedRows?: ({
    from,
    to,
    count,
  }: {
    from: number;
    to: number;
    count: number;
  }) => string | JSX.Element;
  onSetPage: (page: number) => void;
};

const defaultLabelDisplayedRows = ({from, to, count}: {from: number; to: number; count: number}) =>
  `Showing ${from} - ${to} out of ${count}`;

export const Pagination = ({
  size = 'small',
  rowsPerPage = 10,
  count,
  page,
  showPages,
  showDisplayedRowsLabel,
  rowsPerPageOptions,
  onSetRowsPerPage,
  labelRowsPerPage = 'Rows per page',
  labelDisplayedRows = defaultLabelDisplayedRows,
  onSetPage,
  ...props
}: PaginationProps) => {
  const theme = useTheme();
  const totalPagesCount = rowsPerPage ? Math.ceil(count / rowsPerPage) : count; // use Math.ceil to always round to bigger number
  const normalizedPage = page + 1; // Normalized page to zero-based

  const {items} = usePagination({
    ...props,
    onChange: (_, newPage) => onSetPage(newPage - 1), // Normalized onChange to zero-based
    page: normalizedPage,
    count: totalPagesCount,
  });

  const showingResultsMax =
    normalizedPage * rowsPerPage > count ? count : normalizedPage * rowsPerPage;
  const showingResultsMin = normalizedPage * rowsPerPage - rowsPerPage + 1;

  const displayedRowsLabel = labelDisplayedRows({
    from: showingResultsMin,
    to: showingResultsMax,
    count,
  });
  const displayRowsPerPage = rowsPerPageOptions?.length && onSetRowsPerPage;
  const supportTextSize = size === 'small' ? 'body2' : 'body1';

  const SelectStyles: SxProps = useMemo(() => {
    return {
      [`&.${inputBaseClasses.root}`]: {
        padding: size === 'small' ? theme.spacing(1, 2) : theme.spacing(2, 3),
        gap: 2,
        [`.${selectClasses.icon}`]: {
          display: 'none',
        },
        [`.${svgIconClasses.root}`]: {
          fontSize: theme.typography[size === 'small' ? 'h5' : 'h6'].lineHeight,
        },
        [`.${buttonBaseClasses.root}`]: {
          padding: 0,
        },
        [`&> .${inputAdornmentClasses.root}.${inputAdornmentClasses.positionEnd}`]: {
          paddingRight: 0,
          margin: 0,
        },
        [`.${selectClasses.select}`]: {
          minHeight: 'auto',
          height: 'auto',
        },
        [`.${inputBaseClasses.input}.${outlinedInputClasses.input}`]: {
          padding: 0,
          color: theme.palette.semanticPalette.text.secondary,
          fontSize: theme.typography[supportTextSize],
        },
      },
    };
  }, [size, theme, supportTextSize]);

  return (
    <Box display="flex" alignItems="center" gap={2}>
      {displayRowsPerPage && (
        <Box display="flex" gap={3} alignItems="center" marginRight={3}>
          <Typography color="secondary" variant={supportTextSize}>
            {labelRowsPerPage}
          </Typography>

          <SelectField
            required
            onChange={e => {
              onSetPage(0);
              onSetRowsPerPage(e.target.value);
            }}
            value={rowsPerPage}
            options={rowsPerPageOptions}
            sx={SelectStyles}
          />
        </Box>
      )}

      {showDisplayedRowsLabel ? (
        <Typography color="secondary" variant={supportTextSize}>
          {displayedRowsLabel}
        </Typography>
      ) : null}

      <ButtonGroup size={size}>
        {items.map(({page: buttonPage, type, selected, disabled, onClick}, index) => {
          if (regularButtonsTypes.includes(type)) {
            return (
              <StyledPaginationButton
                key={index}
                area-label={`pagination-button-${type}`}
                onClick={onClick}
                disabled={disabled}
                color="secondary"
                selected={selected}
              >
                <SvgIcon fontSize="h5" type={iconTypes[type]}></SvgIcon>
              </StyledPaginationButton>
            );
          }
          if (showPages) {
            const pageButtonText = buttonPage ?? '…';

            return (
              <StyledPaginationButton
                key={index}
                area-label={`pagination-button-${type}`}
                onClick={buttonPage ? onClick : undefined}
                disabled={disabled}
                color="secondary"
                selected={selected}
              >
                <Typography variant="h6" color={selected ? 'main' : 'secondary'}>
                  {pageButtonText}
                </Typography>
              </StyledPaginationButton>
            );
          }

          return null;
        })}
      </ButtonGroup>
    </Box>
  );
};

const StyledPaginationButton = styled(Button)<{selected: boolean}>`
  background-color: ${({theme, selected}) =>
    theme.palette.semanticPalette.surface[selected ? 'secondary' : 'main']};
  min-width: 36px;
`;
