import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {MemoryRouter, Link as RouterLink} from 'react-router-dom';
import {Box, Link, Typography} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';
import {type TYPOGRAPHY_VARIANTS} from 'src/tokens/constants';
import {Stack} from '@mui/system';

const preventDefault = (event: React.SyntheticEvent) => event.preventDefault();

const linkArgTypes = categorizeArgTypes(
  ['children', 'component', 'ref', 'sx', 'color'],
  'MUI System Props'
);

const meta: Meta<typeof Link> = {
  component: Link,
  title: 'components/Data Display/Link',
  argTypes: {
    ...linkArgTypes,
    underline: {
      description: 'Controls when the link should have an underline.',
      control: {
        type: 'select',
        options: ['none', 'hover', 'always'],
      },
    },
  },
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=5811-93483&m=dev',
    },
  },
} as Meta<typeof Link>;

export default meta;
type Story = StoryObj<typeof Link>;

export const Basic: Story = {
  render: props => (
    <Link target="_blank" rel="noopener" href={'https://app.dev.regrow.ag/'} {...props}>
      Regrow dev
    </Link>
  ),
};

export const Underline: Story = {
  render: props => {
    return (
      <Box bgcolor="semanticPalette.surface.main" display="flex" flexDirection="column" gap={2}>
        <Link onClick={preventDefault} href="#" underline="none" {...props}>
          underline="none"
        </Link>
        <Link onClick={preventDefault} href="#" underline="hover" {...props}>
          underline="hover"
        </Link>
        <Link onClick={preventDefault} href="#" underline="always" {...props}>
          underline="always"
        </Link>
      </Box>
    );
  },
};

const VARIANTS: Array<{
  variant: (typeof TYPOGRAPHY_VARIANTS)[number];
  text: string;
}> = [
  {
    variant: 'h1',
    text: 'Header 1',
  },
  {
    variant: 'h2',
    text: 'Header 2',
  },
  {
    variant: 'h3',
    text: 'Header 3',
  },
  {
    variant: 'h4',
    text: 'Header 4',
  },
  {
    variant: 'h5',
    text: 'Header 5',
  },
  {
    variant: 'h6',
    text: 'Header 6',
  },
  {
    variant: 'body1',
    text: 'Body 1',
  },
  {
    variant: 'body2',
    text: 'Body 2',
  },
];

export const Variants: Story = {
  render: props => {
    return (
      <Stack bgcolor="semanticPalette.surface.main" gap={2}>
        <Typography variant="body2" color="secondary">
          All Typopgraphy props are available to use within the Link component, including colors and
          variants for text styles
        </Typography>
        {VARIANTS.map(({variant, text}) => (
          <Link onClick={preventDefault} href="#" variant={variant} {...props}>
            {text}
          </Link>
        ))}
      </Stack>
    );
  },
};

const COLORS = [
  'primary',
  'secondary',
  'semanticPalette.text.main',
  'semanticPalette.text.secondary',
  'semanticPalette.text.brand',
  'semanticPalette.text.success',
  'semanticPalette.text.info',
  'semanticPalette.text.warning',
  'semanticPalette.text.error',
  'categoryPalette.0.text',
  'categoryPalette.1.text',
  'categoryPalette.2.text',
  'categoryPalette.3.text',
  'categoryPalette.4.text',
  'categoryPalette.5.text',
  'categoryPalette.6.text',
  'categoryPalette.7.text',
  'categoryPalette.8.text',
  'inherit',
];
export const Colors: Story = {
  render: props => {
    return (
      <Stack bgcolor="semanticPalette.surface.main" gap={2}>
        <Typography variant="body2" color="secondary">
          All Typopgraphy props are available to use within the Link component, including colors and
          variants for text styles
        </Typography>
        {COLORS.map(color => (
          <Link onClick={preventDefault} href="#" color={color} variant="body2" {...props}>
            {color}
          </Link>
        ))}
      </Stack>
    );
  },
};

export const UsingButtonComponent = {
  render: () => (
    <Stack bgcolor="semanticPalette.surface.main" gap={2} alignItems="flex-start">
      <Link component="button">Link using component="button"</Link>
      <Typography color={'secondary'} variant="body2">
        Because links are intended for navigable elements/routing, the component prop can be used to
        set the native html to button when a use case arises for this styling but no typical routing
        behavior. This allows for consistent semantics.
      </Typography>
    </Stack>
  ),
};

export const UsingRouterLink: Story = {
  decorators: [
    Story => (
      <MemoryRouter>
        <Story />
      </MemoryRouter>
    ),
  ],
  render: props => {
    return (
      <Box bgcolor="semanticPalette.surface.main" display="flex" flexDirection="column" gap={2}>
        <Link component={RouterLink} to={'/'} {...props}>
          Using router link
        </Link>
        <Typography color={'secondary'} variant="body2">
          *the link doesn't work in storybook, but that's how it should be used
        </Typography>
      </Box>
    );
  },
};
