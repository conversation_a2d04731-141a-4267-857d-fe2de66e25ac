import type {Components, Theme} from '@mui/material';
import {
  Link,
  linkClasses,
  type LinkBaseProps,
  type LinkClasses,
  type LinkClassKey,
  type LinkProps,
  type LinkTypeMap,
} from '@mui/material';

export const LinkOverrides: Components<Theme>['MuiLink'] = {
  defaultProps: {},
  styleOverrides: {
    root: ({theme}) => ({
      fontFamily: theme.typography.fontFamily,
    }),
  },
};

export {Link};

export type {LinkProps, LinkBaseProps, LinkClassKey, LinkTypeMap, LinkClasses};
export {linkClasses};

export function StoryComponent<C extends React.ElementType>(props: LinkProps<C>) {
  return <Link {...props} />;
}
StoryComponent.displayName = 'Link';
