import { <PERSON>a, <PERSON><PERSON>, <PERSON><PERSON>, Story } from "@storybook/blocks";
import * as LinkStories from './index.stories.tsx'

<Meta of={LinkStories}/>

## Overview
The Link component allows you to easily customize anchor elements with your theme colors and typography styles.

The Link component is built on top of the Typography component, meaning that you can use its props.
You can learn more here: [https://mui.com/material-ui/react-link/](https://mui.com/material-ui/react-link/)

However, the Link component has some different default props than the Typography component:

- `color="primary"`: Sets the default color to ensure the link stands out.
- `variant="inherit"`: Default variant, as links are typically used within a Typography component.

## Basic
<Canvas of={LinkStories.Basic} />

## Controls
<Controls of={LinkStories.Basic}/>

## Underline
<Canvas of={LinkStories.Underline} />

## Variants
<Canvas of={LinkStories.Variants} />

## Colors
<Canvas of={LinkStories.Colors} />

## Using Button Component
<Canvas of={LinkStories.UsingButtonComponent} />

## Using Router Link
<Canvas of={LinkStories.UsingRouterLink} />



