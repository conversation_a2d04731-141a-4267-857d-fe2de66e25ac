import {getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const CHIP_ARGTYPES = {
  avatar: {
    description: 'The Avatar element to display.',
    table: {
      type: {summary: 'element'},
    },
  },
  clickable: {
    description:
      'If true, the chip will appear clickable, and will raise when pressed, even if the onClick prop is not defined. If false, the chip will not appear clickable, even if onClick prop is defined. This can be used, for example, along with the component prop to indicate an anchor Chip is clickable. Note: this controls the UI and does not affect the onClick event.',
    control: {type: 'boolean'},
    table: {
      type: {
        summary: 'boolean',
      },
    },
  },
  color: {
    description:
      'The color of the component. It supports those theme colors that make sense for this component.',
    options: [
      'default',
      'success',
      'warning',
      'error',
      'info',
      'primary',
      'secondary',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
    ],
    control: {
      type: 'select',
    },

    table: {defaultValue: {summary: 'default'}},
  },
  deleteIcon: {
    description: 'Override the default delete icon element. Shown only if onDelete is set.',
    control: {type: 'element'},
  },
  disabled: {
    description: 'If true, the chip will be disabled.',
    table: {defaultValue: {summary: false}},
    control: {type: 'boolean'},
  },
  icon: {
    description: 'The icon element to render on the left side of the chip.',
    table: {
      type: {
        summary: 'element',
      },
    },
  },
  label: {
    description: 'The content of the component.',
    table: {
      type: {
        summary: 'node',
      },
    },
  },
  onDelete: {
    description: 'Callback function fired when the delete icon is clicked.',
    table: {
      type: {
        summary: 'function',
      },
    },
  },
  size: {
    description: 'The size of the component.',
    options: ['medium', 'small'],
    table: {defaultValue: {summary: 'medium'}},
    control: {
      type: 'inline-radio',
    },
  },
  skipFocusWhenDisabled: {
    description:
      'If true, allows the disabled chip to escape focus. If false, allows the disabled chip to receive focus.',
    table: {
      type: {
        summary: 'boolean',
      },
      defaultValue: {summary: false},
    },
  },
  variant: {
    description: 'The variant to use.',
    options: ['filled', 'outlined'],
    table: {defaultValue: {summary: 'filled'}},
    control: {
      type: 'inline-radio',
    },
  },
};

const chipArgTypeKeys = [...Object.keys(CHIP_ARGTYPES), 'classes', 'component', 'sx'];

const argTypes = getArgTypes(chipArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...CHIP_ARGTYPES,
});

export default argTypes;
