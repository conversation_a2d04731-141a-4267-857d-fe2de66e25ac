import {Meta, <PERSON><PERSON>, <PERSON>s, Story} from '@storybook/blocks';
import * as ChipStories from './index.stories.tsx';

<Meta of={ChipStories} />

## Overview

Chips are compact elements that represent an input, attribute, or action.

## Material Docs

[React Chip](https://mui.com/material-ui/react-chip/)

## Chip

<Canvas of={ChipStories.Basic} />
<Controls of={ChipStories.Basic} />

## Sizes
<Canvas of={ChipStories.Sizes} />

## States
<Canvas of={ChipStories.Usage} />

## Colors and Variants
<Canvas of={ChipStories.ColorsVariants} />

## Icons
<Canvas of={ChipStories.Icons} />

## Long Text Truncation
<Canvas of={ChipStories.LongTextTruncation} />
