import {forwardRef, type ForwardedRef} from 'react';
import type {CATEGORY_KEYS} from 'src/tokens/constants';
import {isCategoryColorKey, isDefined, isString} from 'src/utils/typeGuards';
import type {Components, ChipProps as MUIChipProps, Theme} from '@mui/material';
import {chipClasses, darken, Chip as MUIChip, useTheme} from '@mui/material';

import {TypographyOverflow} from 'src/components/TypographyOverflow';

// Note: This component has augmented props. See src/muiTheme.d.ts for prop overrides
const SEMANTIC_COLOR_KEYS = [
  'success',
  'warning',
  'error',
  'info',
  'secondary',
  'primary',
] as const;

export const isPaletteColorKey = (x: unknown): x is (typeof SEMANTIC_COLOR_KEYS)[number] =>
  SEMANTIC_COLOR_KEYS.includes(x as (typeof SEMANTIC_COLOR_KEYS)[number]);

export const ChipOverrides: Components<Theme>['MuiChip'] = {
  defaultProps: {
    variant: 'filled',
    color: 'primary',
    size: 'medium',
  },
  styleOverrides: {
    root: ({theme, ownerState: {color}}) => {
      return {
        fontSize: theme.typography.body1.fontSize,
        padding: `${theme.spacing(2)} 0`,
        height: 'auto',
        borderRadius: theme.shape.borderRadius * theme.borderRadii.md,
        lineHeight: theme.typography.body1.lineHeight,
        ...(color &&
          color !== 'default' &&
          color !== 'inherit' &&
          color !== 'primary' && {
            backgroundColor: theme.palette.semanticPalette.surface[color],
            color: theme.palette.semanticPalette.text[color],
            borderColor: theme.palette.semanticPalette.stroke[color],
            '&:hover': {
              backgroundColor: darken(theme.palette.semanticPalette.surface[color], 0.1),
            },
          }),
      };
    },
    colorPrimary: ({theme}) => {
      return {
        backgroundColor: theme.palette.semanticPalette.surface.brand,
        color: theme.palette.semanticPalette.text.brand,
        borderColor: theme.palette.semanticPalette.stroke.brand,
        '&:hover': {backgroundColor: darken(theme.palette.semanticPalette.surface.brand, 0.1)},
      };
    },
    colorSecondary: ({theme}) => {
      return {
        borderColor: theme.palette.semanticPalette.strokeInverted.secondary,
        color: theme.palette.semanticPalette.text.secondary,
        backgroundColor: theme.palette.semanticPalette.surface.secondary,
        '&:hover': {backgroundColor: darken(theme.palette.semanticPalette.surface.secondary, 0.1)},
      };
    },
    deletable: ({theme, ownerState}) => {
      return {
        [`&.${chipClasses.root}`]: {
          '&:has(>svg)': {
            padding: `${theme.spacing(2)} 0`,
            [`&.${chipClasses.sizeSmall}`]: {
              padding: `${theme.spacing(1)} 0`,
            },
            svg: {
              marginRight: theme.spacing(2),
              fontSize: theme.typography.body1.lineHeight,
              ...(isPaletteColorKey(ownerState.color) && {
                color: theme.palette[ownerState.color].main,
                '&:hover': {
                  color: theme.palette[ownerState.color].dark,
                },
              }),
            },
          },
        },
      };
    },
    iconMedium: ({theme}) => {
      return {
        fontSize: theme.typography.h5.lineHeight,
      };
    },
    outlined: ({ownerState: {size}}) => {
      return {
        padding: `7px 0`,
        ...(size === 'small' && {
          [`&.${chipClasses.root}`]: {
            padding: `3px 0`,
          },
        }),
      };
    },
    sizeSmall: ({theme}) => {
      return {
        fontSize: theme.typography.body2.fontSize,
        lineHeight: theme.typography.body2.lineHeight,
        [`.MuiTypography-root`]: {
          fontSize: theme.typography.body2.fontSize,
          lineHeight: theme.typography.body2.lineHeight,
        },
        [`&.${chipClasses.root}`]: {
          padding: `${theme.spacing(1)} 0`,
        },
        [`&.${chipClasses.icon}`]: {
          marginLeft: theme.spacing(0),
        },
      };
    },
  },
};

type ChipProps<C extends React.ElementType> = Omit<MUIChipProps, 'color'> & {
  component?: C;
  color?: (typeof SEMANTIC_COLOR_KEYS)[number] | (typeof CATEGORY_KEYS)[number];
};

const ChipComponent = <C extends React.ElementType>(
  props: ChipProps<C>,
  ref: ForwardedRef<HTMLDivElement>
) => {
  const theme = useTheme();
  const {label, color, variant, sx, ...rest} = props;
  const deletable = Boolean(rest.onDelete);

  const label_ = isString(label) ? (
    <TypographyOverflow component="div">{label}</TypographyOverflow>
  ) : (
    label
  );

  if (isDefined(color) && isCategoryColorKey(color)) {
    // categoryPalette does not exist as a direct key on the theme which causes an error when trying to access categoryPalette[color] using styleoverrides
    // Instead, we use the sx prop to access the categoryPalette[color] and apply the color to the chip
    return (
      <MUIChip
        ref={ref}
        sx={{
          backgroundColor: theme.palette.categoryPalette[color].surface,
          [`&:hover`]: {backgroundColor: darken(theme.palette.categoryPalette[color].surface, 0.1)},
          color: theme.palette.categoryPalette[color].text,
          borderColor:
            variant === 'outlined' ? theme.palette.categoryPalette[color].stroke : 'inherit',
          ...(deletable && {
            [`&.${chipClasses.root}`]: {
              '&:has(>svg)': {
                svg: {
                  color: theme.palette.categoryPalette[color].text,
                  '&:hover': {
                    color: theme.palette.categoryPalette[color].surfaceInverted,
                  },
                },
              },
            },
          }),
          ...sx,
        }}
        variant={variant}
        label={label_}
        {...rest}
      />
    );
  }

  return <MUIChip ref={ref} color={color} variant={variant} label={label_} {...rest} />;
};

export const Chip = forwardRef(ChipComponent);

Chip.displayName = 'Chip';

export type {ChipProps};
