import type {<PERSON><PERSON>, <PERSON>Obj} from '@storybook/react';
import type {ComponentProps} from 'react';
import {Box, Chip, Stack, SvgIcon, Typography} from 'src/index';

import argTypes from './argTypes';

const meta = {
  component: Chip,
  title: 'components/Data Display/Chip',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=12307-195&mode=design&t=tmIbKDCVR21mZCtQ-0',
    },
  },
} as Meta<ComponentProps<typeof Chip>>;

export default meta;
type Story = StoryObj<typeof Chip>;

export const Basic: Story = {
  args: {
    label: 'Chip text',
  },
  render: args => <Chip {...args} />,
};

export const Usage: Story = {
  render: () => (
    <>
      <Box display="inline" mr={2}>
        <Chip label="Chip text" />
      </Box>
      <Box display="inline" mr={2}>
        <Chip disabled label="Chip Disabled" />
      </Box>
    </>
  ),
};

export const Sizes: Story = {
  render: () => (
    <>
      <Box display="inline" mr={2}>
        <Chip label="Medium" />
      </Box>
      <Box display="inline" mr={2}>
        <Chip label="Small" size="small" />
      </Box>
    </>
  ),
};

const chipColors = [
  'success',
  'warning',
  'error',
  'info',
  'primary',
  'secondary',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
] as const;
export const ColorsVariants: Story = {
  render: () => (
    <Stack gap={2}>
      <Typography variant="h5">Filled (Default)</Typography>

      <Stack gap={2} direction="row" width={theme => theme.fixedWidths.md} flexWrap="wrap">
        {chipColors.map(color => (
          <Chip
            key={color}
            color={color}
            label={'chip'}
            icon={<SvgIcon type="bell" />}
            onClick={() => null}
          />
        ))}
      </Stack>

      <Typography variant="h5">Outlined</Typography>

      <Stack gap={2} direction="row" width={theme => theme.fixedWidths.md} flexWrap="wrap">
        {chipColors.map(color => (
          <Chip
            key={color}
            color={color}
            label={'chip'}
            variant="outlined"
            icon={<SvgIcon type="bell" />}
            onClick={() => null}
          />
        ))}
      </Stack>
    </Stack>
  ),
};

export const Variants: Story = {
  render: () => (
    <Box>
      <Box mb={2}>
        <Typography variant="h5">Filled / Default</Typography>
      </Box>
      <Box mb={2}>
        {chipColors.map(color => (
          <Box display="inline" mr={2} key={color}>
            <Chip color={color} label={'chip'} />
          </Box>
        ))}
      </Box>
      <Box mb={2}>
        {chipColors.map(color => (
          <Box display="inline" mr={2} key={color}>
            <Chip color={color} label={'chip'} size="small" />
          </Box>
        ))}
      </Box>
      <Box mt={4}>
        <Box mb={2}>
          <Typography variant="h5">Outlined</Typography>
        </Box>
        <Box mb={2}>
          {chipColors.map(color => (
            <Box display="inline" mr={2} key={color}>
              <Chip color={color} label={'chip'} variant="outlined" />
            </Box>
          ))}
        </Box>
        <Box>
          {chipColors.map(color => (
            <Box display="inline" mr={2} key={color}>
              <Chip color={color} size="small" label={'chip'} variant="outlined" />
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  ),
};

export const Icons: Story = {
  render: () => (
    <Stack gap={4}>
      <Typography variant="body2" color="secondary">
        For left positioned Icons, the <code>icon</code> prop should be used. For right positioned
        icons, the <code>deleteIcon</code> should be used. Note, for the delete, an{' '}
        <code>onDelete</code> property is provided.
      </Typography>
      {['medium' as const, 'small' as const].map(size => (
        <>
          <Typography variant="h5">{size}</Typography>
          <Stack gap={2} direction="row">
            <Chip label="Text with icon left" size={size} icon={<SvgIcon type="check-mark" />} />
            <Chip
              onDelete={() => {}}
              clickable={false}
              deleteIcon={<SvgIcon type="cross" />}
              label="Text with icon right"
              color="primary"
              size={size}
            />
          </Stack>
        </>
      ))}
    </Stack>
  ),
};

export const LongTextTruncation: Story = {
  render: () => (
    <Stack gap={4}>
      <Typography variant="body2" color="secondary">
        For Chips with a <code>label</code> property of type <code>string</code> that exceeds the
        chip width, the label text will be automatically truncated and shown with an ellipsis. On
        hover, a tooltip will be shown.
      </Typography>
      <Box width={theme => theme.fixedWidths.xs}>
        <Chip label="Long text which should be truncated" icon={<SvgIcon type="check-mark" />} />
      </Box>
    </Stack>
  ),
};
