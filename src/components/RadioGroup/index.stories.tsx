import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {
  Box,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
} from 'src/index';
import type {RadioGroupProps} from 'src/index';
import {controlsExclude} from 'src/storybook-utils/argTypes';
import {Typography} from '@mui/material';

import argTypes from './argTypes';

export default {
  component: RadioGroup,
  title: 'components/Inputs/RadioGroup',
  argTypes,
  parameters: {
    controls: {
      exclude: controlsExclude,
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=1808%3A17831',
    },
  },
} as Meta<RadioGroupProps>;

export const Basic: StoryObj<RadioGroupProps> = {
  args: {
    name: 'radio-buttons-group',
    defaultValue: 'Cat',
  },
  render: args => (
    <Stack gap={2} width={theme => theme.fixedWidths.xs}>
      <FormControl>
        <FormLabel>Animals</FormLabel>
        <RadioGroup name={args.name} defaultValue={args.defaultValue}>
          <Stack gap={2} mt={2}>
            <FormControlLabel
              value="Cat"
              control={<Radio />}
              label="The cat is the best type of house pet. That is an indisputable fact."
            />
            <FormControlLabel
              value="Dog"
              control={<Radio />}
              label="The dog is the best type of house pet. That is an indisputable fact."
            />
            <FormControlLabel
              value="Fish"
              control={<Radio />}
              label="The fish is the best type of house pet. That is an indisputable fact."
            />
          </Stack>
        </RadioGroup>
      </FormControl>
    </Stack>
  ),
};

export const Spacing: StoryObj<RadioGroupProps> = {
  args: {
    name: 'radio-buttons-group',
    defaultValue: 'Cat',
  },
  render: args => (
    <Stack gap={4}>
      <Typography variant="body2" color="secondary">
        Note, vertical spacing for RadioGroups should be defined by the developer. This can be done
        via placing FormControlLabels or Radio components within a Stack and utilizing the gap
        property. Typically this should be set to spacing(2).
      </Typography>
      <FormControl>
        <FormLabel>
          <Typography variant="h5">Spacing 2</Typography>
        </FormLabel>
        <RadioGroup {...args}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>
      <FormControl>
        <Typography variant="h5">Spacing 4</Typography>
        <RadioGroup {...args}>
          <Stack gap={4} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>
      <FormControl>
        <Typography variant="h5">Spacing 8</Typography>
        <RadioGroup {...args}>
          <Stack gap={8} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>
    </Stack>
  ),
};

export const Directions: StoryObj<RadioGroupProps> = {
  args: {
    name: 'radio-buttons-group',
    defaultValue: 'Dog',
  },
  render: args => (
    <Stack gap={4}>
      <FormControl>
        <FormLabel>Animals (default direction)</FormLabel>
        <RadioGroup name={args.name} defaultValue={args.defaultValue}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel>Animals (row direction)</FormLabel>
        <RadioGroup row name={args.name} defaultValue={args.defaultValue}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>
    </Stack>
  ),
};

export const States: StoryObj<RadioGroupProps> = {
  args: {
    name: 'radio-buttons-group',
    defaultValue: 'Dog',
  },
  render: args => (
    <Stack gap={2}>
      <FormControl>
        <FormLabel>Animals (selected)</FormLabel>
        <RadioGroup name={args.name} defaultValue={args.defaultValue}>
          <Stack gap={4} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl>
        <FormLabel>Animals (unselected)</FormLabel>
        <RadioGroup name={args.name}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>
      <FormControl disabled>
        <FormLabel>Animals (disabled state)</FormLabel>
        <RadioGroup name={args.name}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl disabled>
        <FormLabel>Animals (disabled selected)</FormLabel>
        <RadioGroup name={args.name} defaultValue={args.defaultValue}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl error>
        <FormLabel>Animals (error)</FormLabel>
        <RadioGroup name={args.name} defaultValue={args.defaultValue}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio color="error" />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
      </FormControl>

      <FormControl error>
        <FormLabel>Animals (error with helper text)</FormLabel>
        <RadioGroup name={args.name} defaultValue={args.defaultValue}>
          <Stack gap={2} mt={2}>
            <FormControlLabel value="Cat" control={<Radio />} label="Cat" />
            <FormControlLabel value="Dog" control={<Radio color="error" />} label="Dog" />
            <FormControlLabel value="Fish" control={<Radio />} label="Fish" />
          </Stack>
        </RadioGroup>
        <Box mt={2}>
          <FormHelperText>Example error message</FormHelperText>
        </Box>
      </FormControl>
    </Stack>
  ),
};
