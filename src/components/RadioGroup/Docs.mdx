import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as RadioGroupStories from './index.stories.tsx'

<Meta of={RadioGroupStories} />

 ## Overview
`RadioGroup` should be composed with `Radio`.

[MUI RadioGroup](https://mui.com/material-ui/react-radio-button/)

<Canvas of={RadioGroupStories.Basic} />
<Controls of={RadioGroupStories.Basic}/>

## Spacing
<Canvas of={RadioGroupStories.Spacing} />

## Directions
<Canvas of={RadioGroupStories.Directions} />

## States
<Canvas of={RadioGroupStories.States} />