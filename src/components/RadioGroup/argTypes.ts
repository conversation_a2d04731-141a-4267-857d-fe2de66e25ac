import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const radioArgTypeKeys = [
  'name',
  'defaultValue',
  'disabled',
  'id',
  'inputProps',
  'inputRef',
  'onChange',
  'sx',
  'value',
];

const RADIO_GROUP_ARGTYPES = {
  onChange: {
    ...FORM_COMPONENT_ARGTYPES['onChange'],
    description:
      'Callback fired when the state is changed. <br /> <code>function(event: React.ChangeEvent) => void</code><br /><code>event</code> The event source of the callback. You can pull out the new value state by accessing <br /><code>event.target.value</code>',
  },
};

const argTypes = getArgTypes(radioArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...RADIO_GROUP_ARGTYPES,
});

export default argTypes;
