import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {intlFormat} from 'date-fns';
import enUS from 'date-fns/locale/en-US';
import type {ComponentProps} from 'react';
import {Divider, Typography} from '@mui/material';
import {Box} from '@mui/system';

import {TimeScale, type TimelineRow} from 'src/components/Timeline/types';

import {Timeline as TimelineComponent} from '.';

type TimelineProps = ComponentProps<typeof TimelineComponent>;

const wheatDates = {
  startDate: new Date('2018-07-04'),
  endDate: new Date('2018-10-03'),
};

const cornDates = {
  startDate: new Date('2017-10-01'),
  endDate: new Date('2018-01-30'),
};

const fallowDates = {
  startDate: new Date('2018-04-01'),
  endDate: new Date('2018-06-08'),
};

const exampleRows: TimelineRow[] = [
  {
    id: 'crop',
    title: 'Crops',
    events: [
      {
        id: 'corn-1',
        title: 'Corn',
        startDate: cornDates.startDate,
        endDate: cornDates.endDate,
        color: '1',
        icon: 'corn',
        outline: true,
        tooltip: (
          <>
            <div>
              <strong>Crop:</strong> <span>Corn</span>
            </div>
            <div>
              <strong>Crop class:</strong> <span>Commodity</span>
            </div>
            <div>
              <strong>Growing period:</strong>{' '}
              <span>
                {intlFormat(cornDates.startDate)} - {intlFormat(cornDates.endDate)}
              </span>
            </div>
          </>
        ),
      },
      {
        id: 'fallow-1',
        title: 'Fallow',
        startDate: fallowDates.startDate,
        endDate: fallowDates.endDate,
        color: '9',
        icon: 'fallow',
        tooltip: (
          <>
            <div>
              <strong>Crop:</strong> <span>Fallow</span>
            </div>
            <div>
              <strong>Growing period:</strong>{' '}
              <span>
                {intlFormat(fallowDates.startDate)} - {intlFormat(fallowDates.endDate)}
              </span>
            </div>
          </>
        ),
      },
      {
        id: 'wheat-1',
        title: 'Wheat',
        startDate: wheatDates.startDate,
        endDate: wheatDates.endDate,
        color: '8',
        icon: 'wheat_spring',
        tooltip: (
          <>
            <div>
              <strong>Crop:</strong> <span>Wheat</span>
            </div>
            <div>
              <strong>Crop class:</strong> <span>Commodity</span>
            </div>
            <div>
              <strong>Growing period:</strong>{' '}
              <span>
                {intlFormat(wheatDates.startDate)} - {intlFormat(wheatDates.endDate)}
              </span>
            </div>
          </>
        ),
      },
    ],
  },
  {
    id: 'tillage',
    title: 'Tillage',
    events: [
      {
        id: 'tillage-1',
        startDate: new Date('2018-06-07'),
        color: '5',
      },
      {
        id: 'tillage-2',
        startDate: new Date('2018-06-15'),
        color: '5',
        variant: 'subtle',
      },
      {
        id: 'tillage-3',
        startDate: new Date('2018-05-01'),
        endDate: new Date('2018-05-28'),
        tooltip: 'Filling the entire month of February',
        color: '5',
      },
    ],
  },
  {
    id: 'nutrient',
    title: 'Nutrients',
    events: [
      {
        id: 'nutrient-1',
        startDate: new Date('2018-03-07'),
        color: '4',
      },
      {
        id: 'nutrient-2',
        startDate: new Date('2018-03-15'),
        color: '4',
        variant: 'subtle',
      },
      {
        id: 'nutrient-3',
        startDate: new Date('2018-02-01'),
        endDate: new Date('2018-02-28'),
        tooltip: 'Filling the entire month of February',
        color: '4',
      },
    ],
  },
  {
    id: 'irrigation',
    title: 'Irrigation',
    events: [
      {
        id: 'irrigation-1',
        startDate: new Date('2018-04-01'),
        color: '2',
        tooltip: '1st of April',
      },
      {
        id: 'irrigation-2',
        startDate: new Date('2018-05-13'),
        color: '2',
        variant: 'subtle',
      },
      {
        id: 'irrigation-3',
        startDate: new Date('2018-02-01'),
        tooltip: 'Filling a quarter of February',
        color: '2',
      },
      {
        id: 'irrigation-4',
        startDate: new Date('2018-02-08'),
        tooltip: 'Filling a quarter of February',
        color: '2',
      },
      {
        id: 'irrigation-5',
        startDate: new Date('2018-02-15'),
        tooltip: 'Filling a quarter of February',
        color: '2',
      },
      {
        id: 'irrigation-6',
        startDate: new Date('2018-02-22'),
        tooltip: 'Filling a quarter of February',
        color: '2',
      },
    ],
  },
];

export default {
  component: TimelineComponent,
  title: 'components/Data Display/Timeline',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/eNQYoIzCPl4sTDTHWWNDpS/Nutrient-Management-as-an-Intervention?node-id=1661%3A202045&t=GS47Vmr5BjvM7Kfp-4',
    },
  },
} as Meta<TimelineProps>;

export const Basic: StoryObj<TimelineProps> = {
  render: () => {
    return (
      <TimelineComponent
        highlightedRowId="crop"
        locale={enUS}
        markers={[
          {
            content: '5th of February',
            date: new Date('2018-02-05'),
          },
          {
            content: 'Wheat planted',
            date: wheatDates.startDate,
            alignment: 'left',
          },

          {
            content: 'Wheat harvested',
            date: wheatDates.endDate,
            alignment: 'right',
            endOfDay: true,
          },
          {
            content: '7th of May',
            date: new Date('2018-05-07'),
          },
        ]}
        initialStartDate={new Date('2018-01-01')}
        initialEndDate={new Date('2018-12-31')}
        rows={exampleRows}
      />
    );
  },
};

export const Empty: StoryObj<TimelineProps> = {
  render: () => {
    return (
      <TimelineComponent
        highlightedRowId="crop"
        locale={enUS}
        initialStartDate={new Date('2015-01-01')}
        initialEndDate={new Date('2024-12-31')}
        rows={[]}
        timeScale={TimeScale.Biannual}
        emptyMessage="Fill out the table below to get started."
      />
    );
  },
};

export const TimeScales: StoryObj<TimelineProps> = {
  render: () => {
    return (
      <Box>
        <Typography variant="h4">Biannual</Typography>
        <Box pt={2} pb={6}>
          <TimelineComponent
            highlightedRowId="crop"
            locale={enUS}
            initialStartDate={new Date('2018-01-01')}
            initialEndDate={new Date('2018-12-31')}
            timeScale={TimeScale.Biannual}
            rows={exampleRows}
            emptyMessage="Fill out the table below to get started."
          />
        </Box>
        <Divider />
        <Box pt={4}>
          <Typography variant="h4">Monthly</Typography>
        </Box>
        <Box py={2} pb={6}>
          <TimelineComponent
            highlightedRowId="crop"
            locale={enUS}
            initialStartDate={new Date('2018-01-01')}
            initialEndDate={new Date('2018-12-31')}
            timeScale={TimeScale.Monthly}
            rows={exampleRows}
            emptyMessage="Fill out the table below to get started."
          />
        </Box>
        <Divider />
        <Box pt={4}>
          <Typography variant="h4">Daily</Typography>
        </Box>
        <Box py={2}>
          <TimelineComponent
            highlightedRowId="crop"
            locale={enUS}
            initialStartDate={new Date('2018-01-01')}
            initialEndDate={new Date('2018-12-31')}
            timeScale={TimeScale.Daily}
            rows={exampleRows}
            emptyMessage="Fill out the table below to get started."
          />
        </Box>
      </Box>
    );
  },
};
