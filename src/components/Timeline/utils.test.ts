import enUS from 'date-fns/locale/en-US';

import {errorMessage} from './constants';
import {TimeScale} from './types';
import {
  calculateTimelineRange,
  getGridLines,
  getOffset,
  getSections,
  getTotalUnits,
  getUnitWidth,
  getWidth,
} from './utils';
import {
  calculateTimelineRangeCases,
  getGridLinesCases,
  getOffsetCases,
  getSectionsCases,
  getTotalUnitsCases,
  getUnitWidthCases,
  getWidthCases,
} from './utils.test-data';

describe('calculateTimelineRange', () => {
  test.each(calculateTimelineRangeCases)(
    '$description',
    ({rows, timeScale, minimumRange, expected}) => {
      expect(calculateTimelineRange(rows, timeScale, minimumRange)).toEqual(expected);
    }
  );

  test('throws error when no events were found, or a minimum range was not provided', () => {
    expect(() => calculateTimelineRange([], TimeScale.Daily)).toThrowError(
      errorMessage.noEventsOrMinRangeProvided
    );
  });
});

describe('getSections', () => {
  test.each(getSectionsCases)('$description', ({timeScale, timelineRange, expected}) => {
    expect(getSections(timeScale, timelineRange, enUS)).toEqual(expected);
  });

  //TO DO: fix mocked data
  // test.each(getSectionsCasesUk)('$description', ({timeScale, timelineRange, expected}) => {
  //   expect(getSections(timeScale, timelineRange, uk)).toEqual(expected);
  // });
});

describe('getGridLines', () => {
  test.each(getGridLinesCases)(
    '$description',
    ({timeScale, timelineRange, weekStartsOn, unitWidth, expected}) => {
      expect(getGridLines(timeScale, timelineRange, weekStartsOn, unitWidth)).toEqual(expected);
    }
  );
});

describe('getWidth', () => {
  test.each(getWidthCases)(
    '$description',
    ({timeScale, unitWidth, startDate, endDate, expected}) => {
      expect(getWidth(timeScale, unitWidth, startDate, endDate)).toEqual(expected);
    }
  );
});

describe('getOffset', () => {
  test.each(getOffsetCases)(
    '$description',
    ({timeScale, unitWidth, startDate, timelineRange, expected}) => {
      expect(getOffset(timeScale, unitWidth, startDate, timelineRange)).toEqual(expected);
    }
  );
});

describe('getUnitWidth', () => {
  test.each(getUnitWidthCases)(
    '$description',
    ({timeScale, containerWidth, totalUnits, expected}) => {
      expect(getUnitWidth(timeScale, containerWidth, totalUnits)).toEqual(expected);
    }
  );
});

describe('getTotalUnits', () => {
  test.each(getTotalUnitsCases)('$description', ({timeScale, timelineRange, expected}) => {
    expect(getTotalUnits(timeScale, timelineRange)).toEqual(expected);
  });
});
