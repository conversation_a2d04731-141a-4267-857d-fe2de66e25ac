import type {ReactNode} from 'react';
import type {IconType} from 'src/tokens/themeTypes';
import {type CategoryPalette} from 'src/tokens/themeTypes';

export type TimelineRow = {
  id: string;
  title: string;
  events: TimelineEvent[];
};

export type TimelineEvent = {
  id: string;
  title?: string;
  variant?: 'subtle';
  startDate: Date;
  endDate?: Date | null;
  tooltip?: ReactNode;
  color: keyof CategoryPalette;
  icon?: IconType;
  outline?: boolean;
};

export type TimelineRange = {
  startDate: Date;
  endDate: Date;
};

export type TimelineSection = {
  label: string;
  startDate: Date;
  endDate: Date;
};

export type GridLine = {
  startDate: Date;
};

export enum TimeScale {
  /**
   * Section width = 1 month
   *
   * Unit width = 1 day
   */
  Daily = 'daily',
  /**
   * Section width = 1 month
   *
   * GridLine width = 1 week
   *
   * Unit width = 1 week
   */
  Monthly = 'monthly',
  /**
   * Section width = 6 months
   *
   * GridLine width = 1 month
   *
   * Unit width = 1 week
   */
  Biannual = 'biannual',
}

export enum DayOfWeek {
  Sunday = 0,
  Monday = 1,
  Tuesday = 2,
  Wednesday = 3,
  Thursday = 4,
  Friday = 5,
  Saturday = 6,
}

export enum Month {
  January = 0,
  February = 1,
  March = 2,
  April = 3,
  May = 4,
  June = 5,
  July = 6,
  August = 7,
  September = 8,
  October = 9,
  November = 10,
  December = 11,
}
