import type {Locale} from 'date-fns';
import {
  addDays,
  addMonths,
  addWeeks,
  daysInWeek,
  differenceInDays,
  endOfMonth,
  format,
  getDaysInMonth,
  isValid,
  startOfDay,
  startOfMonth,
  startOfWeek,
} from 'date-fns';

import {errorMessage, monthlyGridLineMinWidth, monthsInHalfYear} from './constants';
import type {DayOfWeek, GridLine, TimelineRange, TimelineRow, TimelineSection} from './types';
import {Month, TimeScale} from './types';

/**
 * `startDate` is the first of the month from the earliest date out of all events and the `minimumRange`.
 *
 * `endDate` is the last of the month from the latest date out of all events and the `minimumRange`.
 *
 * TimelineRange also considers the extensions caused by sections,
 * for example the "biannual" timeScale uses 6 months as one section and so may add months to the range to fill the section,
 * whereas the "monthly" and "daily" timeScales uses 1 month as one section and so will add days to the range provided by events / minimumRange.
 */
export function calculateTimelineRange(
  rows: TimelineRow[],
  timeScale: TimeScale,
  minimumRange?: TimelineRange
): TimelineRange {
  let startDate: Date | undefined;
  let endDate: Date | undefined;

  if (minimumRange) {
    startDate = minimumRange.startDate;
    endDate = minimumRange.endDate;
  } else {
    const firstEvent = rows.find(row => row.events.length > 0)?.events[0];
    startDate = firstEvent?.startDate;
    endDate = firstEvent?.endDate || startDate;
  }

  if (startDate === undefined || endDate === undefined) {
    throw new Error(errorMessage.noEventsOrMinRangeProvided);
  }

  for (let r = 0; r < rows.length; r++) {
    const events = rows[r].events;

    for (let e = 0; e < events.length; e++) {
      const event = events[e];

      if (event.startDate < startDate) {
        startDate = event.startDate;
      }

      if (event.endDate && event.endDate > endDate) {
        endDate = event.endDate;
      }

      if (event.startDate > endDate) {
        endDate = event.startDate;
      }

      if (event.endDate && event.endDate < startDate) {
        startDate = event.endDate;
      }
    }
  }

  switch (timeScale) {
    case TimeScale.Daily:
    case TimeScale.Monthly:
      startDate = startOfMonth(startDate);
      endDate = startOfDay(endOfMonth(endDate));
      break;

    case TimeScale.Biannual:
      startDate = getPreviousHalfYearStart(startDate);
      endDate = getNextHalfYearEnd(endDate);
      break;
  }

  return {
    startDate,
    endDate,
  };
}

function getPreviousHalfYearStart(date: Date): Date {
  const currentYear = date.getFullYear();
  const currentMonth = date.getMonth();
  const currentDate = date.getDate();

  // If January 1st or July 1st, return the date
  if ((currentMonth === 0 && currentDate === 1) || (currentMonth === 6 && currentDate === 1)) {
    return date;
  }

  if (currentMonth >= 6) {
    // July 1st of the current year
    return new Date(currentYear, 6, 1);
  }

  if (currentMonth >= 1) {
    // January 1st of the current year
    return new Date(currentYear, 0, 1);
  }

  // July 1st of the previous year
  return new Date(currentYear - 1, 6, 1);
}

function getNextHalfYearEnd(date: Date): Date {
  const currentYear = date.getFullYear();
  const currentMonth = date.getMonth();

  if (currentMonth >= 0 && currentMonth < 6) {
    return new Date(currentYear, 5, 30);
  }

  return new Date(currentYear, 11, 31);
}

export function getSections(
  timeScale: TimeScale,
  timelineRange: TimelineRange,
  locale: Locale
): TimelineSection[] {
  const sections: TimelineSection[] = [];
  let startDate = new Date(timelineRange.startDate);
  const endDate = timelineRange.endDate;

  switch (timeScale) {
    case TimeScale.Daily:
    case TimeScale.Monthly:
      while (startDate <= endDate) {
        sections.push({
          // remove the dot, some locales add this in the month abbreviation
          label: format(startDate, "MMM ''yy", {locale}).replace('.', ''),
          startDate,
          endDate: new Date(new Date(startDate).setDate(getDaysInMonth(startDate))),
        });

        startDate = addMonths(startDate, 1);
      }
      break;

    case TimeScale.Biannual:
      const halfYearRemainder = startDate.getMonth() % monthsInHalfYear;
      if (halfYearRemainder !== 0) {
        startDate = addMonths(startDate, monthsInHalfYear - halfYearRemainder);
      }

      while (startDate <= endDate) {
        const halfYearOffset = addMonths(startDate, monthsInHalfYear - 1);

        sections.push({
          label: format(startDate, "MMM ''yy", {locale}).replace('.', ''),
          startDate,
          endDate: new Date(new Date(halfYearOffset).setDate(getDaysInMonth(halfYearOffset))),
        });

        startDate = addMonths(startDate, monthsInHalfYear);
      }
      break;
  }

  return sections;
}

export function getGridLines(
  timeScale: TimeScale,
  timelineRange: TimelineRange,
  weekStartsOn: DayOfWeek,
  unitWidth: number
): GridLine[] {
  switch (timeScale) {
    case TimeScale.Daily:
      return [];

    case TimeScale.Monthly: {
      // Don't show grid lines if the unit width is too small to see clearly
      if (unitWidth < monthlyGridLineMinWidth) {
        return [];
      }

      return getWeekGridLines(timelineRange, weekStartsOn);
    }

    case TimeScale.Biannual:
      return getMonthGridLines(timelineRange);
  }
}

function getWeekGridLines(timelineRange: TimelineRange, weekStartsOn: DayOfWeek): GridLine[] {
  const weeks: GridLine[] = [];
  let startDate = new Date(timelineRange.startDate);

  if (startDate.getDay() > weekStartsOn) {
    // initially set startDate to the next first day of a week
    startDate = startOfWeek(addWeeks(startDate, 1), {weekStartsOn});
  }

  while (startDate <= timelineRange.endDate) {
    const dayOfMonth = startDate.getDate();

    // don't include the first day in the month, these are the thicker Section lines
    if (dayOfMonth === 1) {
      startDate = addWeeks(startDate, 1);
      continue;
    }

    weeks.push({
      startDate,
    });

    const weekOffset = daysInWeek - (startDate.getDay() - weekStartsOn);
    startDate = addDays(startDate, weekOffset);
  }

  return weeks;
}

function getMonthGridLines(timelineRange: TimelineRange): GridLine[] {
  const months: GridLine[] = [];
  let startDate = new Date(timelineRange.startDate);
  const endDate = timelineRange.endDate;

  if (startDate.getDate() > 1) {
    // set startDate to the first day of the next month
    startDate = startOfMonth(addMonths(startDate, 1));
  }

  while (startDate <= endDate) {
    const month = startDate.getMonth();

    // don't include these months, these are the thicker Section lines
    if (month !== Month.January && month !== Month.July) {
      months.push({
        startDate,
      });
    }

    startDate = addMonths(startDate, 1);
  }

  return months;
}

/**
 * Returns the width of a TimelineElement in pixels.
 */
export function getWidth(
  timeScale: TimeScale,
  unitWidth: number,
  startDate: Date,
  endDate?: Date | null,
  {hasBorderOffset = true} = {}
): number {
  const borderOffset = hasBorderOffset ? 1 : 0;

  if (!endDate) {
    return unitWidth + borderOffset;
  }

  const totalDays =
    differenceInDays(endDate, startDate) +
    // Inclusive of the end date
    1;

  switch (timeScale) {
    case TimeScale.Daily:
      return totalDays * unitWidth + borderOffset;

    case TimeScale.Monthly:
    case TimeScale.Biannual:
      const totalWeeks = totalDays / daysInWeek;
      return totalWeeks * unitWidth + borderOffset;
  }
}

/**
 * Returns the offset of a TimelineElement in pixels.
 */
export function getOffset(
  timeScale: TimeScale,
  unitWidth: number,
  startDate: Date,
  timelineRange: TimelineRange
): number {
  const daysOffset =
    // Includes days from the start of the first month of the timeline range.
    // We subtract 1 to be inclusive of the start date.
    timelineRange.startDate.getDate() - 1;

  const totalDays = differenceInDays(startDate, timelineRange.startDate) + daysOffset;

  switch (timeScale) {
    case TimeScale.Daily:
      return totalDays * unitWidth;

    case TimeScale.Monthly:
    case TimeScale.Biannual:
      const totalWeeks = totalDays / daysInWeek;
      return totalWeeks * unitWidth;
  }
}

/**
 * Returns the width of a unit in pixels.
 */
export function getUnitWidth(
  timeScale: TimeScale,
  containerWidth: number,
  totalUnits: number
): number {
  const width = containerWidth / totalUnits;
  const minUnitWidth = getMinUnitWidth(timeScale);

  if (width > minUnitWidth) {
    return width;
  }

  return minUnitWidth;
}

function getMinUnitWidth(timeScale: TimeScale): number {
  switch (timeScale) {
    case TimeScale.Daily:
      return 8;

    case TimeScale.Monthly:
      return 12;

    case TimeScale.Biannual:
      return 4;
  }
}

type ProcessRowResponse = {
  processedRows: TimelineRow[];
  timelineRowsHaveEvents: boolean;
};

/**
 * Removes events with invalid dates from the rows.
 * An event is considered invalid if it has an invalid start date or end date.
 * Returns an object with the processed rows and a boolean indicating if the timeline rows have events.
 */
export function processRows(rows: TimelineRow[]): ProcessRowResponse {
  let timelineRowsHaveEvents = false;
  const validatedRows: TimelineRow[] = [];
  rows.forEach(row => {
    const events = row.events.filter(event => {
      if (event.endDate) {
        return isValid(event.endDate) && isValid(event.startDate);
      }

      return isValid(event.startDate);
    });

    if (!timelineRowsHaveEvents && events.length > 0) {
      timelineRowsHaveEvents = true;
    }
    validatedRows.push({
      ...row,
      events,
    });
  });

  return {
    processedRows: validatedRows,
    timelineRowsHaveEvents,
  };
}

export function removeEventsWithInvalidDates(rows: TimelineRow[]): TimelineRow[] {
  return rows.map(row => {
    const events = row.events.filter(event => {
      if (event.endDate) {
        return isValid(event.endDate) && isValid(event.startDate);
      }

      return isValid(event.startDate);
    });

    return {
      ...row,
      events,
    };
  });
}

export function getTotalUnits(timeScale: TimeScale, timelineRange: TimelineRange): number {
  const totalDays =
    differenceInDays(timelineRange.endDate, timelineRange.startDate) +
    // Inclusive of the end date
    1;

  switch (timeScale) {
    case TimeScale.Daily:
      return totalDays;

    case TimeScale.Monthly:
    case TimeScale.Biannual:
      return totalDays / daysInWeek;
  }
}
