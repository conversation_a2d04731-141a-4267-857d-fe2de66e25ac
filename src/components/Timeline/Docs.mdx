import { Meta, <PERSON>vas, <PERSON>s, Story } from "@storybook/blocks";
import * as TimelineStories from './index.stories.tsx'


<Meta of={TimelineStories} />

## Overview
The timeline component displays a series of events in chronological order.

It is currently used to display MRV events during the data entry stages in an MRV program.

<Canvas of={TimelineStories.Basic} />
<Controls  of={TimelineStories.Basic} />

## Empty
Timeline displayed with no events
<Canvas of={TimelineStories.Empty} />


## Time Scale
<Canvas of={TimelineStories.TimeScales} />
