import {render, screen} from '@testing-library/react';
import enUS from 'date-fns/locale/en-US';
import {DesignSystemProvider} from 'src/design-system-provider';

import {Timeline} from './index';

window.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

describe('Timeline', () => {
  it('displays an empty timeline with the default empty message if no event data present', () => {
    renderWithProvider(
      <Timeline
        rows={[]}
        locale={enUS}
        initialStartDate={new Date('10-10-2023')}
        initialEndDate={new Date('12-12-2023')}
      />
    );
    expect(screen.getByText('There are no timeline events to display.')).toBeInTheDocument();
  });
  it('displays an empty timeline with the provided empty message if no event data present', () => {
    renderWithProvider(
      <Timeline
        rows={[]}
        locale={enUS}
        initialStartDate={new Date('10-10-2023')}
        initialEndDate={new Date('12-12-2023')}
        emptyMessage="No events to display"
      />
    );
    expect(screen.getByText('No events to display')).toBeInTheDocument();
  });

  it('displays a timeline with the provided event data', () => {
    renderWithProvider(
      <Timeline
        rows={[
          {
            id: '1',
            title: 'Event 1',
            events: [
              {
                id: '1',
                title: 'Event 1.1',
                startDate: new Date('10-10-2023'),
                endDate: new Date('10-12-2023'),
                color: '1',
              },
            ],
          },
        ]}
        locale={enUS}
        initialStartDate={new Date('10-10-2023')}
        initialEndDate={new Date('12-12-2023')}
      />
    );
    expect(screen.getByText('Event 1')).toBeInTheDocument();
  });
  // TO DO: https://regrow.atlassian.net/browse/DES-132 Add tests for display of markers and tooltips
});

const renderWithProvider = (children: JSX.Element) => {
  render(<DesignSystemProvider muiThemeKey={0}>{children}</DesignSystemProvider>);
};
