import type {ReactNode} from 'react';
import {Box, styled, Typography} from '@mui/material';

export type TimelineMarker = {
  content: ReactNode;
  /**
   * This controls the position of the marker on the timeline.
   */
  date: Date;
  /**
   * Positions the marker at the end of the day.
   */
  endOfDay?: boolean;
  /**
   * Defaults to a centered alignment.
   */
  alignment?: 'left' | 'right';
};

type MarkerProps = Pick<TimelineMarker, 'content' | 'alignment'> & {offset: number};
export function Marker({content, offset, alignment}: MarkerProps) {
  return (
    <Box
      position="absolute"
      bottom={0}
      display="flex"
      width="2px"
      height="calc(100% - 36px)"
      justifyContent="center"
      bgcolor={theme => theme.palette.semanticPalette.surfaceInverted.main}
      style={{
        left: offset,
      }}
    >
      <Bubble
        position="absolute"
        top={'-16px'}
        paddingY={1}
        paddingX={2}
        $alignment={alignment}
        borderRadius={theme => theme.spacing(theme.borderRadii.pill)}
        bgcolor={theme => theme.palette.semanticPalette.surfaceInverted.main}
        color={theme => theme.palette.semanticPalette.textInverted.main}
        whiteSpace={'nowrap'}
      >
        <Typography variant="body2">{content}</Typography>
      </Bubble>
    </Box>
  );
}

type BubbleProps = {
  $alignment: TimelineMarker['alignment'];
};
const Bubble = styled(Box)<BubbleProps>`
  ${({$alignment}) => {
    switch ($alignment) {
      case 'left':
        return `
          transform: translateX(calc(-50% + 10px));
        `;

      case 'right':
        return `
          left: -10px;
        `;
    }
  }}
`;
