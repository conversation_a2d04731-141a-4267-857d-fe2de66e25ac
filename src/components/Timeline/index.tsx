import type {Locale} from 'date-fns';
import {addDays} from 'date-fns';
import {useLayoutEffect, useMemo, useRef, useState} from 'react';
import type {CSSProperties} from 'react';
import {isIconType} from 'src/utils/typeGuards';
import {Box, styled, Typography} from '@mui/material';

import {SvgIcon} from 'src/components/Icon';

import {useDimensions} from '../../hooks';
import {getTint} from '../../utils/color';
import {Tooltip} from '../Tooltip';
import {Marker} from './Marker';
import type {TimelineMarker} from './Marker';
import type {TimelineEvent, TimelineRange, TimelineRow} from './types';
import {DayOfWeek, TimeScale} from './types';
import {
  calculateTimelineRange,
  getGridLines,
  getOffset,
  getSections,
  getTotalUnits,
  getUnitWidth,
  getWidth,
  processRows,
} from './utils';

type TimelineProps = {
  /**
   * `rows` will still be displayed if no events exist in the row, to hide the row, simply filter it out
   */
  rows: TimelineRow[];
  locale: Locale;
  highlightedRowId?: TimelineRow['id'];
  markers?: TimelineMarker[];
  /**
   * @default TimeScale.Monthly
   */
  timeScale?: TimeScale;
  /**
   * @default DayOfWeek.Monday
   */
  weekStartsOn?: DayOfWeek;
  /**
   * @default 'Timeline'
   */
  ariaLabel?: string;
  style?: CSSProperties;
  className?: string;
  /**
   * Scroll the timeline to the right when the component mounts or is resized.
   */
  scrollToEnd?: boolean;
  /**
   * display a message when there are no events to display
   * @default 'There are no timeline events to display'
   * */
  emptyMessage?: string;
} & MutuallyRequiredDates;

type MutuallyRequiredDates =
  | {
      /**
       * Events occurring before `initialStartDate` will increase the calculated timeline range.
       */
      initialStartDate: Date;
      /**
       * Events occurring after `initialEndDate` will increase the calculated timeline range.
       */
      initialEndDate: Date;
    }
  | {
      initialStartDate?: never;
      initialEndDate?: never;
    };

export function Timeline({
  rows,
  locale,
  highlightedRowId,
  initialStartDate,
  initialEndDate,
  markers = [],
  timeScale = TimeScale.Monthly,
  weekStartsOn = DayOfWeek.Monday,
  ariaLabel = 'Timeline',
  style,
  className,
  scrollToEnd,
  emptyMessage = 'There are no timeline events to display.',
}: TimelineProps) {
  const {validatedRows, eventsExist} = useMemo(() => {
    const {processedRows, timelineRowsHaveEvents: hasEvents} = processRows(rows);
    return {
      validatedRows: processedRows,
      eventsExist: hasEvents,
    };
  }, [rows]);

  const timelineRange = useMemo(() => {
    let minimumRange: TimelineRange | undefined;

    if (!!initialStartDate) {
      minimumRange = {
        startDate: initialStartDate,
        endDate: initialEndDate,
      };
    }

    return calculateTimelineRange(validatedRows, timeScale, minimumRange);
  }, [validatedRows, timeScale, initialStartDate, initialEndDate]);

  const {sections, totalUnits} = useMemo(() => {
    return {
      sections: getSections(timeScale, timelineRange, locale),
      totalUnits: getTotalUnits(timeScale, timelineRange),
    };
  }, [timeScale, timelineRange, locale]);

  // Using state instead of ref so that the Tooltip component can re-render when the element initializes.
  const [scrollingContainer, setScrollingContainer] = useState<HTMLDivElement | null>(null);
  const {width: scrollingContainerWidth} = useDimensions(scrollingContainer);
  const scrollingContainerHasWidth = scrollingContainerWidth > 0;
  const unitWidth = useMemo(
    () => getUnitWidth(timeScale, scrollingContainerWidth, totalUnits),
    [timeScale, scrollingContainerWidth, totalUnits]
  );

  const gridLines = useMemo(() => {
    return getGridLines(timeScale, timelineRange, weekStartsOn, unitWidth);
  }, [timeScale, timelineRange, weekStartsOn, unitWidth]);

  // Storing the ref so that we can scroll to the end of the timeline when the component mounts.
  const scrollingContainerRef = useRef<HTMLDivElement | null>(null);
  useLayoutEffect(() => {
    if (scrollToEnd && scrollingContainerRef.current && scrollingContainerWidth) {
      scrollingContainerRef.current.scrollLeft = scrollingContainerWidth;
    }
  }, [scrollToEnd, scrollingContainerWidth]);

  return (
    <Box
      aria-label={ariaLabel}
      role="img"
      style={style}
      className={className}
      display="flex"
      flexDirection="row"
      width="100%"
      minHeight="150px"
      position={'relative'}
    >
      <Box
        aria-hidden
        display="flex"
        flexDirection="column"
        gap={2}
        paddingTop={12}
        paddingBottom={6}
        paddingRight={1}
        whiteSpace="nowrap"
      >
        {validatedRows.map(row => (
          <Box paddingY={2.5} key={row.id}>
            <RowLabel
              $highlighted={row.id === highlightedRowId}
              variant="body2"
              color={theme => theme.palette.text.secondary}
            >
              {row.title}
            </RowLabel>
          </Box>
        ))}
      </Box>
      {!eventsExist && (
        <Box
          position={'absolute'}
          top="50%"
          left={0}
          right={0}
          zIndex={theme => theme.zIndex.modal}
          margin="0 auto"
          textAlign="center"
          width={theme => theme.fixedWidths.sm}
          sx={{
            transform: `translateY(-50%)`,
          }}
          paddingTop={8}
        >
          <Box
            bgcolor={theme => theme.palette.semanticPalette.surface.warning}
            border={theme => `1px solid ${theme.palette.semanticPalette.stroke.warning}`}
            color={theme => theme.palette.semanticPalette.text.warning}
            paddingY={2}
            paddingX={3}
            display="inline-block"
            borderRadius={theme => theme.spacing(theme.borderRadii.sm)}
          >
            <Typography variant="h6">{emptyMessage}</Typography>
          </Box>
        </Box>
      )}
      <ScrollingContainer
        ref={ref => {
          setScrollingContainer(ref);
          if (scrollToEnd) {
            scrollingContainerRef.current = ref;
          }
        }}
        aria-hidden
      >
        <Box height="100%">
          {scrollingContainerHasWidth && (
            <>
              {sections.map(section => {
                const isJan = section.startDate.getMonth() === 0;
                return (
                  <Box
                    position="absolute"
                    display="flex"
                    flexDirection="column"
                    gap={5}
                    height="100%"
                    key={section.label}
                    style={{
                      width: getWidth(timeScale, unitWidth, section.startDate, section.endDate, {
                        hasBorderOffset: false,
                      }),
                      left: getOffset(timeScale, unitWidth, section.startDate, timelineRange),
                    }}
                  >
                    <Typography variant="body2" fontWeight={isJan ? 'bold' : ''}>
                      {section.label}
                    </Typography>
                    <Box
                      height="100%"
                      borderLeft={theme =>
                        `${isJan ? '2' : '1'}px solid ${
                          isJan
                            ? theme.palette.semanticPalette.stroke.main
                            : theme.palette.semanticPalette.stroke.secondary
                        }`
                      }
                    />
                  </Box>
                );
              })}
              {gridLines.map(line => {
                return (
                  <GridLine
                    key={line.startDate.getTime()}
                    style={{
                      left: getOffset(timeScale, unitWidth, line.startDate, timelineRange),
                    }}
                    timeScale={timeScale}
                    position="absolute"
                    bottom={0}
                    height="calc(100% - 36px)" // 40px is the height of the row labels plus the Section gap
                  />
                );
              })}
            </>
          )}
        </Box>
        <Box position="absolute" top={48} left={0}>
          <Box display="flex" flexDirection="column" gap={2} position="relative">
            {scrollingContainerHasWidth &&
              validatedRows.map(row => (
                <Box
                  key={row.id}
                  display="flex"
                  flexDirection="row"
                  position="relative"
                  height={36}
                >
                  {row.events.map(event => (
                    <Tooltip
                      id={`tooltip-${event.id}`}
                      key={event.id}
                      title={event.tooltip}
                      disableInteractive
                      followCursor
                      PopperProps={{
                        modifiers: [
                          {
                            name: 'preventOverflow',
                            options: {
                              boundary: scrollingContainer,
                              padding: 8,
                            },
                          },
                        ],
                      }}
                      slotProps={{
                        tooltip: {
                          sx: {maxWidth: 'none'},
                        },
                      }}
                    >
                      <Event
                        $color={event.color}
                        $variant={event.variant}
                        $outline={event.outline}
                        position="absolute"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        height={36}
                        top={0}
                        borderRadius={theme => theme.spacing(theme.borderRadii.sm)}
                        style={{
                          width: getWidth(timeScale, unitWidth, event.startDate, event.endDate),
                          left: getOffset(timeScale, unitWidth, event.startDate, timelineRange),
                        }}
                      >
                        {event.title && (
                          <EventLabel
                            borderRadius={theme => theme.spacing(theme.borderRadii.sm)}
                            display="flex"
                            alignItems="center"
                            gap={1}
                            paddingY={1}
                            paddingX={2}
                            $color={event.color}
                            $variant={event.variant}
                            whiteSpace="nowrap"
                          >
                            {isIconType(event.icon) && (
                              <SvgIcon type={event.icon} fontSize="body2" />
                            )}

                            <Typography variant="body1">{event.title}</Typography>
                          </EventLabel>
                        )}
                      </Event>
                    </Tooltip>
                  ))}
                </Box>
              ))}
          </Box>
        </Box>
        {scrollingContainerHasWidth &&
          markers.map(marker => (
            <Marker
              // Overlapping markers are not supported yet, maybe in the future we should use a unique id instead of the date for a key.
              key={marker.date.toISOString()}
              content={marker.content}
              // TODO move this logic into the Marker component, considering marker.endOfDay is a feature of the Marker component
              offset={getOffset(
                timeScale,
                unitWidth,
                marker.endOfDay ? addDays(marker.date, 1) : marker.date,
                timelineRange
              )}
              alignment={marker.alignment}
            />
          ))}
      </ScrollingContainer>
    </Box>
  );
}

const ScrollingContainer = styled('div')`
  width: 100%;
  overflow-x: auto;
  position: relative;
  // For the right border offset (events touching the edge of the container)
  padding-right: 1px;
`;

type GridLineProps = {
  timeScale: TimeScale;
};
const GridLine = styled(Box)<GridLineProps>`
  border-left: 1px solid
    ${({theme, timeScale}) =>
      timeScale === TimeScale.Monthly
        ? getTint(theme.palette.semanticPalette.stroke.secondary, 0.5)
        : theme.palette.semanticPalette.stroke.secondary};
`;

type RowLabelProps = {
  $highlighted: boolean;
};
const RowLabel = styled(Typography)<RowLabelProps>`
  ${({$highlighted, theme}) =>
    $highlighted &&
    `
      font-weight: ${theme.typography.h1.fontWeight};
      color: ${theme.palette.text.primary};
    `}
`;

type EventProps = {
  $color: TimelineEvent['color'];
  $variant: TimelineEvent['variant'];
  $outline: TimelineEvent['outline'];
};
const Event = styled(Box)<EventProps>`
  ${({$color, $variant, $outline, theme}) => {
    if ($variant === 'subtle') {
      return `
        border: 1px solid ${getTint(theme.palette.categoryPalette[$color].chart, 0.5)};
        background-color: ${getTint(theme.palette.categoryPalette[$color].surface, 0.5)};
        border-style: dashed;
        ${
          $outline &&
          `
          outline: 2px solid ${theme.palette.semanticPalette.stroke.info};
          outline-offset: 2px;
        `
        }
      `;
    }

    return `
      border: 1px solid ${theme.palette.categoryPalette[$color].stroke};
      background-color: ${theme.palette.categoryPalette[$color].surface};
       ${
         $outline &&
         `
          outline: 2px solid ${theme.palette.semanticPalette.stroke.info};
          outline-offset: 2px;
        `
       }
    `;
  }};
`;

type EventLabelProps = {
  $color: TimelineEvent['color'];
  $variant: TimelineEvent['variant'];
};
const EventLabel = styled(Box)<EventLabelProps>`
  ${({$color, $variant, theme}) => {
    if ($variant === 'subtle') {
      return `
        background-color: ${getTint(theme.palette.categoryPalette[$color].highlight, 0.5)};
      `;
    }

    return `
      background-color: ${theme.palette.categoryPalette[$color].highlight};
    `;
  }};
`;
