import type {FunctionTestCase} from '../../types';
import {monthlyGridLineMinWidth} from './constants';
import {DayOfWeek, TimeScale} from './types';
import type {
  calculateTimelineRange,
  getGridLines,
  getOffset,
  getSections,
  getTotalUnits,
  getUnitWidth,
  getWidth,
} from './utils';

export const calculateTimelineRangeCases: FunctionTestCase<
  typeof calculateTimelineRange,
  ['rows', 'timeScale', 'minimumRange']
>[] = [
  {
    description:
      '1. returns the correct response from one event in one row (outside of minimum range)',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2020-12-01T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2020-12-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
  },
  {
    description:
      '2. returns the correct response from two events in one row (outside of minimum range)',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2020-12-31T00:00'),
            color: '1',
          },
          {
            id: '2',
            startDate: new Date('2022-01-01T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2020-12-01T00:00'),
      endDate: new Date('2022-01-31T00:00'),
    },
  },
  {
    description:
      '3. returns the correct response from two events in two rows (outside of minimum range)',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2020-12-31T00:00'),
            color: '1',
          },
        ],
      },
      {
        id: '2',
        title: 'Row 2',
        events: [
          {
            id: '2',
            startDate: new Date('2022-01-01T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2020-12-01T00:00'),
      endDate: new Date('2022-01-31T00:00'),
    },
  },
  {
    description:
      '4. returns the correct response from one event (with an end date) in one row (outside of minimum range)',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2020-12-31T00:00'),
            endDate: new Date('2022-01-01T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2020-12-01T00:00'),
      endDate: new Date('2022-01-31T00:00'),
    },
  },
  {
    description:
      '5. returns the correct response from two events (with end dates) in two rows (2nd outside of minimum range)',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2021-01-01T00:00'),
            endDate: new Date('2021-01-02T00:00'),
            color: '1',
          },
        ],
      },
      {
        id: '2',
        title: 'Row 2',
        events: [
          {
            id: '2',
            startDate: new Date('2021-12-31T00:00'),
            endDate: new Date('2022-01-01T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2022-01-31T00:00'),
    },
  },
  {
    description:
      '6. returns the correct response from two events (1st one with an end date) in two rows',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2021-01-01T00:00'),
            endDate: new Date('2021-01-05T00:00'),
            color: '1',
          },
        ],
      },
      {
        id: '2',
        title: 'Row 2',
        events: [
          {
            id: '2',
            startDate: new Date('2021-01-03T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
  },
  {
    description: '7. returns the correct response from two events (inside range)',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2021-02-01T00:00'),
            color: '1',
          },
          {
            id: '2',
            startDate: new Date('2021-03-01T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
  },
  {
    description: '8. returns the correct response when no events exist',
    minimumRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [],
    expected: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-12-31T00:00'),
    },
  },
  {
    description:
      '9. returns the minimumRange.endDate and event.startDate when event.startDate is before the minimumRange.startDate',
    minimumRange: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2019-01-01T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2017-12-03T00:00'),
            endDate: new Date('2018-01-02T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2017-12-01T00:00'),
      endDate: new Date('2019-01-31T00:00'),
    },
  },
  {
    description:
      '10. returns the minimumRange.startDate and event.endDate when event.endDate is after the minimumRange.endDate',
    minimumRange: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2019-01-01T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2018-01-02T00:00'),
            endDate: new Date('2019-01-02T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2019-01-31T00:00'),
    },
  },
  {
    description: '11. extends the endDate to the end of the month',
    minimumRange: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2019-01-01T00:00'),
    },
    timeScale: TimeScale.Monthly,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2018-01-02T00:00'),
            endDate: new Date('2018-01-02T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2019-01-31T00:00'),
    },
  },
  {
    description:
      '12. returns correct response with biannual time scale with the event appearing before the minimum range',
    minimumRange: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2019-01-01T00:00'),
    },
    timeScale: TimeScale.Biannual,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2017-12-01T00:00'),
            endDate: new Date('2018-06-30T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2017-07-01T00:00'),
      endDate: new Date('2019-06-30T00:00'),
    },
  },
  {
    description:
      '13. returns correct response with biannual time scale with the event appearing after the minimum range',
    minimumRange: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2018-12-31T00:00'),
    },
    timeScale: TimeScale.Biannual,
    rows: [
      {
        id: '1',
        title: 'Row 1',
        events: [
          {
            id: '1',
            startDate: new Date('2017-12-01T00:00'),
            endDate: new Date('2018-06-30T00:00'),
            color: '1',
          },
        ],
      },
    ],
    expected: {
      startDate: new Date('2017-07-01T00:00'),
      endDate: new Date('2018-12-31T00:00'),
    },
  },
  {
    description:
      '14. returns correct response with biannual time scale starting and ending on a half-year boundary',
    minimumRange: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2018-12-31T00:00'),
    },
    timeScale: TimeScale.Biannual,
    rows: [],
    expected: {
      startDate: new Date('2018-01-01T00:00'),
      endDate: new Date('2018-12-31T00:00'),
    },
  },
];

export const getSectionsCases: FunctionTestCase<
  typeof getSections,
  ['timeScale', 'timelineRange']
>[] = [
  {
    description: '1. (en) returns the correct response from a timeline range of 1 month - daily',
    timeScale: TimeScale.Daily,
    timelineRange: {
      startDate: new Date('2021-12-01T00:00'),
      endDate: new Date('2021-12-01T00:00'),
    },
    expected: [
      {
        label: "Dec '21",
        startDate: new Date('2021-12-01T00:00'),
        endDate: new Date('2021-12-31T00:00'),
      },
    ],
  },
  {
    description:
      '2. (en) returns the correct response from a timeline range of 2 months over a year boundary - daily',
    timeScale: TimeScale.Daily,
    timelineRange: {
      startDate: new Date('2021-12-01T00:00'),
      endDate: new Date('2022-02-01T00:00'),
    },
    expected: [
      {
        label: "Dec '21",
        startDate: new Date('2021-12-01T00:00'),
        endDate: new Date('2021-12-31T00:00'),
      },
      {
        label: "Jan '22",
        startDate: new Date('2022-01-01T00:00'),
        endDate: new Date('2022-01-31T00:00'),
      },
      {
        label: "Feb '22",
        startDate: new Date('2022-02-01T00:00'),
        endDate: new Date('2022-02-28T00:00'),
      },
    ],
  },
  {
    description: '3. (en) returns the correct response from a timeline range of 2 years - biannual',
    timeScale: TimeScale.Biannual,
    timelineRange: {
      startDate: new Date('2017-12-01T00:00'),
      endDate: new Date('2019-01-01T00:00'),
    },
    expected: [
      {
        label: "Jan '18",
        startDate: new Date('2018-01-01T00:00'),
        endDate: new Date('2018-06-30T00:00'),
      },
      {
        label: "Jul '18",
        startDate: new Date('2018-07-01T00:00'),
        endDate: new Date('2018-12-31T00:00'),
      },
      {
        label: "Jan '19",
        startDate: new Date('2019-01-01T00:00'),
        endDate: new Date('2019-06-30T00:00'),
      },
    ],
  },
];

export const getSectionsCasesUk: FunctionTestCase<
  typeof getSections,
  ['timeScale', 'timelineRange']
>[] = [
  {
    description: '1. (uk) returns the correct response from a timeline range of 1 month - daily',
    timeScale: TimeScale.Daily,
    timelineRange: {
      startDate: new Date('2021-12-01T00:00'),
      endDate: new Date('2021-12-01T00:00'),
    },
    expected: [
      {
        label: "груд '21",
        startDate: new Date('2021-12-01T00:00'),
        endDate: new Date('2021-12-31T00:00'),
      },
    ],
  },
  {
    description:
      '2. (uk) returns the correct response from a timeline range of 2 months over a year boundary - daily',
    timeScale: TimeScale.Daily,
    timelineRange: {
      startDate: new Date('2021-12-01T00:00'),
      endDate: new Date('2022-02-01T00:00'),
    },
    expected: [
      {
        label: "груд '21",
        startDate: new Date('2021-12-01T00:00'),
        endDate: new Date('2021-12-31T00:00'),
      },
      {
        label: "січ '22",
        startDate: new Date('2022-01-01T00:00'),
        endDate: new Date('2022-01-31T00:00'),
      },
      {
        label: "лют '22",
        startDate: new Date('2022-02-01T00:00'),
        endDate: new Date('2022-02-28T00:00'),
      },
    ],
  },
  {
    description: '3. (uk) returns the correct response from a timeline range of 2 years - biannual',
    timeScale: TimeScale.Biannual,
    timelineRange: {
      startDate: new Date('2017-12-01T00:00'),
      endDate: new Date('2019-01-01T00:00'),
    },
    expected: [
      {
        label: "січ '18",
        startDate: new Date('2018-01-01T00:00'),
        endDate: new Date('2018-06-30T00:00'),
      },
      {
        label: "лип '18",
        startDate: new Date('2018-07-01T00:00'),
        endDate: new Date('2018-12-31T00:00'),
      },
      {
        label: "січ '19",
        startDate: new Date('2019-01-01T00:00'),
        endDate: new Date('2019-06-30T00:00'),
      },
    ],
  },
];

export const getGridLinesCases: FunctionTestCase<
  typeof getGridLines,
  ['timeScale', 'timelineRange', 'weekStartsOn', 'unitWidth']
>[] = [
  {
    description: '1. returns empty array when using the daily time scale',
    timeScale: TimeScale.Daily,
    timelineRange: {
      startDate: new Date('2021-06-01T00:00'),
      endDate: new Date('2021-07-31T00:00'),
    },
    weekStartsOn: DayOfWeek.Monday,
    unitWidth: 100,
    expected: [],
  },
  {
    description: '2. returns lines for each week when using the monthly time scale',
    timeScale: TimeScale.Monthly,
    timelineRange: {
      startDate: new Date('2021-12-01T00:00'),
      endDate: new Date('2022-01-31T00:00'),
    },
    weekStartsOn: DayOfWeek.Monday,
    unitWidth: 100,
    expected: [
      {
        startDate: new Date('2021-12-06T00:00'),
      },
      {
        startDate: new Date('2021-12-13T00:00'),
      },
      {
        startDate: new Date('2021-12-20T00:00'),
      },
      {
        startDate: new Date('2021-12-27T00:00'),
      },
      {
        startDate: new Date('2022-01-03T00:00'),
      },
      {
        startDate: new Date('2022-01-10T00:00'),
      },
      {
        startDate: new Date('2022-01-17T00:00'),
      },
      {
        startDate: new Date('2022-01-24T00:00'),
      },
      {
        startDate: new Date('2022-01-31T00:00'),
      },
    ],
  },
  {
    description: '3. returns lines for each month when using the biannual time scale',
    timeScale: TimeScale.Biannual,
    timelineRange: {
      startDate: new Date('2021-12-01T00:00'),
      endDate: new Date('2022-03-31T00:00'),
    },
    weekStartsOn: DayOfWeek.Monday,
    unitWidth: 100,
    expected: [
      {
        startDate: new Date('2021-12-01T00:00'),
      },
      {
        startDate: new Date('2022-02-01T00:00'),
      },
      {
        startDate: new Date('2022-03-01T00:00'),
      },
    ],
  },
  {
    description:
      '4. returns empty array when using the monthly time scale and a unitWidth less than the minimum',
    timeScale: TimeScale.Monthly,
    timelineRange: {
      startDate: new Date('2021-12-01T00:00'),
      endDate: new Date('2022-01-31T00:00'),
    },
    weekStartsOn: DayOfWeek.Monday,
    unitWidth: monthlyGridLineMinWidth - 1,
    expected: [],
  },
];

export const getWidthCases: FunctionTestCase<
  typeof getWidth,
  ['timeScale', 'unitWidth', 'startDate', 'endDate']
>[] = [
  {
    description: '1. returns the correct response from a daily time scale with no end date',
    timeScale: TimeScale.Daily,
    unitWidth: 8,
    startDate: new Date('2021-01-01T00:00'),
    expected: 9,
  },
  {
    description: '2. returns the correct response from a daily time scale with an end date',
    timeScale: TimeScale.Daily,
    unitWidth: 8,
    startDate: new Date('2021-01-01T00:00'),
    endDate: new Date('2021-01-03T00:00'),
    expected: 25,
  },
  {
    description: '3. returns the correct response from a monthly time scale with no end date',
    timeScale: TimeScale.Monthly,
    unitWidth: 24,
    startDate: new Date('2021-01-01T00:00'),
    expected: 25,
  },
  {
    description: '4. returns the correct response from a monthly time scale with an end date',
    timeScale: TimeScale.Monthly,
    unitWidth: 24,
    startDate: new Date('2021-01-01T00:00'),
    endDate: new Date('2021-01-14T00:00'),
    expected: 49,
  },
  {
    description:
      '5. returns the correct response from a monthly time scale with an end date over month boundaries',
    timeScale: TimeScale.Monthly,
    unitWidth: 24,
    startDate: new Date('2018-01-03T00:00'),
    endDate: new Date('2018-06-07T00:00'),
    expected: 535.8571428571429,
  },
  {
    description:
      '6. returns the correct response from a biannual time scale with an end date over month boundaries',
    timeScale: TimeScale.Biannual,
    unitWidth: 24,
    startDate: new Date('2018-01-03T00:00'),
    endDate: new Date('2018-06-07T00:00'),
    expected: 535.8571428571429,
  },
];

export const getOffsetCases: FunctionTestCase<
  typeof getOffset,
  ['timeScale', 'unitWidth', 'startDate', 'timelineRange']
>[] = [
  {
    description:
      '1. returns the correct response from a daily time scale starting on the first day',
    timeScale: TimeScale.Daily,
    unitWidth: 8,
    startDate: new Date('2021-01-01T00:00'),
    timelineRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-01-03T00:00'),
    },
    expected: 0,
  },
  {
    description:
      '2. returns the correct response from a daily time scale starting on the second day',
    timeScale: TimeScale.Daily,
    unitWidth: 8,
    startDate: new Date('2021-01-02T00:00'),
    timelineRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-01-03T00:00'),
    },
    expected: 8,
  },
  {
    description: '3. returns the correct response from a daily time scale starting on the last day',
    timeScale: TimeScale.Daily,
    unitWidth: 8,
    startDate: new Date('2021-01-03T00:00'),
    timelineRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-01-03T00:00'),
    },
    expected: 16,
  },
  {
    description:
      '4. returns the correct response from a daily time scale starting on the first day, where the timeline range starts after the first day of the month',
    timeScale: TimeScale.Daily,
    unitWidth: 8,
    startDate: new Date('2021-01-02T00:00'),
    timelineRange: {
      startDate: new Date('2021-01-02T00:00'),
      endDate: new Date('2021-01-05T00:00'),
    },
    expected: 8,
  },
  {
    description:
      '5. returns the correct response from a monthly time scale starting on the first day',
    timeScale: TimeScale.Monthly,
    unitWidth: 24,
    startDate: new Date('2021-01-01T00:00'),
    timelineRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-01-14T00:00'),
    },
    expected: 0,
  },
  {
    description:
      '6. returns the correct response from a monthly time scale starting on the second day',
    timeScale: TimeScale.Monthly,
    unitWidth: 24,
    startDate: new Date('2021-01-02T00:00'),
    timelineRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-01-14T00:00'),
    },
    expected: 3.4285714285714284,
  },
  {
    description:
      '7. returns the correct response from a monthly time scale starting on the last day',
    timeScale: TimeScale.Monthly,
    unitWidth: 24,
    startDate: new Date('2021-01-14T00:00'),
    timelineRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-01-14T00:00'),
    },
    expected: 44.57142857142857,
  },
  {
    description:
      '8. returns the correct response from a monthly time scale starting on the first day, where the timeline range starts after the first day of the month',
    timeScale: TimeScale.Monthly,
    unitWidth: 24,
    startDate: new Date('2018-01-03T00:00'),
    timelineRange: {
      startDate: new Date('2018-01-03T00:00'),
      endDate: new Date('2018-10-11T00:00'),
    },
    expected: 6.857142857142857,
  },
  {
    description:
      '9. returns the correct response from a biannual time scale starting on the first day, where the timeline range starts after the first day of the month',
    timeScale: TimeScale.Biannual,
    unitWidth: 24,
    startDate: new Date('2018-01-03T00:00'),
    timelineRange: {
      startDate: new Date('2018-01-03T00:00'),
      endDate: new Date('2018-10-11T00:00'),
    },
    expected: 6.857142857142857,
  },
];

export const getUnitWidthCases: FunctionTestCase<
  typeof getUnitWidth,
  ['timeScale', 'containerWidth', 'totalUnits']
>[] = [
  {
    description:
      '1. returns minUnitWidth when monthly and containerWidth portion is less than minUnitWidth',
    timeScale: TimeScale.Monthly,
    containerWidth: 100,
    totalUnits: 10,
    expected: 12,
  },
  {
    description:
      '2. returns containerWidth / totalUnits when monthly and containerWidth portion is greater than minUnitWidth',
    timeScale: TimeScale.Monthly,
    containerWidth: 100,
    totalUnits: 5,
    expected: 20,
  },
  {
    description:
      '3. returns containerWidth / total units rounded down to lowest integer when monthly and containerWidth portion is greater than minUnitWidth',
    timeScale: TimeScale.Monthly,
    containerWidth: 101,
    totalUnits: 5,
    expected: 20.2,
  },
  {
    description:
      '4. returns minUnitWidth when daily and containerWidth portion is less than minUnitWidth',
    timeScale: TimeScale.Daily,
    containerWidth: 70,
    totalUnits: 10,
    expected: 8,
  },
  {
    description:
      '5. returns containerWidth / totalUnits rounded down to lowest integer when daily and containerWidth portion is greater than minUnitWidth',
    timeScale: TimeScale.Daily,
    containerWidth: 72,
    totalUnits: 8,
    expected: 9,
  },
  {
    description:
      '6. returns minUnitWidth when biannual and containerWidth portion is less than minUnitWidth',
    timeScale: TimeScale.Biannual,
    containerWidth: 100,
    totalUnits: 30,
    expected: 4,
  },
];

export const getTotalUnitsCases: FunctionTestCase<
  typeof getTotalUnits,
  ['timeScale', 'timelineRange']
>[] = [
  {
    description: '1. returns the correct response from a daily time scale',
    timeScale: TimeScale.Daily,
    timelineRange: {
      startDate: new Date('2021-01-01T00:00'),
      endDate: new Date('2021-01-31T00:00'),
    },
    expected: 31,
  },
  {
    description: '2. returns the correct response from a monthly time scale',
    timeScale: TimeScale.Monthly,
    timelineRange: {
      startDate: new Date('2021-02-01T00:00'),
      endDate: new Date('2021-02-28T00:00'),
    },
    expected: 4,
  },
  {
    description: '3. returns the correct response from a biannual time scale',
    timeScale: TimeScale.Biannual,
    timelineRange: {
      startDate: new Date('2021-02-01T00:00'),
      endDate: new Date('2021-02-28T00:00'),
    },
    expected: 4,
  },
];
