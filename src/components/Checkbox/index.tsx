import {isStateColor} from 'src/utils/typeGuards';
import {
  Checkbox,
  checkboxClasses,
  type CheckboxProps,
  type Components,
  type Theme,
} from '@mui/material';

const UncheckedSVG = (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.5" y="0.5" width="23" height="23" rx="3.5" />
  </svg>
);

const IndeterminateSVG = (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.5" y="0.5" width="23" height="23" rx="3.5" />
    <g clipPath="url(#clip0_886_16447)">
      <path d="M6 12H18" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_886_16447">
        <rect width="24" height="24" rx="4" />
      </clipPath>
    </defs>
  </svg>
);

const CheckedSVG = (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.5" y="0.5" width="23" height="23" rx="3.5" />
    <g clipPath="url(#clip0_868_16086)">
      <path d="M5 13L9 17L19 7" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_868_16086">
        <rect width="24" height="24" rx="4" />
      </clipPath>
    </defs>
  </svg>
);

// Note: This component has augmented props. See src/muiTheme.d.ts for prop overrides
export const CheckboxOverrides: Components<Theme>['MuiCheckbox'] = {
  defaultProps: {
    color: 'info',
    disableRipple: true,
    disableTouchRipple: true,
    disableFocusRipple: true,
    icon: UncheckedSVG,
    checkedIcon: CheckedSVG,
    indeterminateIcon: IndeterminateSVG,
  },
  styleOverrides: {
    root: ({theme, ownerState: {color = 'info'}}) => ({
      fill: 'none',
      color: theme.palette.semanticPalette.surface.main,
      stroke:
        isStateColor(color) && color !== 'info'
          ? theme.palette.semanticPalette.stroke[color]
          : theme.palette.semanticPalette.stroke.main,
      [`& > svg path`]: {
        stroke: theme.palette.common.black,
      },
      [`&.${checkboxClasses.checked}, &.${checkboxClasses.indeterminate}`]: {
        color: isStateColor(color)
          ? theme.palette.semanticPalette.surface[color]
          : theme.palette.semanticPalette.surface.brand,
      },
      [`&.${checkboxClasses.disabled}`]: {
        color: theme.palette.semanticPalette.surface.secondary,
        stroke: theme.palette.semanticPalette.stroke.main,
        [`& > svg path`]: {
          stroke: theme.palette.grey[400],
        },
      },
      [`&.${checkboxClasses.root}:has(> svg[font-size="medium"])`]: {
        padding: `0 ${theme.spacing(2)}`,
        [`> svg`]: {
          height: 20,
          width: 20,
        },
      },
      [`&.${checkboxClasses.root}:has(> svg[font-size="small"])`]: {
        padding: `0 ${theme.spacing(2)}`,
        [`> svg`]: {
          height: 16,
          width: 16,
        },
      },
    }),
  },
};

export {Checkbox};
export type {CheckboxProps};
