import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import {Checkbox, FormControlLabel, FormGroup, Stack} from 'src/index';
import {controlsExclude} from 'src/storybook-utils/argTypes';

import argTypes from './argTypes';

const meta: Meta<typeof Checkbox> = {
  component: Checkbox,
  argTypes,
  title: 'components/Inputs/Checkbox',
  parameters: {
    controls: {
      exclude: controlsExclude,
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=1808%3A17831',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

export const Basic: Story = {
  args: {checked: false},
  render: args => <Checkbox {...args} value="1" />,
};

export const States: Story = {
  render: args => (
    <FormGroup>
      <Stack gap={2}>
        <FormControlLabel label="Checked" control={<Checkbox checked {...args} />} />
        <FormControlLabel label="Indeterminate" control={<Checkbox indeterminate {...args} />} />
        <FormControlLabel label="Unselected" control={<Checkbox {...args} />} />
        <FormControlLabel label="Default Checked" control={<Checkbox defaultChecked {...args} />} />
        <FormControlLabel
          label="Disabled Default Checked"
          control={<Checkbox disabled defaultChecked {...args} />}
        />
      </Stack>
    </FormGroup>
  ),
};

export const Colors: Story = {
  render: args => (
    <>
      <Stack gap={4}>
        <FormGroup>
          <Stack gap={2}>
            <FormControlLabel
              label="primary (checked)"
              control={<Checkbox checked color="info" {...args} />}
            />
            <FormControlLabel
              label="primary (indeterminate)"
              control={<Checkbox indeterminate color="info" {...args} />}
            />
            <FormControlLabel label="primary" control={<Checkbox color="info" {...args} />} />
            <FormControlLabel
              label="primary (disabled checked)"
              control={<Checkbox disabled defaultChecked color="info" {...args} />}
            />
            <FormControlLabel
              label="primary (indeterminate)"
              control={<Checkbox disabled indeterminate color="info" {...args} />}
            />
            <FormControlLabel
              label="primary (disabled)"
              control={<Checkbox disabled color="info" {...args} />}
            />
          </Stack>
        </FormGroup>
        <FormGroup>
          <Stack gap={2}>
            <FormControlLabel
              label="error"
              control={<Checkbox checked color="error" {...args} />}
            />
            <FormControlLabel label="error" control={<Checkbox color="error" {...args} />} />
            <FormControlLabel
              label="error"
              control={<Checkbox disabled defaultChecked color="error" {...args} />}
            />
          </Stack>
        </FormGroup>
      </Stack>
    </>
  ),
};

export const Sizes: Story = {
  render: args => (
    <FormGroup>
      <Stack gap={2}>
        <FormControlLabel
          label="medium (default)"
          control={<Checkbox checked size="medium" {...args} />}
        />
        <FormControlLabel label="small" control={<Checkbox checked size="small" {...args} />} />
      </Stack>
    </FormGroup>
  ),
};
