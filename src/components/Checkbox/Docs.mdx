import { <PERSON>a, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as CheckboxStories from './index.stories.tsx'

<Meta of={CheckboxStories} />

 ## Overview
`Checkboxes` should be composed with `FormControlLabel`.

[MUI Checkbox](https://mui.com/material-ui/react-button/)

<Canvas of={CheckboxStories.Basic} />
<Controls of={CheckboxStories.Basic}/>

## States
<Canvas of={CheckboxStories.States} />

## Colors
Note, default and primary result in the same colors.
<Canvas of={CheckboxStories.Colors} />

## Sizes
<Canvas of={CheckboxStories.Sizes} />