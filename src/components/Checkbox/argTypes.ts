import {FORM_COMPONENT_ARGTYPES, getArgTypes, SYSTEM_ARGTYPES} from 'src/storybook-utils/argTypes';

const checkboxArgTypeKeys = [
  'checked',
  'classes',
  'color',
  'defaultChecked',
  'disabled',
  'indeterminate',
  'edge',
  'id',
  'inputProps',
  'inputRef',
  'onChange',
  'required',
  'size',
  'sx',
  'value',
];

const CHECKBOX_ARGTYPES = {
  color: {
    table: {
      type: {
        summary: 'enum',
      },
      defaultValue: {
        summary: 'primary',
      },
    },
    control: 'inline-radio',
    options: ['default', 'primary', 'error'],
    description: 'The color of the component.',
  },
  size: {
    table: {
      type: {
        summary: 'enum',
      },
      defaultValue: {
        summary: 'medium',
      },
    },
    control: 'inline-radio',
    options: ['medium', 'small'],
    description: 'The size of the component.',
  },
  onChange: {
    ...FORM_COMPONENT_ARGTYPES['onChange'],
    description:
      'Callback fired when the state is changed. <br /> <code>function(event: React.ChangeEvent) => void</code><br /><code>event</code> The event source of the callback. You can pull out the new checked state by accessing <br /><code>event.target.checked</code>',
  },
};

const argTypes = getArgTypes(checkboxArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...CHECKBOX_ARGTYPES,
});

export default argTypes;
