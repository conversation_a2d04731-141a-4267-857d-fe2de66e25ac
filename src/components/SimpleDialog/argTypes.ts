import type {ArgTypes} from '@storybook/react';
import {
  ACCESSIBILITY_ARGTYPES,
  ANIMATION_ARGTYPES,
  FORM_COMPONENT_ARGTYPES,
  getArgTypes,
  SYSTEM_ARGTYPES,
} from 'src/storybook-utils/argTypes';
import {getTypedKeys} from 'src/utils/object';

const SIMPLE_DIALOG_ARGTYPES: ArgTypes = {
  title: {
    description: "This will be the dialog's title",
    control: 'text',
  },
  description: {
    description: "This will be the dialog's description",
    control: 'text',
  },
  maxWidth: {
    description:
      'Dialog max width configurable to `xs, sm, md, lg, xl` mapped to theme fixedWidths',
    table: {
      defaultValue: {summary: 'sm'},
    },
    control: 'radio',
    options: ['xs', 'sm', 'md', 'lg', 'xl'],
  },
  open: {
    description: 'If true, the dialog is open',
    control: 'boolean',
    type: {
      required: true,
      name: 'boolean',
    },
  },
  disableBackdropClick: {
    description: "Disable dialog's close on backdrop click",
    control: 'boolean',
  },
  disableEscapeKeyDown: {
    description: "Disable dialog's close on escape key down",
    control: 'boolean',
  },
  fullScreen: {
    description: 'If true, the dialog will be full-screen',
    control: 'boolean',
    table: {
      defaultValue: {summary: false},
    },
  },
  fullWidth: {
    description:
      'If true, the dialog stretches to maxWidth. Notice that the dialog width grow is limited by the default margin.',
    control: 'boolean',
    table: {
      defaultValue: {summary: true},
    },
  },
  hideCloseIcon: {
    description: "Hide dialog's close icon",
    control: 'boolean',
  },
  PaperComponent: {
    description: 'The component to use as the container component',
    control: 'object',
    table: {
      category: 'Nested Components',
    },
  },
  PaperProps: {
    description: 'Props applied to the Paper component',
    control: 'object',
    table: {
      category: 'Nested Components',
    },
  },
  scroll: {
    description: "Dialog's scroll type, can be 'paper' or 'body'",
    table: {
      defaultValue: {summary: 'paper'},
    },
    control: 'radio',
    options: ['paper', 'body'],
  },
};

const dialogArgTypeKeys = [
  ...getTypedKeys(SIMPLE_DIALOG_ARGTYPES),
  'aria-describedby',
  'aria-labelledby',
  'children',
  'classes',
  'sx',
  'components',
  'componentsProps',
  'slots',
  'slotProps',
  'TransitionComponent',
  'TransitionProps',
  'transitionDuration',
];

const argTypes = getArgTypes(dialogArgTypeKeys, {
  ...ACCESSIBILITY_ARGTYPES,
  ...SYSTEM_ARGTYPES,
  ...ANIMATION_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...SIMPLE_DIALOG_ARGTYPES,
});

export default argTypes;
