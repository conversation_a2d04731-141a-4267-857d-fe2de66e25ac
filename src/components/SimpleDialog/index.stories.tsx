import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import React from 'react';
import type {SelectChangeEvent} from 'src/index';
import {
  Box,
  Button,
  Checkbox,
  DialogActions,
  DialogContent,
  FormControlLabel,
  SelectField,
  SimpleDialog,
  Stack,
  TextField,
  Typography,
  type SimpleDialogProps,
} from 'src/index';

import argTypes from './argTypes';

type Story = StoryObj<typeof SimpleDialog>;

const meta: Meta<typeof SimpleDialog> = {
  component: SimpleDialog,
  title: 'components/Feedback/SimpleDialog',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?node-id=6180%3A21125',
    },
    layout: 'centered',
    controls: {
      exclude: ['BackdropComponent', 'BackdropProps', 'ref'],
    },
  },
  argTypes,
};

export default meta;

export const Basic: Story = {
  args: {
    title: 'Basic dialog',
    description:
      'Dialog description can be optional and works for you to tell everybody what your dialog is for',
  },
  render: args => (
    <OpenableDialog {...args}>
      <DialogContent>
        <Typography>
          This is typography content "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
          do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
          quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis
          aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
          pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
          deserunt mollit anim id est laborum."
        </Typography>
      </DialogContent>
    </OpenableDialog>
  ),
};

/**
 * Wrapper for stories so they can display a button to open the dialog
 * Named as SimpleDialog so it renders like this in stories and is not misleading
 */
const OpenableDialog = (props: SimpleDialogProps): JSX.Element => {
  const [open, setOpen] = React.useState(false);
  return (
    <Box>
      <Button onClick={() => setOpen(true)}>Open dialog</Button>
      <SimpleDialog {...props} open={open} onClose={() => setOpen(false)} />
    </Box>
  );
};

OpenableDialog.displayName = 'SimpleDialog';

const SelectFieldWrapper = (): JSX.Element => {
  const [value, setValue] = React.useState<number>(0);
  const handleChange = (e: SelectChangeEvent<number>) => {
    setValue(e.target.value);
  };
  const options = [
    {
      label: '2021',
      value: 2021,
    },
    {
      label: '2022',
      value: 2022,
    },
    {
      label: '2023',
      value: 2023,
    },
  ];
  return (
    <SelectField label="Year" required value={value} options={options} onChange={handleChange} />
  );
};

export const WithActions: Story = {
  args: {
    title: 'Dialog with actions',
  },
  render: args => (
    <OpenableDialog {...args}>
      <DialogContent>
        <Stack spacing={3}>
          <TextField label="Name" />
          <TextField label="Last name" />
          <SelectFieldWrapper />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" color="secondary">
          Cancel
        </Button>
        <Button>Save</Button>
      </DialogActions>
    </OpenableDialog>
  ),
};

export const LongContent: Story = {
  args: {
    title: 'Scrollable dialog with content dividers',
  },
  render: args => (
    <OpenableDialog {...args}>
      <DialogContent dividers={args.scroll === 'paper'}>
        <Typography variant="h5">You can play with scroll type in storybook controls</Typography>
        <Typography>
          This is typography content "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
          do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
          quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis
          aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
          pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
          deserunt mollit anim id est laborum." This is typography content "Lorem ipsum dolor sit
          amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
          magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
          aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit
          esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non
          proident, sunt in culpa qui officia deserunt mollit anim id est laborum."This is
          typography content "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis
          nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
          irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
          pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
          deserunt mollit anim id est laborum." This is typography content "Lorem ipsum dolor sit
          amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
          magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
          aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit
          esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non
          proident, sunt in culpa qui officia deserunt mollit anim id est laborum." This is
          typography content "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis
          nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
          irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
          pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
          deserunt mollit anim id est laborum."This is typography content "Lorem ipsum dolor sit
          amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore
          magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut
          aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit
          esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non
          proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" color="secondary">
          Cancel
        </Button>
        <Button>Save</Button>
      </DialogActions>
    </OpenableDialog>
  ),
};

export const Sizes: Story = {
  args: {
    title: 'Dialog sizes',
    description: 'Select dialog size from the dropdown below',
  },
  parameters: {
    controls: {
      exclude: ['maxWidth'],
    },
  },

  render: args =>
    ((): JSX.Element => {
      const [width, setWidth] = React.useState<SimpleDialogProps['maxWidth']>('xs');

      return (
        <OpenableDialog {...args} maxWidth={width}>
          <DialogContent>
            <Stack direction="row" justifyContent="center" alignContent="center">
              <SelectField
                onChange={e => setWidth(e.target.value)}
                value={width}
                label="Dialog size"
                required
                options={[
                  {
                    label: 'Extra small',
                    value: 'xs',
                  },
                  {
                    label: 'Small',
                    value: 'sm',
                  },
                  {
                    label: 'Medium',
                    value: 'md',
                  },
                  {
                    label: 'Large',
                    value: 'lg',
                  },
                  {
                    label: 'Extra large',
                    value: 'xl',
                  },
                ]}
              />
            </Stack>
          </DialogContent>
        </OpenableDialog>
      );
    })(),
};

export const ClosingOptions: Story = {
  args: {
    title: 'Closing options',
    description: 'You can play with closing options in storybook controls',
    disableBackdropClick: false,
    hideCloseIcon: false,
    disableEscapeKeyDown: false,
  },
  render: args =>
    ((): JSX.Element => {
      const [closingOptions, setClosingOptions] = React.useState<
        Pick<SimpleDialogProps, 'disableBackdropClick' | 'disableEscapeKeyDown' | 'hideCloseIcon'>
      >({
        disableBackdropClick: args.disableBackdropClick,
        hideCloseIcon: args.hideCloseIcon,
        disableEscapeKeyDown: args.disableEscapeKeyDown,
      });
      return (
        <OpenableDialog {...args} {...closingOptions}>
          <DialogContent>
            <Stack direction="column" spacing={2}>
              <FormControlLabel
                control={
                  <Checkbox
                    onChange={e =>
                      setClosingOptions(prev => ({...prev, disableBackdropClick: e.target.checked}))
                    }
                    value={closingOptions.disableBackdropClick}
                  />
                }
                label="Disable backdrop click"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    onChange={e =>
                      setClosingOptions(prev => ({...prev, hideCloseIcon: e.target.checked}))
                    }
                    value={closingOptions.hideCloseIcon}
                  />
                }
                label="Hide close icon"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    onChange={e =>
                      setClosingOptions(prev => ({...prev, disableEscapeKeyDown: e.target.checked}))
                    }
                    value={closingOptions.disableEscapeKeyDown}
                  />
                }
                label="Disable escape key down"
              />
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button variant="outlined" color="secondary">
              Cancel
            </Button>
            <Button>Continue</Button>
          </DialogActions>
        </OpenableDialog>
      );
    })(),
};
