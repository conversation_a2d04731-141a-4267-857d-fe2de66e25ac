import {
  backdropClasses,
  DialogContentText,
  DialogTitle,
  Dialog as MuiDialog,
  DialogActions as MuiDialogActions,
  DialogContent as MuiDialogContent,
  type Components,
  type DialogContentProps,
  type DialogActionsProps as MuiDialogActionProps,
  type DialogProps as MuiDialogProps,
  type Theme,
} from '@mui/material';

import {Box} from 'src/components/Box';
import {IconButton} from 'src/components/Button';
import {SvgIcon} from 'src/components/Icon';
import {Stack} from 'src/components/Stack';

export const DialogOverrides: Components<Theme>['MuiDialog'] = {
  defaultProps: {
    fullWidth: true,
    maxWidth: 'sm',
    scroll: 'paper',
  },
  styleOverrides: {
    paper: ({theme, ownerState: {maxWidth}}) => ({
      // eslint-disable-next-line no-restricted-properties
      boxShadow: theme.shadows[theme.boxShadows.lg],
      paddingBottom: theme.spacing(5), // Bottom padding is added for no actions usage
      maxWidth:
        maxWidth !== false && maxWidth !== undefined ? theme.fixedWidths[maxWidth] : undefined, //Overrides the maxWidth to use our fixedWidths
    }),
    root: ({theme}) => ({
      [`& >.${backdropClasses.root}`]: {
        backgroundColor: `${theme.palette.semanticPalette.surfaceInverted.main}80`, //80 is 50% opacity
      },
    }),
  },
};

export const DialogTitleOverrides: Components<Theme>['MuiDialogTitle'] = {
  defaultProps: {
    variant: 'h3',
  },
  styleOverrides: {
    root: ({theme}) => ({
      padding: theme.spacing(5, 14, 5, 5),
    }),
  },
};

export const DialogContentOverrides: Components<Theme>['MuiDialogContent'] = {
  styleOverrides: {
    root: ({theme, ownerState}) => ({
      padding: theme.spacing(ownerState.dividers ? 5 : 0, 5), //If dividers are on we want our content to have top and bottom padding
    }),
  },
};

export const DialogActionsOverrides: Components<Theme>['MuiDialogActions'] = {
  styleOverrides: {
    spacing: ({theme}) => ({
      padding: theme.spacing(5, 5, 0), // Bottom padding is removed for no actions usage
      '& > :not(:first-of-type)': {
        marginLeft: theme.spacing(3),
      },
    }),
  },
};

type DialogCloseReason = 'backdropClick' | 'escapeKeyDown' | 'iconClick';
type DialogActionsProps = Omit<MuiDialogActionProps, 'disableSpacing'>;

type SimpleDialogProps = Omit<MuiDialogProps, 'onClose'> & {
  title: string;
  description?: string | React.ReactNode;
  onClose: (event: {}, reason: DialogCloseReason) => void;
  hideCloseIcon?: boolean;
  disableBackdropClick?: boolean;
};

const SimpleDialog = ({
  title,
  disableBackdropClick,
  onClose,
  description,
  hideCloseIcon,
  children,
  ...otherProps
}: SimpleDialogProps) => (
  <MuiDialog
    {...otherProps}
    onClose={(e, reason) => {
      if (disableBackdropClick && reason === 'backdropClick') {
        return; // Prevents triggering onClick event if backdropClick is disabled
      }
      onClose?.(e, reason);
    }}
  >
    <DialogTitle>
      <Stack gap={1}>
        {title}
        {description && <DialogContentText>{description}</DialogContentText>}
      </Stack>
    </DialogTitle>
    {!hideCloseIcon && (
      <Box position="absolute" right={18} top={18}>
        <IconButton color="primary" onClick={e => onClose?.(e, 'iconClick')}>
          <SvgIcon fontSize="small" type="cross" />
        </IconButton>
      </Box>
    )}
    {children}
  </MuiDialog>
);

const DialogContent = (props: DialogContentProps) => <MuiDialogContent {...props} />;
const DialogActions = (props: DialogActionsProps) => <MuiDialogActions {...props} />;

SimpleDialog.displayName = 'SimpleDialog';
DialogContent.displayName = 'DialogContent';
DialogActions.displayName = 'DialogActions';

export {
  SimpleDialog,
  DialogContent,
  DialogActions,
  type SimpleDialogProps,
  type DialogContentProps,
  type DialogActionsProps,
  type DialogCloseReason,
};
