import { <PERSON>a, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import { SimpleDialog} from './index';
import * as SimpleDialogStories from './index.stories';

<Meta of={SimpleDialogStories}/>

## Overview
`SimpleDialog` is an abstraction of MUI's `Dialog` component creating a simplified API for the most common use cases for a dialog. SimpleDialog exposes props for `title`, `description`, `hideCloseIcon` and `disableBackdropClick` in addition to MUI [Dialog props](https://mui.com/material-ui/api/dialog/) and MUI [Modal props](https://mui.com/material-ui/api/modal/).

To use this component, you'll need to compose `SimpleDialog` along with M<PERSON> [DialogContent](https://mui.com/material-ui/api/dialog-content/) and optionally M<PERSON> [DialogActions](https://mui.com/material-ui/api/dialog-actions/) as children.

<Canvas of={SimpleDialogStories.Basic} />
<Controls of={SimpleDialogStories.Basic}/>

## With actions
You can add the actions to a dialog using the tag `DialogActions` and passing the buttons as children.
<Canvas of={SimpleDialogStories.WithActions} />

## Long content
Long content can be shown in two different ways: scrollable or not scrollable. 
By default the content is scrollable but you can disable it using the prop `scroll="body"` of the `Dialog` component. 

Additionally, you can also add dividers between the content and the actions using the prop `dividers` of the `DialogContent` component. 
In this example we're automatically rendering the dividers when the `scroll="body` is enabled.

You can play with it using the controls.
<Canvas of={SimpleDialogStories.LongContent} />

## Sizes
You can change the size of the dialog using the prop `maxWidth` of the `Dialog` component. 
The available sizes are: `xs`, `sm`, `md`, `lg`, `xl`.
<Canvas of={SimpleDialogStories.Sizes}/>

## Closing options
By default, Dialog can be closed in multiple ways like clicking outside the dialog, 
pressing the escape key or clicking the close button. Alternatively, you can disable some of
these options using any of the props `disableBackdropClick`, `disableEscapeKeyDown` and `hideCloseButton`.
<Canvas of={SimpleDialogStories.ClosingOptions}/>



