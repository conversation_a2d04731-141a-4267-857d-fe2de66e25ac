import type {Components, Theme} from '@mui/material';
import {buttonBaseClasses} from '@mui/material';

export const ButtonBaseOverrides: Components<Theme>['MuiButtonBase'] = {
  defaultProps: {
    disableRipple: true,
    disableTouchRipple: true,
  },
  styleOverrides: {
    root: ({theme}) => ({
      [`&.${buttonBaseClasses.root}`]: {
        textTransform: 'none',
        lineHeight: '20px',
        minWidth: theme.spacing(9),
        padding: theme.spacing(2, 3),
        '&:has(> svg)': {
          padding: theme.spacing(2, 2),
          minWidth: 'initial',
        },
        '& svg': {
          fill: 'currentColor',
        },
      },
    }),
  },
};
