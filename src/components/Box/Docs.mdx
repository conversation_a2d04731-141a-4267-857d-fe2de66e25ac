import { Meta, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as BoxStories from './index.stories.tsx'

<Meta of={BoxStories}/>

 ## Overview
The Box component serves as a wrapper component for most of the CSS utility needs.
Boxes are useful for many reasons such as adding borders, backgrounds and spacing properties.
The Box component packages [all the style functions](https://mui.com/system/properties/) that are exposed in @mui/system.

You can learn more here: [https://mui.com/material-ui/react-box/](https://mui.com/material-ui/react-box/)

<Canvas of={BoxStories.Basic} />
<Controls of={BoxStories.Basic}/>


## Common Use Cases
<Canvas of={BoxStories.LayoutExample} />

The following demonstrates the usage of theme tokens to customize the Box component using BoxProps
<Canvas of={BoxStories.UsingThemeTokens} />
