import {forwardRef, type ForwardedRef} from 'react';
import {Box as MUIBox, type BoxProps as MUIBoxProps} from '@mui/material';

type BoxProps<C extends React.ElementType> = MUIBoxProps & {
  component?: C;
};

const BoxComponent = <C extends React.ElementType>(
  props: BoxProps<C>,
  ref: ForwardedRef<HTMLDivElement>
) => <MUIBox ref={ref} {...props} />;

export const Box = forwardRef(BoxComponent);

Box.displayName = 'Box';

export type {BoxProps};
