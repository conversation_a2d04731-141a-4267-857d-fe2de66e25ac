import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import {Box, Button, Stack, Typography, useTheme} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';
import {SIZE_KEYS} from 'src/tokens/constants';

import {ClickToCopy} from 'src/storybook-utils/story-components';

type Story = StoryObj<typeof Box>;

const boxArgTypeCategories = categorizeArgTypes(['component', 'ref'], 'MUI System Props');
export default {
  argTypes: {
    ...boxArgTypeCategories,
  },
  component: Box,
  title: 'components/Layout/Box',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16749',
    },
  },
} as Meta<ComponentProps<typeof Box>>;

export const Basic: Story = {
  args: {
    mt: 2,
  },
  render: props => (
    <Box {...props}>
      <Typography variant="h1">Hello World!</Typography>
    </Box>
  ),
};

export const LayoutExample: Story = {
  render: props => {
    return (
      <Box {...props} display="flex">
        <Box {...props} flexGrow={1} bgcolor="semanticPalette.surface.secondary" p={3}>
          <Typography variant="h2">Welcome to ReGrow!</Typography>
        </Box>
        <Box
          {...props}
          flexGrow={1}
          bgcolor="semanticPalette.surface.secondary"
          p={3}
          justifyContent="flex-end"
          display="flex"
        >
          <Button color="secondary">Read more</Button>
        </Box>
      </Box>
    );
  },
};

export const UsingThemeTokens: Story = {
  render: () => {
    return <SampleBox />;
  },
};

const SampleBox = () => {
  const theme = useTheme();
  const borderRadii = ['sm', 'md', 'pill'] as const;
  const boxShadows = ['sm', 'md', 'lg'] as const;
  const colors = ['brand', 'main', 'success', 'error', 'warning', 'info'];
  return (
    <Stack spacing={8}>
      {SIZE_KEYS.map(size => (
        <Box key={size} bgcolor="grey.200" width={theme.fixedWidths[size]} p={4}>
          <ClickToCopy>{`<Box width={theme.fixedWidths.${size}}/>`}</ClickToCopy>
        </Box>
      ))}
      {borderRadii.map(radii => (
        <Box
          key={radii}
          border={1}
          borderColor="semanticPalette.stroke.main"
          width={theme.fixedWidths.lg}
          borderRadius={theme.borderRadii[radii]}
          p={4}
        >
          <ClickToCopy>{`<Box borderRadius={theme.borderRadii.${radii}} borderColor="semanticPalette.stroke.main"/>`}</ClickToCopy>
        </Box>
      ))}
      {boxShadows.map(boxShadow => (
        <Box
          key={boxShadow}
          width={theme.fixedWidths.lg}
          boxShadow={theme.boxShadows[boxShadow]}
          p={4}
        >
          <ClickToCopy>{`<Box boxShadow={theme.boxShadows.${boxShadow}}/>`}</ClickToCopy>
        </Box>
      ))}
      {colors.map(color => (
        <Box
          key={color}
          width={theme.fixedWidths.lg}
          bgcolor={`semanticPalette.highlight.${color}`}
          p={4}
        >
          <ClickToCopy>{`<Box bgcolor="semanticPalette.highlight.${color}"/>`}</ClickToCopy>
        </Box>
      ))}
    </Stack>
  );
};
