import type {ReactElement, MouseEvent as ReactMouseEvent, ReactNode} from 'react';
import React, {useCallback, useEffect, useLayoutEffect, useRef, useState} from 'react';
import {isReactElement} from 'src/utils/typeGuards';
import type {BoxProps} from '@mui/system';

import {Box} from '../Box';

export enum ArrowKey {
  Left = 'ArrowLeft',
  Right = 'ArrowRight',
}

type ResizableFlexContainerProps = {
  children: ReactNode[];
  onResizeEnd?: (widths: number[]) => void;
  resizePanelLabel?: string;
} & BoxProps;

const MIN_PERCENT = 5;
const HANDLE_WIDTH_PX = 2;

const defaultChildState = {
  width: undefined,
  minWidth: undefined,
  maxWidth: undefined,
};

function setUserSelect(enabled: boolean) {
  document.body.style.userSelect = enabled ? '' : 'none';
}

function normalizeWidths(widths: number[]): number[] {
  const total = widths.reduce((a, b) => a + b, 0);
  return widths.map(w => (w / total) * 100);
}

function ResizableFlexContainer({
  children,
  onResizeEnd,
  resizePanelLabel,
  ...boxProps
}: ResizableFlexContainerProps) {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [widthPercents, setWidthPercents] = useState<number[]>([]);
  const isDragging = useRef(false);
  const activeHandleIndex = useRef<number | null>(null);
  const startClientX = useRef(0);
  const initialWidths = useRef<number[]>([]);
  const containerPixelWidth = useRef(0);
  const activeHandleRef = useRef<HTMLDivElement | null>(null);
  const liveRegionRef = useRef<HTMLDivElement | null>(null);
  const widthPercentsRef = useRef(widthPercents);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const {ref, ...rest} = boxProps;

  useEffect(() => {
    widthPercentsRef.current = widthPercents;
  }, [widthPercents]);

  useLayoutEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const containerWidth = container.offsetWidth;

    const pixelWidths = children.map(child =>
      isReactElement(child) && typeof child.props.width === 'number' ? child.props.width : null
    );

    const totalFixed = pixelWidths.reduce((sum, w) => sum + (w ?? 0), 0);
    const flexibleCount = pixelWidths.filter(w => w === null).length;
    const remainingPercent = ((containerWidth - totalFixed) / containerWidth) * 100;

    let initial = pixelWidths.map(w =>
      w !== null ? (w / containerWidth) * 100 : remainingPercent / flexibleCount
    );

    // Normalize to exactly 100%
    initial = normalizeWidths(initial);

    setWidthPercents(initial);
  }, [children]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging.current || activeHandleIndex.current === null) return;

    const deltaX = e.clientX - startClientX.current;
    const deltaPercent = (deltaX / containerPixelWidth.current) * 100;

    const i = activeHandleIndex.current;
    const updated = [...initialWidths.current];

    let left = initialWidths.current[i] + deltaPercent;
    let right = initialWidths.current[i + 1] - deltaPercent;

    if (left < MIN_PERCENT) {
      right -= MIN_PERCENT - left;
      left = MIN_PERCENT;
    } else if (right < MIN_PERCENT) {
      left -= MIN_PERCENT - right;
      right = MIN_PERCENT;
    }

    if (left >= MIN_PERCENT && right >= MIN_PERCENT) {
      updated[i] = left;
      updated[i + 1] = right;
      const normalized = normalizeWidths(updated);
      setWidthPercents(normalized);
    }
  }, []);

  const stopDragging = useCallback(() => {
    isDragging.current = false;
    activeHandleIndex.current = null;
    setUserSelect(true);

    const widthPercentsToPx = widthPercentsRef.current.map(
      w => (w / 100) * containerPixelWidth.current
    );

    onResizeEnd?.(widthPercentsToPx);

    if (activeHandleRef.current) {
      activeHandleRef.current.blur();
      activeHandleRef.current = null;
    }

    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', stopDragging);
  }, [handleMouseMove, onResizeEnd]);

  const startDragging = (index: number) => (e: ReactMouseEvent) => {
    isDragging.current = true;
    activeHandleIndex.current = index;
    startClientX.current = e.clientX;
    initialWidths.current = [...widthPercents];
    containerPixelWidth.current = containerRef.current?.offsetWidth ?? 1;
    setUserSelect(false);

    activeHandleRef.current = e.currentTarget as HTMLDivElement;

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', stopDragging);
  };

  useEffect(() => {
    return () => {
      setUserSelect(true);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', stopDragging);
    };
  }, [handleMouseMove, stopDragging]);

  return (
    <>
      <Box ref={containerRef} display="flex" width="100%" height="100%" {...rest}>
        {widthPercents.length > 0 &&
          children.map((child, i) => {
            const cloned = React.cloneElement(child as ReactElement, defaultChildState);
            const isElement = isReactElement(child);
            const minWidth = isElement ? child.props.minWidth || 0 : 0;
            const maxWidth = isElement && child.props.maxWidth ? child.props.maxWidth : 'none';

            return (
              <React.Fragment key={`resizeable-box-child-${i}`}>
                <Box
                  display="flex"
                  flexDirection="column"
                  sx={{
                    width: `${widthPercents[i]}%`,
                    minWidth,
                    maxWidth,
                    flexShrink: 0,
                  }}
                >
                  {cloned}
                </Box>

                {i < children.length - 1 && (
                  <Box
                    ref={activeHandleRef}
                    onMouseDown={startDragging(i)}
                    onKeyDown={e => {
                      if (!Object.values(ArrowKey).includes(e.key as ArrowKey)) return;

                      const direction = e.key === ArrowKey.Left ? -1 : 1;
                      const delta = 0.5 * direction;

                      setWidthPercents(prev => {
                        const next = [...prev];
                        const left = Math.max(prev[i] + delta, MIN_PERCENT);
                        const right = Math.max(prev[i + 1] - delta, MIN_PERCENT);

                        if (left < MIN_PERCENT || right < MIN_PERCENT) return prev;

                        next[i] = parseFloat(left.toFixed(2));
                        next[i + 1] = parseFloat(right.toFixed(2));

                        const normalized = normalizeWidths(next);

                        if (liveRegionRef.current) {
                          liveRegionRef.current.textContent = `Panel ${i + 1} width: ${left.toFixed(
                            1
                          )}%, Panel ${i + 2} width: ${right.toFixed(1)}%`;
                        }

                        return normalized;
                      });

                      e.preventDefault();
                    }}
                    role="separator"
                    aria-orientation="horizontal"
                    aria-label={resizePanelLabel}
                    aria-valuemin={MIN_PERCENT}
                    aria-valuemax={100 - MIN_PERCENT}
                    tabIndex={0}
                    padding={2}
                    sx={{
                      cursor: 'ew-resize',
                      bgcolor: 'transparent',
                      outline: 'none',
                      ':hover > *': {
                        bgcolor: theme => theme.palette.semanticPalette.highlight.main,
                      },
                      ':focus > *': {
                        bgcolor: theme => theme.palette.grey[400],
                      },
                      zIndex: 10,
                    }}
                  >
                    <Box
                      sx={{
                        height: '100%',
                        width: `${HANDLE_WIDTH_PX}px`,
                      }}
                    />
                  </Box>
                )}
              </React.Fragment>
            );
          })}
      </Box>

      <Box
        ref={liveRegionRef}
        role="status"
        aria-live="polite"
        aria-atomic="true"
        sx={{
          position: 'absolute',
          left: -9999,
          height: 0,
          width: 0,
          overflow: 'hidden',
        }}
      />
    </>
  );
}

export {ResizableFlexContainer, type ResizableFlexContainerProps};
