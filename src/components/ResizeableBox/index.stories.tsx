import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import type {ComponentProps} from 'react';
import {Box, ResizableFlexContainer, Typography} from 'src/index';
import {categorizeArgTypes} from 'src/storybook-utils/storybook';

type Story = StoryObj<typeof ResizableFlexContainer>;

const boxArgTypeCategories = categorizeArgTypes(['component', 'ref'], 'MUI System Props');
export default {
  argTypes: {
    onResizeEnd: {
      control: false,
      description:
        'Callback function that is called when the resizing ends. Returns the widths of the children in pixels.',
    },
    ...boxArgTypeCategories,
  },
  component: ResizableFlexContainer,
  title: 'components/Layout/ResizeableBox',
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?node-id=1778%3A16749',
    },
  },
} as Meta<ComponentProps<typeof ResizableFlexContainer>>;

export const BasicFlexContainer: Story = {
  render: props => {
    const onResizeEnd = (widths: number[]) => {
      localStorage.setItem('resizedWidths', JSON.stringify(widths));
    };

    const savedWidths = localStorage.getItem('resizedWidths');

    return (
      <>
        <ResizableFlexContainer {...props} display="flex" onResizeEnd={onResizeEnd}>
          <Box
            bgcolor="semanticPalette.surface.secondary"
            width={savedWidths ? JSON.parse(savedWidths)[0] : 200}
            minWidth={100}
            maxWidth={400}
            p={3}
          >
            <Box>
              <Typography variant="h2">Panel 1</Typography>
              <Typography variant="body1">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
                deserunt mollit anim id est laborum.
              </Typography>
              <Typography variant="body1">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia
                deserunt mollit anim id est laborum.
              </Typography>
            </Box>
          </Box>
          <Box
            bgcolor="semanticPalette.surface.secondary"
            width={savedWidths ? JSON.parse(savedWidths)[1] : 600}
            minWidth={500}
            p={3}
          >
            <Typography variant="h2">Panel 2</Typography>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
              incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
              exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
              dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
              Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
              mollit anim id est laborum.
            </Typography>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
              incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
              exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
              dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
              Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
              mollit anim id est laborum.
            </Typography>
          </Box>
          <Box
            bgcolor="semanticPalette.surface.secondary"
            width={savedWidths ? JSON.parse(savedWidths)[2] : 600}
          >
            <Typography variant="h2">Panel 3</Typography>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
              incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
              exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
              dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
              Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
              mollit anim id est laborum.
            </Typography>
            <Typography variant="body1">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
              incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
              exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure
              dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
              Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt
              mollit anim id est laborum.
            </Typography>
          </Box>
        </ResizableFlexContainer>
      </>
    );
  },
};
