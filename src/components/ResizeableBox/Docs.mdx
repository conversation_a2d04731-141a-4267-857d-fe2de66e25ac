import { <PERSON>a, <PERSON><PERSON>, <PERSON>s, Story } from "@storybook/blocks";
import * as ResizeableBox from './index.stories.tsx'

<Meta of={ResizeableBox}/>

 ## Overview
The `ResizableFlexContainer` component serves as a wrapper component which inherits all the properties of the [Box](https://mui.com/material-ui/react-box/) component from Material UI.
The container enables drag to resize horizontally for all children elements.
Children elements respect `minWidth`, `maxWidth` and `width`. If these properties are not set, their widths are adjusted to fill the available space evenly. 

[TBD]: Single resizable `Box` component that is independent of the flex container.

<Canvas of={ResizeableBox.BasicFlexContainer} />
<Controls of={ResizeableBox.BasicFlexContainer}/>
