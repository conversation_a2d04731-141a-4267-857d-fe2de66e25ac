import {fireEvent, render, screen} from '@testing-library/react';
import {DesignSystemProvider} from 'src/design-system-provider';

import {ResizableFlexContainer} from '.';
import {Box} from '../Box';

describe('ResizableFlexContainer', () => {
  beforeEach(() => {
    // Mock getBoundingClientRect to simulate element sizes
    Element.prototype.getBoundingClientRect = jest.fn(() => ({
      width: 300,
      height: 100,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
      toJSON: () => {},
    }));
  });

  it('should render the ResizableFlexContainer', () => {
    renderWithProvider(
      <ResizableFlexContainer>
        <Box>One</Box>
        <Box>Two</Box>
        <Box>Three</Box>
      </ResizableFlexContainer>
    );

    expect(screen.getByText('One')).toBeInTheDocument();
  });

  it('should resize boxes on drag', async () => {
    let currentWidth = 300;
    Element.prototype.getBoundingClientRect = jest.fn(() => ({
      width: currentWidth,
      height: 100,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
      toJSON: () => {},
    }));

    renderWithProvider(
      <ResizableFlexContainer width={500}>
        <Box width={300}>One</Box>
        <Box width={300}>Two</Box>
        <Box width={300}>Three</Box>
      </ResizableFlexContainer>
    );

    const resizers = screen.queryAllByRole('separator');
    expect(resizers.length).toBe(2);
    const resizer = resizers[0];
    expect(resizer).not.toBeNull();

    const boxOne = screen.getByText('One').parentElement;
    const boxTwo = screen.getByText('Two');
    const boxThree = screen.getByText('Three');
    expect(boxOne).not.toBeNull();
    expect(boxTwo).not.toBeNull();
    expect(boxThree).not.toBeNull();

    const boxOneWidth = boxOne?.getBoundingClientRect().width;
    fireEvent.mouseDown(resizer, {clientX: 100});
    currentWidth = 200;
    fireEvent.mouseMove(document, {clientX: 200});
    fireEvent.mouseUp(document);

    expect(boxOne?.getBoundingClientRect().width).not.toEqual(boxOneWidth);
  });

  it('should return resized widths in px on drag end', () => {
    const onResizeEnd = jest.fn();

    const currentWidth = 300;
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: currentWidth,
    });

    Element.prototype.getBoundingClientRect = jest.fn(() => ({
      width: currentWidth,
      height: 100,
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
      toJSON: () => {},
    }));

    renderWithProvider(
      <ResizableFlexContainer onResizeEnd={onResizeEnd}>
        <Box width={300}>One</Box>
        <Box width={300}>Two</Box>
        <Box width={300}>Three</Box>
      </ResizableFlexContainer>
    );

    const resizers = screen.queryAllByRole('separator');
    expect(resizers.length).toBe(2);
    const resizer = resizers[0];
    expect(resizer).not.toBeNull();

    fireEvent.mouseDown(resizer, {clientX: 100});
    fireEvent.mouseMove(document, {clientX: 200});
    fireEvent.mouseUp(document);

    expect(onResizeEnd).toHaveBeenCalled();

    const resizedWidths = onResizeEnd.mock.calls[0][0];
    expect(resizedWidths).toBeInstanceOf(Array);
    expect(resizedWidths.length).toBe(3);
    expect(resizedWidths[0]).toBeGreaterThan(0);
    expect(resizedWidths[1]).toBeGreaterThan(0);
    expect(resizedWidths[2]).toBeGreaterThan(0);
  });
});

const renderWithProvider = (children: JSX.Element) => {
  render(<DesignSystemProvider muiThemeKey={1}>{children}</DesignSystemProvider>);
};
