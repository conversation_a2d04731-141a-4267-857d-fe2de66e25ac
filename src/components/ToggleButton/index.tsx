import type {ForwardedRef} from 'react';
import {forwardRef} from 'react';
import type {Components, ToggleButtonProps as MUIToggleButtonProps, Theme} from '@mui/material';
import {ToggleButton as MUIToggleButton, toggleButtonClasses} from '@mui/material';

// Note: This component has augmented props. See src/muiTheme.d.ts for prop overrides
export const ToggleButtonOverrides: Components<Theme>['MuiToggleButton'] = {
  styleOverrides: {
    root: ({theme}) => ({
      [`&.${toggleButtonClasses.root}`]: {
        backgroundColor: theme.palette.semanticPalette.surface.main,
        borderColor: theme.palette.semanticPalette.stroke.main,
        color: theme.palette.semanticPalette.text.secondary,
        // -1px to accomodate for border
        padding: '7px 11px',
        '&:has(> svg)': {
          padding: '7px',
        },
        '&:hover': {
          color: theme.palette.semanticPalette.text.main,
          backgroundColor: theme.palette.semanticPalette.surface.main,
        },
      },
      [`&.${toggleButtonClasses.selected}, &:active`]: {
        backgroundColor: theme.palette.semanticPalette.surface.info,
        color: theme.palette.semanticPalette.text.info,
        '&:hover': {
          backgroundColor: theme.palette.semanticPalette.surface.info,
        },
        [`&.${toggleButtonClasses.primary}`]: {
          backgroundColor: theme.palette.semanticPalette.surface.brand,
          color: theme.palette.semanticPalette.text.brand,
          '&:hover': {
            backgroundColor: theme.palette.semanticPalette.surface.brand,
            color: theme.palette.semanticPalette.text.main,
          },
          [`&.${toggleButtonClasses.disabled}`]: {
            color: theme.palette.semanticPalette.textInverted.secondary,
          },
        },
      },
      [`&.${toggleButtonClasses.disabled}`]: {
        color: theme.palette.semanticPalette.textInverted.secondary,
      },
    }),
    sizeSmall: ({theme}) => ({
      [`&.${toggleButtonClasses.sizeSmall}`]: {
        fontSize: `${theme.typography.body2.fontSize}px`,
        lineHeight: theme.spacing(4),
        // -1px to accomodate for border
        padding: '3px 7px',
        '&:has(> svg)': {
          paddingTop: '3px',
          paddingBottom: '3px',
        },
      },
    }),
  },
};

type ToggleButtonProps<C extends React.ElementType> = MUIToggleButtonProps & {
  component?: C;
};

const ToggleButtonComponent = <C extends React.ElementType>(
  props: ToggleButtonProps<C>,
  ref: ForwardedRef<HTMLButtonElement>
) => <MUIToggleButton ref={ref} {...props} />;

export const ToggleButton = forwardRef(ToggleButtonComponent);

ToggleButton.displayName = 'ToggleButton';

export type {ToggleButtonProps};
