import {use<PERSON><PERSON>back, useMemo, useState, type SyntheticEvent} from 'react';
import {SelectAllText} from 'src/utils/staticText';
import {isDefined} from 'src/utils/typeGuards';
import type {
  AutocompleteChangeDetails,
  AutocompleteChangeReason,
  AutocompleteRenderGroupParams,
  AutocompleteValue,
} from '@mui/material';
import {Autocomplete as MuiAutocomplete} from '@mui/material';

import {AutocompleteOverrides} from 'src/components/Autocomplete/AutocompleteOverrides';
import {
  AutoCompleteSelectDeselectAll,
  RenderOption,
  RenderTags,
  RenderTextFieldInput,
  sortOptionsByGroup,
  sortOptionsBySelected,
} from 'src/components/Autocomplete/autoCompleteUtils';
import {isSelectAllAutocompleteProps} from 'src/components/Autocomplete/typeGuards';
import {
  type AutocompleteCommonProps,
  type AutocompleteMultiProps,
  type AutocompleteOptionType,
  type AutocompleteSingleProps,
  type OnChangeValue,
} from 'src/components/Autocomplete/types';

import type {HandleMultiChange} from './renderGroup';
import {MultiGroup} from './renderGroup';

const emptyValue: OnChangeValue<unknown, unknown> = [];

type AutocompleteProps<
  T,
  Multiple extends boolean | undefined = boolean,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = boolean
> = (Multiple extends true ? AutocompleteMultiProps : AutocompleteSingleProps) &
  AutocompleteCommonProps<T, Multiple, DisableClearable, FreeSolo>;

function Autocomplete<
  T extends AutocompleteOptionType,
  Multiple extends boolean | undefined = false,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = boolean
>(props: AutocompleteProps<T, Multiple, DisableClearable, FreeSolo>): JSX.Element {
  const [searchText, setSearchText] = useState<string>('');
  const {
    description,
    disabled,
    error,
    focused,
    freeSolo,
    hasClear,
    hasSelectAll,
    helperText,
    inputVariant,
    label,
    localeText,
    multiple,
    onChange,
    options,
    readOnly,
    required,
    onInputChange,
    renderInputProps,
    ...rest
  } = props;

  const SELECT_ALL_OPTIONS = useMemo(
    () =>
      (hasSelectAll && isSelectAllAutocompleteProps({hasSelectAll, localeText})
        ? [
            {
              label: localeText?.selectAll ?? SelectAllText.SELECT_ALL,
              value: 'select-all',
            },
            {
              label: localeText?.deselectAll ?? SelectAllText.DESELECT_ALL,
              value: 'deselect-all',
            },
          ]
        : []) as T[],
    [hasSelectAll, localeText]
  );

  const optionsWithSelectAll = useMemo(
    () => [...SELECT_ALL_OPTIONS, ...options],
    [SELECT_ALL_OPTIONS, options]
  );

  const handleChange = useCallback(
    (
      event: SyntheticEvent<Element, Event>,
      value: AutocompleteValue<T, Multiple, DisableClearable, FreeSolo>,
      _3: AutocompleteChangeReason,
      details?: AutocompleteChangeDetails<T>
    ) => {
      if (!onChange) return;
      if (hasSelectAll) {
        if (details?.option.value === 'select-all') {
          const selectedOptions = props.options.filter((option: T) => {
            return (
              !option.disabled && option.value !== 'select-all' && option.value !== 'deselect-all'
            );
          });
          onChange(event, selectedOptions as OnChangeValue<Multiple, T>, _3, details);
          return;
        }
        if (details?.option.value === 'deselect-all') {
          onChange(event, emptyValue as OnChangeValue<Multiple, T>, _3, details);
          return;
        }
      }

      onChange(event, value as OnChangeValue<Multiple, T>, _3, details);
    },
    [hasSelectAll, onChange, props.options]
  );

  const renderGroup = useMemo(() => {
    if (!multiple) return undefined;
    const localSelectedOptions = isDefined(props.value) ? (props.value as T[]) : [];

    return (renderGroupParams: AutocompleteRenderGroupParams) => (
      <MultiGroup<T>
        key={renderGroupParams.key}
        renderGroupParams={renderGroupParams}
        searchText={searchText}
        options={options}
        value={localSelectedOptions}
        onChange={handleChange as HandleMultiChange<T>}
      />
    );
  }, [handleChange, multiple, options, props.value, searchText]);

  const renderOption = useCallback(
    (renderOptionProps, option, {selected}) => {
      if (option.value === 'select-all' || option.value === 'deselect-all') {
        return (
          <AutoCompleteSelectDeselectAll
            key={option.value}
            option={option}
            renderOptionProps={renderOptionProps}
          />
        );
      }

      return (
        <RenderOption
          renderOptionProps={renderOptionProps}
          selected={selected}
          option={option}
          multiple={multiple}
          key={option.value}
        />
      );
    },
    [multiple]
  );

  // only used when multiple is true (renderValue in MUI7 will allow modifying single select input display)
  const renderTags = useCallback(
    (value, _, ownerState) => {
      return (
        <RenderTags
          value={value}
          ownerState={ownerState}
          readOnly={readOnly}
          freeSolo={freeSolo}
          multiSelectText={{
            getLimitTagsText: localeText?.getLimitTagsText,
            getLimitTagsTextFocused: localeText?.getLimitTagsTextFocused,
          }}
        />
      );
    },
    [freeSolo, localeText?.getLimitTagsText, localeText?.getLimitTagsTextFocused, readOnly]
  );

  const renderTextFieldProps = useMemo(
    () => ({
      description,
      disabled,
      error,
      focused,
      helperText,
      label,
      readOnly,
      required,
      variant: inputVariant,
    }),
    [description, disabled, error, focused, helperText, inputVariant, label, readOnly, required]
  );

  const renderInput = useCallback(
    params => {
      const valueIsArray = Array.isArray(props.value) && props.value.length;
      const placeholder = multiple ? (valueIsArray ? '' : props.placeholder) : props.placeholder;
      const textFieldParams = {
        ...params,
        InputProps: {...params.InputProps, ...renderInputProps},
        inputProps: {...params.inputProps, ...props.inputProps},
      };
      return (
        <RenderTextFieldInput
          placeholder={placeholder}
          params={textFieldParams}
          {...renderTextFieldProps}
        />
      );
    },
    [multiple, props, renderInputProps, renderTextFieldProps]
  );

  const handleInputChange = useCallback(
    (_, value, reason) => {
      onInputChange?.(_, value, reason);
      setSearchText(value);
    },
    [onInputChange]
  );

  const disableClearable = freeSolo ? false : hasClear ? false : true;
  const hasGroup = options.some(option => isDefined(option.group));

  return (
    <MuiAutocomplete
      {...rest}
      disabled={disabled}
      freeSolo={freeSolo}
      disableClearable={disableClearable as DisableClearable}
      getOptionDisabled={option => !!option.disabled}
      isOptionEqualToValue={(option, value) => option.value === value.value}
      multiple={multiple}
      onInputChange={handleInputChange}
      onChange={handleChange}
      options={hasSelectAll ? optionsWithSelectAll : options}
      readOnly={readOnly}
      placeholder={undefined}
      renderInput={renderInput}
      renderTags={renderTags} // only used when multiple is true (renderValue in MUI7 will allow modifying single select input display)
      renderOption={renderOption}
      groupBy={hasGroup ? option => option.group : undefined}
      renderGroup={renderGroup}
      slotProps={{
        popper: {
          sx: theme => ({
            minWidth: hasSelectAll ? theme.fixedWidths.xs : 'auto',
          }),
        },
        ...props.slotProps,
      }}
    />
  );
}

Autocomplete.displayName = 'Autocomplete';

export {
  Autocomplete,
  type AutocompleteProps,
  AutocompleteOverrides,
  sortOptionsBySelected,
  sortOptionsByGroup,
};
