import {type SyntheticEvent} from 'react';
import type {
  AutocompleteChangeDetails,
  AutocompleteChangeReason,
  AutocompleteRenderInputParams,
  InputBaseComponentProps,
  AutocompleteProps as MuiAutocompleteprops,
} from '@mui/material';

import {type InputVariant} from 'src/components/Inputs/InputBase';

type AutocompleteSingleProps = {
  multiple?: false;
  localeText?: never;
  hasSelectAll?: never;
};

type AutocompletePropsMultiWithSelectAll = {
  hasSelectAll: true;
  localeText?: {
    selectAll?: string;
    deselectAll?: string;
    getLimitTagsText?: (more: number) => string;
    getLimitTagsTextFocused?: (more: number) => string;
  };
};

type AutocompletePropsMultiWithoutSelectAll = {
  hasSelectAll?: false;
  localeText?: {
    getLimitTagsText?: (more: number) => string;
    getLimitTagsTextFocused?: (more: number) => string;
  };
};

type AutocompleteMultiProps = {
  multiple: true;
} & (AutocompletePropsMultiWithSelectAll | AutocompletePropsMultiWithoutSelectAll);

type OnChangeValue<Multiple, T> = Multiple extends true ? T[] : T;

type AutocompleteCommonProps<
  T,
  Multiple extends boolean | undefined = false,
  DisableClearable extends boolean | undefined = false,
  FreeSolo extends boolean | undefined = boolean
> = Omit<
  MuiAutocompleteprops<T, Multiple, DisableClearable, FreeSolo>,
  // Note: Locking down these options to keep the API consistent and in line with Leaf requirements/styles/designs
  | 'clearIcon'
  | 'disableClearable'
  // Note: Remove defaultValue from the API to enforce controlled component usage
  | 'defaultValue'
  | 'forcePopupIcon'
  // Note: Remove getLimitTagsText and wrap under localeText to maintain parity with SelectFIeld
  | 'getLimitTagsText'
  | 'getOptionDisabled'
  // Note: Remove limitTags since we will always limit it to 1 tag per Leaf designs
  | 'limitTags'
  | 'onChange'
  | 'popupIcon'
  | 'renderOption'
  | 'renderTags'
  | 'renderInput'
> & {
  description?: string;
  disabled?: boolean;
  error?: boolean;
  focused?: boolean;
  hasClear?: boolean;
  helperText?: string;
  inputVariant?: InputVariant;
  /**
   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.
   * */
  inputProps?: InputBaseComponentProps;
  renderInputProps?: Partial<AutocompleteRenderInputParams['InputProps']>;
  label?: string;
  onChange: (
    event: SyntheticEvent<Element, Event>,
    value: OnChangeValue<Multiple, T>,
    _3: AutocompleteChangeReason,
    details?: AutocompleteChangeDetails<T>
  ) => void;
  placeholder?: string;
  required?: boolean;
};

/**
 * @property {string} label - Used for text searching and display unless DisplayLabel is provided
 * @property {JSX.Element | undefined} DisplayLabel - Used for customized option display, if not provided label will be used
 */
type AutocompleteOptionType = {
  label: string;
  value: string | number;
  disabled?: boolean;
  group?: string;
  DisplayLabel?: JSX.Element;
};

export type {
  AutocompleteSingleProps,
  AutocompletePropsMultiWithSelectAll,
  AutocompletePropsMultiWithoutSelectAll,
  AutocompleteMultiProps,
  OnChangeValue,
  AutocompleteCommonProps,
  AutocompleteOptionType,
};
