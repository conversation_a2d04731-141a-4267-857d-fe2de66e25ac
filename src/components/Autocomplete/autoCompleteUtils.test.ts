import {
  sortOptionsByGroup,
  sortOptionsBySelected,
} from 'src/components/Autocomplete/autoCompleteUtils';

describe('sortOptionsBySelected', () => {
  it('should sort options by selected', () => {
    const optionsMock = [
      {label: 'Option 1', value: 'option-1'},
      {label: 'Option 2', value: 'option-2'},
      {label: 'Option 3', value: 'option-3'},
      {label: 'Option 4', value: 'option-4'},
    ];
    const selectedOptionsMock = [
      {label: 'Option 2', value: 'option-2'},
      {label: 'Option 4', value: 'option-4'},
    ];
    const sortedOptions = sortOptionsBySelected(optionsMock, selectedOptionsMock);

    expect(sortedOptions).toEqual([
      {label: 'Option 2', value: 'option-2'},
      {label: 'Option 4', value: 'option-4'},
      {label: 'Option 1', value: 'option-1'},
      {label: 'Option 3', value: 'option-3'},
    ]);
  });
});

describe('sortOptionsByGroup', () => {
  it('should sort options by group', () => {
    const optionsMock = [
      {label: 'Option 1', value: 'option-1', group: 'Group 1'},
      {label: 'Option 2', value: 'option-2', group: 'Group 2'},
      {label: 'Option 3', value: 'option-3', group: 'Group 1'},
      {label: 'Option 4', value: 'option-4', group: 'Group 2'},
    ];
    const sortedOptions = sortOptionsByGroup(optionsMock);

    expect(sortedOptions).toEqual([
      {label: 'Option 1', value: 'option-1', group: 'Group 1'},
      {label: 'Option 3', value: 'option-3', group: 'Group 1'},
      {label: 'Option 2', value: 'option-2', group: 'Group 2'},
      {label: 'Option 4', value: 'option-4', group: 'Group 2'},
    ]);
  });
});
