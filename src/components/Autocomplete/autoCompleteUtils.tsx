import {useEffect, useState, type SyntheticEvent} from 'react';
import {MultiSelectText} from 'src/utils/staticText';
import {isDefined, isString} from 'src/utils/typeGuards';
import type {AutocompleteRenderGroupParams, AutocompleteRenderInputParams} from '@mui/material';
import {
  autocompleteClasses,
  Checkbox,
  Chip,
  chipClasses,
  IconButton,
  lighten,
  MenuItem,
  useFormControl,
} from '@mui/material';

import type {AutocompleteOptionType} from 'src/components/Autocomplete/types';
import {Box} from 'src/components/Box';
import {SvgIcon} from 'src/components/Icon';
import {type InputVariant} from 'src/components/Inputs/InputBase';
import {Label} from 'src/components/Inputs/inputHelpers';
import {TextField} from 'src/components/Inputs/TextField';
import {Typography} from 'src/components/Typography';
import {TypographyOverflow} from 'src/components/TypographyOverflow';

function RenderTextFieldInput(props: {
  params: AutocompleteRenderInputParams;
  disabled?: boolean;
  description?: string;
  placeholder?: string;
  error?: boolean;
  focused?: boolean;
  helperText?: string;
  label?: string;
  required?: boolean;
  readOnly?: boolean;
  variant?: InputVariant;
}) {
  const {placeholder, readOnly, label, description, params, variant, helperText} = props;

  const muiFormControl = useFormControl();

  const {error, disabled, focused, required} = muiFormControl || {
    error: props.error,
    helperText: props.helperText,
    disabled: props.disabled,
    focused: props.focused,
    required: props.required,
  };

  return (
    <TextField
      aria-readonly={readOnly}
      {...params}
      disabled={disabled}
      focused={focused}
      required={required}
      error={error}
      helperText={helperText}
      label={label ? <Label title={label} description={description} /> : ''}
      placeholder={placeholder}
      size="medium"
      variant={variant ?? 'outlined'}
    />
  );
}

function RenderOption<T extends AutocompleteOptionType>({
  renderOptionProps,
  option: {DisplayLabel, ...option},
  selected,
  multiple,
}: {
  renderOptionProps: React.HTMLAttributes<HTMLLIElement>;
  option: T;
  selected: boolean;
  multiple?: boolean;
}) {
  return (
    <MenuItem
      {...renderOptionProps}
      sx={theme => ({
        ...(multiple && {
          [`&&&.${autocompleteClasses.option}`]: {
            paddingLeft: theme.spacing(1),
            paddingY: theme.spacing(2),
          },
        }),
      })}
      key={option.label}
    >
      {multiple && <Checkbox checked={selected} />}
      {isDefined(DisplayLabel) ? (
        DisplayLabel
      ) : (
        <TypographyOverflow>{option.label}</TypographyOverflow>
      )}
    </MenuItem>
  );
}

function AutoCompleteSelectDeselectAll<T extends AutocompleteOptionType>(props: {
  option: T;
  renderOptionProps: React.HTMLAttributes<HTMLLIElement>;
}) {
  return (
    <MenuItem
      {...props.renderOptionProps}
      sx={theme => ({
        '&&&&': {
          width: '50%',
          display: 'inline-flex',
          padding: theme.spacing(0),
          minHeight: theme.spacing(9),
          borderBottom: `1px solid ${theme.palette.divider}`,
        },
      })}
    >
      <Box display="flex" justifyContent="center" width="100%">
        <Typography variant="h5" component="span">
          {props.option.label}
        </Typography>
      </Box>
    </MenuItem>
  );
}

function RenderGroup({
  group,
  children,
  expanded,
  onChange,
  indeterminate,
  selected,
  disabled,
  multiple,
}: AutocompleteRenderGroupParams & {
  expanded: boolean;
  onChange: (event: SyntheticEvent<Element, Event>, group: string) => void;
  indeterminate: boolean;
  selected: boolean;
  disabled: boolean;
  multiple?: boolean;
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    expanded && setIsExpanded(true); // This is to handle when it's expanded by search
  }, [expanded]);

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        onClick={() => setIsExpanded(!isExpanded)}
        pl={1}
        pr={2}
        sx={theme => ({
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: lighten(theme.palette.common.black, 0.96),
          },
        })}
      >
        <Box
          pl={multiple ? 0 : 3}
          color={theme => (disabled ? theme.palette.text.disabled : 'inherit')}
          aria-disabled={disabled}
        >
          {multiple && (
            <Checkbox
              indeterminate={indeterminate}
              checked={selected}
              onClick={e => {
                e.stopPropagation();
                onChange(e, group);
              }}
            />
          )}
          {group}
        </Box>
        <IconButton onClick={() => setIsExpanded(!isExpanded)}>
          <SvgIcon
            fontSize="body1"
            type={isExpanded ? 'chevron-up' : 'chevron-down'}
            color="main"
          />
        </IconButton>
      </Box>
      {isExpanded && <Box pl={7}>{children}</Box>}
    </Box>
  );
}

const FocusedInputDisplay = ({displayElement}: {displayElement: React.ReactNode}) => (
  <Chip
    label={displayElement}
    color="secondary"
    size="small"
    sx={{
      maxWidth: '80%',
      mr: theme => theme.spacing(1),
      [`&&&:has(>svg).${chipClasses.sizeSmall}`]: {
        padding: '0',
      },
    }}
  />
);

const NonFocusedInputDisplay = ({
  displayElement,
  readOnly,
}: {
  displayElement: React.ReactNode;
  readOnly?: boolean;
}) =>
  isString(displayElement) ? (
    <TypographyOverflow
      variant="body1"
      component={'span'}
      color={theme => (readOnly ? theme.palette.grey[400] : 'inherit')}
    >
      {displayElement}
    </TypographyOverflow>
  ) : (
    <Box color={theme => (readOnly ? theme.palette.grey[400] : 'inherit')} width={1}>
      {displayElement}
    </Box>
  );

// only used when multiple = true (renderValue in MUI7 will allow modifying single select input display)
function RenderTags<T extends AutocompleteOptionType>(props: {
  value: T[];
  readOnly?: boolean;
  ownerState: {focused: boolean};
  freeSolo?: boolean;
  multiSelectText?: {
    getLimitTagsText?: (more: number) => React.ReactNode;
    getLimitTagsTextFocused?: (more: number) => React.ReactNode;
  };
}) {
  const {readOnly, value, freeSolo, ownerState, multiSelectText} = props;

  // Focused input display text
  if (!readOnly && ownerState.focused) {
    // single value selected
    if (value.length <= 1) {
      const displayElement = (freeSolo && (value[0].label ?? value)) || value[0].label;
      return <FocusedInputDisplay displayElement={displayElement} />;
    }

    // multiple values selected
    const displayElementMultipleValues = isDefined(multiSelectText?.getLimitTagsTextFocused)
      ? multiSelectText?.getLimitTagsTextFocused?.(value.length)
      : `${value.length} ${MultiSelectText.ITEMS_SELECTED}`;

    return <FocusedInputDisplay displayElement={displayElementMultipleValues} />;
  }

  // Non focused input display text
  // single value selected
  if (value.length <= 1) {
    const displayElement = (freeSolo && (value[0].label ?? value)) || value[0].label;
    return <NonFocusedInputDisplay readOnly={readOnly} displayElement={displayElement} />;
  }

  // multiple values selected
  const displayElementMultipleValues = isDefined(multiSelectText?.getLimitTagsText)
    ? multiSelectText?.getLimitTagsText?.(value.length)
    : `${value.length} ${MultiSelectText.ITEMS_SELECTED}`;

  return (
    <NonFocusedInputDisplay readOnly={readOnly} displayElement={displayElementMultipleValues} />
  );
}

function sortOptionsBySelected<T extends AutocompleteOptionType>(
  options: T[],
  selectedOptions: T[]
) {
  const selectedValues = selectedOptions.map(option => option.value);

  const sortedSelectedOptions = selectedOptions.sort((a, b) => (a.value > b.value ? 1 : -1));
  const unselectedOptions = options.filter(option => !selectedValues.includes(option.value));

  return [...sortedSelectedOptions, ...unselectedOptions];
}

function sortOptionsByGroup<T extends AutocompleteOptionType>(options: T[]) {
  return options.sort((a, b) => {
    if (isDefined(a.group) && isDefined(b.group)) {
      return a.group === b.group ? 0 : a.group < b.group ? -1 : 1;
    } else return 0;
  });
}

export {
  RenderTextFieldInput,
  RenderOption,
  RenderTags,
  AutoCompleteSelectDeselectAll,
  RenderGroup,
  sortOptionsBySelected,
  sortOptionsByGroup,
};
