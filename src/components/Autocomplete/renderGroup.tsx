import {type SyntheticEvent} from 'react';
import {isArray, isNonEmptyArray, isNonEmptyString, isUndefined} from 'src/utils/typeGuards';
import type {AutocompleteChangeReason, AutocompleteRenderGroupParams} from '@mui/material';

import {RenderGroup} from 'src/components/Autocomplete/autoCompleteUtils';
import type {OnChangeValue} from 'src/components/Autocomplete/types';
import {type AutocompleteOptionType} from 'src/components/Autocomplete/types';

import {
  getFilteredGroups,
  getGroupOptions,
  getIsEntireGroupSelected,
  isEntireGroupDisabled,
} from './groupUtils';

export type HandleMultiChange<T> = (
  event: SyntheticEvent<Element, Event>,
  value: OnChangeValue<true, T>,
  _3: AutocompleteChangeReason
) => void;

const handleMultiGroupChange =
  <T extends AutocompleteOptionType>(
    options: readonly T[],
    value: OnChangeValue<true, T>,
    handleChange: HandleMultiChange<T>
  ) =>
  (event: SyntheticEvent<Element, Event>, group: string) => {
    const allGroupOptions = getGroupOptions(group, options);
    const isEntireGroupSelected = getIsEntireGroupSelected(allGroupOptions, value);

    if (isEntireGroupSelected) {
      const newValues = value.filter(option => group !== option.group);
      handleChange(event, newValues, 'selectOption');
    } else {
      const allButDisabledOptionsAreSelected = allGroupOptions.every(
        option => option.disabled || value.includes(option)
      );

      if (allButDisabledOptionsAreSelected) {
        const newValues = value.filter(option => !allGroupOptions.includes(option));
        handleChange(event, newValues, 'selectOption');
      } else {
        const missingGroupOptions = allGroupOptions.filter(
          option => !value.includes(option) && !option.disabled
        );
        handleChange(event, [...value, ...missingGroupOptions], 'selectOption');
      }
    }
  };

export const MultiGroup = <T extends AutocompleteOptionType>({
  renderGroupParams,
  searchText,
  options,
  value,
  onChange,
}: {
  renderGroupParams: AutocompleteRenderGroupParams;
  searchText: string;
  options: readonly T[];
  value: T[];
  onChange: HandleMultiChange<T>;
}) => {
  const {group, children, key} = renderGroupParams;
  if (isUndefined(group)) return <>{children}</>;
  if (!isArray(value)) return <>{children}</>;

  const groupOptions = getGroupOptions<T>(group, options);

  const isEntireGroupSelected = getIsEntireGroupSelected<T>(groupOptions, value);
  const someSelected =
    !isEntireGroupSelected &&
    isNonEmptyArray(value) &&
    value.some(option => option.group === group);

  const filteredGroups = getFilteredGroups<T>(options, searchText);
  const expanded =
    isNonEmptyString(searchText) &&
    isNonEmptyArray(filteredGroups) &&
    filteredGroups.includes(group);

  const disabledGroup = isEntireGroupDisabled<T>(groupOptions);

  return (
    <RenderGroup
      key={key}
      expanded={expanded}
      group={group}
      multiple={true}
      onChange={handleMultiGroupChange<T>(options, value, onChange)}
      indeterminate={someSelected}
      selected={isEntireGroupSelected ?? false}
      disabled={disabledGroup}
    >
      {children}
    </RenderGroup>
  );
};
