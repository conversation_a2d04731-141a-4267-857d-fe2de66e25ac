import {
  FORM_COMPONENT_ARGTYPES,
  getArgTypes,
  POPPER_ARGTYPES,
  SYSTEM_ARGTYPES,
} from 'src/storybook-utils/argTypes';

const autoCompleteFieldArgTypeKeys = [
  'autoWidth',
  'autoFocus',
  'componentProps',
  'open',
  'disabled',
  'disablePortal',
  'fullWidth',
  'id',
  'multiple',
  'name',
  'options',
  'onChange',
  'onClose',
  'onOpen',
  'open',
  'placeholder',
  'readOnly',
  'required',
  'variant',
  'classes',
  'slotProps',
  'sx',
];

const AUTOCOMPLETE_ARGTYPES = {
  options: {
    description:
      'The option to display in the list, containing specific label and value. Aditionally you can set them to disabled, group them or customize the display label with DisplayLabel.',
    table: {
      type: {
        summary:
          'Array<{label: string, value: string, disabled?: boolean, group?: string; DisplayLabel?: JSX.Element}>',
      },
    },
  },
  autoComplete: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the portion of the selected suggestion that the user hasnt typed, known as the completion string, appears inline after the input cursor in the textbox. The inline completion string is visually highlighted and has a selected state.',
  },
  autoHighlight: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the first option will be automatically highlighted. This does not affect the input value.',
  },
  autoSelect: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'Control if the input should be blurred when an option is selected: `false` the input is not blurred. `true` the input is always blurred.`touch` the input is blurred after a touch event. `mouse` the input is blurred after a mouse event.',
  },
  blurOnSelect: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the input will blur when a selection is made. This is helpful when the AutoComplete is used in a dialog.',
  },
  clearOnBlur: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the inputs text is cleared on blur if no value is selected. Set it to true if you want to help the user enter a new value. Set it to false if you want to help the user resume their search.',
  },
  clearOnEscape: {
    table: {
      type: {summary: 'boolean'},
    },
    description: 'If true, clear all values when the user presses escape and the popup is closed.',
  },
  clearText: {
    table: {
      type: {summary: 'string'},
      defaultValue: {summary: 'Clear'},
    },
    control: 'text',
    description: 'Override the default text for the clear icon button.',
  },
  closeText: {
    table: {
      type: {summary: 'string'},
      defaultValue: {summary: 'Close'},
    },
    control: 'text',
    description: 'Override the default text for the close popup icon button.',
  },
  description: {
    table: {
      type: {summary: 'string'},
    },
    description: 'The description of the Autocomplete passed to the TextField element.',
  },
  disableCloseOnSelect: {
    table: {
      type: {summary: 'boolean'},
    },
    description: 'If true, the popup wont close when a value is selected.',
  },
  disabledItemsFocusable: {
    table: {
      type: {summary: 'boolean'},
      defaultValue: {summary: 'false'},
    },
    description: 'If true, will allow focus on disabled items.',
  },
  disableListWrap: {
    table: {
      type: {summary: 'boolean'},
      defaultValue: {summary: 'false'},
    },
    description: 'If true, the list box in the popup will not wrap focus.',
  },
  filterOptions: {
    table: {
      type: {summary: 'func'},
    },
    description: 'A function that determines the filtered options to be rendered on search.',
  },
  filterSelectedOptions: {
    table: {
      type: {summary: 'boolean'},
    },
    description: 'If true, hide the selected options from the list box.',
  },
  freeSolo: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the Autocomplete is free solo, meaning that the user input is not bound to provided options.',
  },
  getOptionKey: {
    table: {
      type: {summary: 'func'},
    },
    description:
      'Used to determine the key for a given option. This can be useful when the labels of options are not unique (since labels are used as keys by default).',
  },
  getOptionLabel: {
    table: {
      type: {summary: 'func'},
    },
    description:
      'Used to determine the string value for a given option. Its used to fill the input (and the list box options if renderOption is not provided).If used in free solo mode, it must accept both the type of the options and a string.',
  },
  groupBy: {
    table: {
      type: {summary: 'func'},
    },
    description:
      'If provided, the options will be grouped under the returned string. The groupBy value is also used as the text for group headings when renderGroup is not provided.',
  },
  handleHomeEndKeys: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the component handles the "Home" and "End" keys when the popup is open. It should move focus to the first option and last option, respectively.',
  },
  includeInputInList: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the input is included in the popup list. It will be selected when navigating the list.',
  },
  inputValue: {
    table: {
      type: {summary: 'string'},
    },
    description: 'The input value.',
  },
  isOptionEqualToValue: {
    table: {
      type: {summary: 'func'},
    },
    description:
      'Used to determine if the option represents the given value. Uses strict equality by default. ⚠️ Both arguments need to be handled, an option can only match with one value.      ',
  },
  label: {
    table: {
      type: {summary: 'string'},
    },
    description: 'The label of the Autocomplete passed to the TextField element.',
  },
  ListboxComponent: {
    table: {
      type: {summary: 'elementType'},
    },
    description: 'The component used to render the listbox.',
  },
  ListboxProps: {
    table: {
      type: {summary: 'object'},
    },
    description: 'Props applied to the Listbox component.',
  },
  loading: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the component is in a loading state. This shows the loadingText in place of suggestions (only if there are no suggestions to show, for example options are empty).',
  },
  loadingText: {
    table: {
      type: {summary: 'node'},
    },
    description: 'Text to display when in a loading state.',
  },
  noOptionsText: {
    table: {
      type: {summary: 'string'},
    },
    description: 'Text to display when there are no options.',
  },
  onChange: {
    table: {
      type: {summary: 'func'},
    },
    description: `function(event: React.SyntheticEvent, value: Value | Array, reason: string, details?: string) => void.
      event: The event source of the callback
      value: The new value of the component.
      reason: One of "createOption", "selectOption", "removeOption", "blur" or "clear".
      details: Additional details about the event.
      `,
  },
  onHighlightChange: {
    table: {
      type: {summary: 'func'},
    },
    description: 'Callback fired when the highlight option changes.',
  },
  onInputChange: {
    table: {
      type: {summary: 'func'},
    },
    description: 'Callback fired when the input value changes.',
  },
  onOpenFocus: {
    table: {
      type: {summary: 'func'},
    },
    description: 'If true, the popup will open on input focus.',
  },
  openText: {
    table: {
      type: {summary: 'string'},
      defaultValue: {summary: 'Open'},
    },
    description: 'Override the default text for the open popup icon button.',
  },
  PaperComponent: {
    table: {
      type: {summary: 'elementType'},
    },
    description: 'The component used to render the popup.',
  },
  PopperComponent: {
    table: {
      type: {summary: 'elementType'},
    },
    description: 'The component used to position the popup.',
  },
  renderGroup: {
    table: {
      type: {summary: 'func'},
    },
    description: 'Render the group.',
  },
  selectOnFocus: {
    table: {
      type: {summary: 'boolean'},
    },
    description:
      'If true, the inputs text is selected on focus. It helps the user clear the selected value.',
  },
  hasClear: {
    table: {
      type: {summary: 'boolean'},
    },
    control: 'boolean',
    description:
      'If present, a clear button (x) will be displayed in the Autocomplete input and clear the selected items on click.',
  },
  hasSelectAll: {
    table: {
      type: {summary: 'boolean'},
    },
    control: 'boolean',
    description:
      'If present, a select all button and a deselect all button will appear in the menu list and select/deselect all options on respective clicks.',
  },
  sortSelectedOnClose: {
    description:
      'Allows the selected option to be sorted to the top of the list when the popup is closed.',
    table: {
      type: {summary: 'boolean'},
    },
  },
  helperText: {
    table: {
      type: {summary: 'string'},
    },
    description: 'The helper text of the Autocomplete passed to the TextField element.',
  },
  localeText: {
    table: {
      type: {
        summary: `{
          selectAll: 'string',
          deselectAll: 'string',
          getLimitTagsText: 'function',
          getLimitTagsTextFocused: 'function',
        }`,
      },
    },
    control: 'object',
    description:
      'Object to support localized text for: select/deselect all buttons and a function that should return a string that reads `x {items} {selected}` formatted for i18n by the consuming application. There are two separate functions for focused and unfocused states to allow for different string lengths when the user is searching within the input versus reading the value of the input. This is to accommodate the search input shrinking the input width.',
  },
};

const argTypes = getArgTypes(autoCompleteFieldArgTypeKeys, {
  ...SYSTEM_ARGTYPES,
  ...FORM_COMPONENT_ARGTYPES,
  ...POPPER_ARGTYPES,
});

const args = {
  ...AUTOCOMPLETE_ARGTYPES,
  ...argTypes,
};

export default args;
