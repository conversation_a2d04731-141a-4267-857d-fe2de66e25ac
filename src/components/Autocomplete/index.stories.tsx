import type {<PERSON><PERSON>, StoryObj} from '@storybook/react';
import React from 'react';
import {
  Autocomplete,
  Box,
  Stack,
  SvgIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  type AutocompleteProps,
} from 'src/index';

import argTypes from 'src/components/Autocomplete/argTypes';
import type {AutocompleteOptionType} from 'src/components/Autocomplete/types';

type Story = StoryObj<typeof Autocomplete>;

const meta: Meta<typeof Autocomplete> = {
  component: Autocomplete,
  title: 'components/Inputs/Autocomplete',
  argTypes,
  parameters: {
    design: {
      type: 'figma',
      // Note: Will need to update when the design is officially added to the DS Figma file
      url: 'https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Leaf-Design-System?type=design&node-id=13160-101271&mode=design&t=hk6KZqJormiymOdw-0',
    },
  },
};

function ExampleAutocompleteSingle<T extends AutocompleteOptionType>(args: AutocompleteProps<T>) {
  const [value, setValue] = React.useState<T | null>(null);

  const handleChange = (option: T) => {
    setValue(option);
  };

  return (
    <Autocomplete
      {...args}
      onChange={(_, v) => handleChange(v as T)}
      value={value}
      inputProps={{
        'aria-label': 'autocomplete input label',
      }}
    />
  );
}
ExampleAutocompleteSingle.displayName = 'Autocomplete';

function ExampleAutocompleteMulti<T extends AutocompleteOptionType>(args: AutocompleteProps<T>) {
  const [value, setValue] = React.useState<T[]>([]);

  const handleChange = (option: T[]) => {
    setValue(option);
  };

  return (
    <Autocomplete
      {...args}
      hasSelectAll={true}
      multiple={true}
      onChange={(_, v) => handleChange(v)}
      value={value}
    />
  );
}
ExampleAutocompleteMulti.displayName = 'Autocomplete';

function ExampleAutocompleteSingleWithSetValue<T extends AutocompleteOptionType>(
  args: AutocompleteProps<T>
) {
  const [value, setValue] = React.useState<T | null>(args.options[0]);

  const handleChange = (option: T) => {
    setValue(option);
  };

  return <Autocomplete {...args} onChange={(_, v) => handleChange(v as T)} value={value} />;
}
ExampleAutocompleteSingleWithSetValue.displayName = 'Autocomplete';

function ExampleAutocompleteSingleWithSetDisabledValue<T extends AutocompleteOptionType>(
  args: AutocompleteProps<T>
) {
  const [value, setValue] = React.useState<T | null>(args.options[1]);

  const handleChange = (option: T) => {
    setValue(option);
  };

  return <Autocomplete {...args} onChange={(_, v) => handleChange(v as T)} value={value} />;
}
ExampleAutocompleteSingleWithSetDisabledValue.displayName = 'Autocomplete';

function ExampleAutocompleteMultiWithSetValue<T extends AutocompleteOptionType>(
  args: AutocompleteProps<T>
) {
  const [value, setValue] = React.useState<T[]>([
    args.options[0],
    args.options[1],
    args.options[2],
  ]);

  const handleChange = (option: T[]) => {
    setValue(option);
  };

  return (
    <Autocomplete
      {...args}
      hasSelectAll={true}
      localeText={{
        selectAll: 'Select all',
        deselectAll: 'Deselect all',
        getLimitTagsText: more => {
          return `${more} years selected`;
        },
        getLimitTagsTextFocused: more => {
          return `${more} years selected`;
        },
      }}
      multiple={true}
      onChange={(_, v) => handleChange(v)}
      value={value}
    />
  );
}
ExampleAutocompleteMultiWithSetValue.displayName = 'Autocomplete';

const CustomDisplayLabel = ({displayText}: {displayText: string}) => (
  <Stack direction="row" width="100%" justifyContent="space-between" alignItems="center" gap={0.5}>
    <Typography variant="body1">{displayText}</Typography>
    <Tooltip title="This is a custom option example!">
      <div>
        <SvgIcon type="info-circled" fontSize="h5" />
      </div>
    </Tooltip>
  </Stack>
);

export default meta;

export const Basic: Story = {
  args: {
    placeholder: 'Select a year',
    label: 'Basic Autocomplete',
    helperText: 'Basic helper text',
    description: 'Basic description',
    options: [
      {
        label: '2021',
        value: '2021',
      },
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
  },
  render: args => {
    return (
      <Box maxWidth={200}>
        <ExampleAutocompleteSingle {...args} />
      </Box>
    );
  },
};

export const Single: Story = {
  args: {
    placeholder: 'Select a year',
    label: 'Single Autocomplete',
    helperText: 'Single helper text',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    value: {label: '2021', value: '2021'},
  },
  render: args => {
    return (
      <Stack width={theme => theme.fixedWidths.xs} spacing={4}>
        <ExampleAutocompleteSingle {...args} label="Autocomplete single" />
        <ExampleAutocompleteSingle {...args} hasClear label="Autocomplete single with hasClear" />
      </Stack>
    );
  },
};

export const Multiple: Story = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'Select a year',
    label: 'Multiple Autocomplete',
    helperText: 'Multiple helper text',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    value: {label: '2021', value: '2021'},
  },
  render: args => {
    return (
      <Stack maxWidth={theme => theme.fixedWidths.xs} spacing={8}>
        <ExampleAutocompleteMulti {...args} />
        <ExampleAutocompleteMulti
          {...args}
          disableCloseOnSelect
          label="Multiple with disableCloseOnSelect"
          helperText="This prop is used to keep the menu open after a selection is made. This is useful when you want to allow users to select multiple options without having to reopen the menu each time."
        />
        <Box maxWidth={200}>
          <ExampleAutocompleteMulti
            {...args}
            disableCloseOnSelect
            hasClear
            label="Multiple Autocomplete"
            helperText="With hasClear button and selectAll"
          />
        </Box>
      </Stack>
    );
  },
};

export const FreeSolo: Story = {
  argTypes: {
    options: {
      control: {
        type: 'object',
      },
    },
  },
  args: {
    placeholder: 'Add a producer',
    freeSolo: true,
    options: [
      {label: 'Tom', value: 'Tom'},
      {label: 'Rob', value: 'Rob', disabled: true},
      {label: 'Mary', value: 'Mary'},
      {label: 'Rachel', value: 'Rachel'},
    ],
  },
  render: args => {
    return (
      <Stack maxWidth={theme => theme.fixedWidths.xs} spacing={4}>
        <ExampleAutocompleteSingle {...args} label="FreeSolo single" />
        <ExampleAutocompleteMulti
          {...args}
          label="FreeSolo multiple"
          helperText="Multi free solo is in lab state. Please consult with the design team before using."
        />
      </Stack>
    );
  },
};

export const States: Story = {
  args: {
    options: [
      {label: '2021', value: '2021'},
      {
        label: '2022 - Custom',
        value: '2022',
        disabled: true,
        DisplayLabel: <CustomDisplayLabel displayText="2022 - Custom" />,
      },
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    multiple: false,
  },
  render: args => {
    return (
      <Stack maxWidth={theme => theme.fixedWidths.xs} spacing={4}>
        <ExampleAutocompleteMulti
          {...args}
          label="Placeholder"
          description="Example of autocomplete with a placeholder"
          helperText="Placeholder helper text"
          placeholder="Select a year"
        />
        <ExampleAutocompleteSingle
          {...args}
          label="Disabled State"
          description="Example of autocomplete in a Disabled state"
          disabled={true}
          helperText="Disabled helper text"
          placeholder="Select a year"
        />
        <ExampleAutocompleteSingleWithSetValue
          {...args}
          description="Example of autocomplete in a Disabled state"
          label="Disabled State with set value"
          disabled={true}
          helperText="Disabled helper text"
          placeholder="Select a year"
        />
        <ExampleAutocompleteMultiWithSetValue
          {...args}
          label="Disabled State Multi With Set Value"
          description="Example of autocomplete in a Disabled state"
          disabled={true}
          helperText="Disabled helper text"
          placeholder="Select a year"
        />
        <ExampleAutocompleteSingleWithSetValue
          {...args}
          color="disabled"
          description="Example of autocomplete with disabled color only"
          label="Disabled Color"
          helperText="helper text"
        />
        <ExampleAutocompleteSingleWithSetDisabledValue
          {...args}
          label="Autocomplete with disabled set value"
          helperText="helper text"
          description="Example of autocomplete with disabled selected value"
        />
        <ExampleAutocompleteMultiWithSetValue
          {...args}
          label="Multi with disabled set value"
          helperText="helper text"
          description="Example of multi autocomplete with disabled selected value"
        />
        <ExampleAutocompleteMulti
          {...args}
          label="Required State"
          helperText="Required helper text"
          description="Example of autocomplete in a Required state"
          required={true}
        />
        <ExampleAutocompleteSingleWithSetValue
          {...args}
          label="ReadOnly State"
          helperText="ReadOnly helper text"
          description="Example of autocomplete in a ReadOnly state"
          readOnly={true}
        />
        <ExampleAutocompleteMultiWithSetValue
          {...args}
          label="ReadOnly State Multi"
          helperText="ReadOnly helper text"
          description="Example of autocomplete in a ReadOnly state"
          readOnly={true}
        />
        <ExampleAutocompleteMulti
          {...args}
          label="Focused State"
          helperText="Focused helper text"
          description="Example of autocomplete in a Focused state"
          focused={true}
        />
        <ExampleAutocompleteMulti
          {...args}
          label="Autocomplete Error State"
          description="Example of autocomplete in an Error state"
          error={true}
          helperText="Error helper text"
        />
      </Stack>
    );
  },
};

export const Filled: Story = {
  args: {
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    value: {label: '2021', value: '2021'},
    multiple: false,
    inputVariant: 'filled',
  },
  render: args => {
    return (
      <Stack maxWidth={theme => theme.fixedWidths.xs} spacing={4}>
        <ExampleAutocompleteMulti
          {...args}
          label="Placeholder"
          description="Example of autocomplete with a placeholder"
          helperText="Placeholder helper text"
          placeholder="Select a year"
        />
        <ExampleAutocompleteMulti
          {...args}
          label="Disabled State"
          description="Example of autocomplete in a Disabled state"
          disabled={true}
          helperText="Disabled helper text"
        />
        <ExampleAutocompleteMulti
          {...args}
          label="Required State"
          helperText="Required helper text"
          description="Example of autocomplete in a Required state"
          required={true}
        />
        <ExampleAutocompleteSingleWithSetValue
          {...args}
          label="ReadOnly State"
          helperText="ReadOnly helper text"
          description="Example of autocomplete in a ReadOnly state"
          readOnly={true}
        />
        <ExampleAutocompleteMultiWithSetValue
          {...args}
          label="ReadOnly State Multi"
          helperText="ReadOnly helper text"
          description="Example of autocomplete in a ReadOnly state"
          readOnly={true}
        />
        <ExampleAutocompleteMulti
          {...args}
          label="Focused State"
          helperText="Focused helper text"
          description="Example of autocomplete in a Focused state"
          focused={true}
        />
        <ExampleAutocompleteMulti
          {...args}
          label="Autocomplete Error State"
          description="Example of autocomplete in an Error state"
          error={true}
          helperText="Error helper text"
        />
      </Stack>
    );
  },
};

export const Colors: Story = {
  args: {
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    value: {label: '2021', value: '2021'},
    multiple: false,
    inputVariant: 'filled',
  },
  render: args => {
    return (
      <Stack maxWidth={theme => theme.fixedWidths.sm} spacing={4}>
        <Typography variant="body2" color="secondary">
          The <code>filled</code> variant is meant to be used inside table cells. Table cells do not
          have labels. Note, the error state is distinct from <code>color="error"</code>
        </Typography>
        <ExampleAutocompleteMulti {...args} placeholder="Color = default" />
        <ExampleAutocompleteMulti {...args} placeholder="Color = warning" color="warning" />
        <ExampleAutocompleteMulti {...args} placeholder="Color = error" color="error" />
        <ExampleAutocompleteMulti {...args} placeholder="Color = disabled" color="disabled" />
      </Stack>
    );
  },
};

export const Groups: Story = {
  args: {
    placeholder: 'Select a year',
    description: 'Basic description',
    options: [
      {
        label: '2021',
        value: '2021',
        group: 'Group 1',
      },
      {label: '2022 - ex disabled', value: '2022', disabled: true, group: 'Group 1'},
      {label: '2023', value: '2023', group: 'Group 1'},
      {label: '2026', value: '2026', group: 'Group 2'},
      {label: '2027', value: '2027', group: 'Group 2'},
      {label: '2028', value: '2028', group: 'Group 2'},
      {label: '2029', value: '2029', group: 'Group 2', disabled: true},
      {label: '2030', value: '2030', group: 'Group 2'},
      {label: '2031', value: '2031', group: 'Group 2'},
      {label: '2032', value: '2032', group: 'Group 3'},
      {label: '2033', value: '2033', group: 'Disabled group', disabled: true},
      {label: '2034', value: '2034', group: 'Disabled group', disabled: true},
      {label: '2035', value: '2035', group: 'Disabled group', disabled: true},
    ],
  },
  render: args => {
    return (
      <Box maxWidth={500}>
        <Stack spacing={4}>
          <Autocomplete
            label="Single select Autocomplete with groups"
            helperText="You can pass group options to the Autocomplete component to group options together."
            multiple={false}
            options={args.options}
            onChange={args.onChange}
            placeholder="Select a year"
          />
          <ExampleAutocompleteMulti
            disableCloseOnSelect
            {...args}
            label="Multiselect Autocomplete with groups"
          />
        </Stack>
      </Box>
    );
  },
};

export const Translations: Story = {
  args: {
    placeholder: 'Sélectionnez une année',
    description: 'Basic description',
    options: [
      {
        label: '2021',
        value: '2021',
      },
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    localeText: {
      selectAll: 'Tout sélectionner',
      deselectAll: 'Tout désélectionner',
      getLimitTagsText: more => `${more} éléments sélectionnés`,
      getLimitTagsTextFocused: more => `${more}  sélectionnés`,
    },
  },
  render: args => {
    return (
      <Stack maxWidth={500} spacing={8}>
        <ExampleAutocompleteMulti
          {...args}
          label="Autocomplete with translations"
          helperText="You can pass localeText to translate the content of selectAll, deselectAll and the regular and focused states for displayed the selected values."
        />
        <Box>
          <Typography variant="body1" color={'secondary'}>
            uses `localeText` to translate the content of
            <ol>
              <li>
                <code>selectAll</code>
              </li>
              <li>
                <code>deselectAll</code>
              </li>
              <li>
                <code>getLimitTagsText</code>: function to display the input value when multiple
                items are selected
              </li>
              <li>
                <code>getLimitTagsTextFocused</code>: function to display the input value when
                multiple items are selected and input is focused. This is used to accommodate the
                input value while the user is searching to limit the characters of the currently
                selected value.
              </li>
            </ol>
          </Typography>
        </Box>
      </Stack>
    );
  },
};

export const CustomOptionDisplayLabel: Story = {
  args: {
    placeholder: 'Select a year',
    description: 'Basic description',
    options: [
      {
        label: '2021',
        value: '2021',
        DisplayLabel: <CustomDisplayLabel displayText="2021 - Custom" />,
      },
      {
        label: '2022',
        value: '2022',
        DisplayLabel: <CustomDisplayLabel displayText="2022 - Custom" />,
        group: 'Group 1 with custom DisplayLabel',
      },
    ],
  },
  render: args => {
    return (
      <Box maxWidth={500}>
        <ExampleAutocompleteMulti {...args} label="Autocomplete with custom options" />
      </Box>
    );
  },
};

export const LongTextTruncation: Story = {
  args: {
    placeholder: 'Select a year',
    description: 'Basic description',
    options: [
      {
        label:
          'Great Lakes/Corn Belt (1) Great Lakes/Corn Belt (1) Great Lakes/Corn Belt (1) Great Lakes/Corn Belt (1) Great Lakes/Corn Belt (1) Great Lakes/Corn Belt (1)',
        value: '2021',
      },
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {
        label: 'Regions 64 in MD/PA, between Potomac and Susquehanna Rivers',
        value: '2023',
      },
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
  },
  render: args => {
    return (
      <>
        <Box mb={4}>
          <Typography variant="body2" color="secondary">
            For Autocomplete implementations with long text options, the options will truncate with
            an ellipsis and show a tooltip with the full text on hover. For single-selects, the
            truncated selected text is scrollable on focus, to allow users to view the full selected
            option. For multi-selects, the truncated selected option provides a tooltip on hover
            when not focused.
          </Typography>
        </Box>
        <Stack width={theme => theme.fixedWidths.xs} spacing={8}>
          <ExampleAutocompleteSingle
            {...args}
            label="Autocomplete single-select with extra wide menu options"
            helperText="You can pass slotProps to the Autocomplete component to style the Popper component."
          />
          <ExampleAutocompleteMulti
            {...args}
            helperText="If multiple is true, long render values will be truncated"
            label="Autocomplete multi-select with extra wide menu options"
          />
          <ExampleAutocompleteMulti
            {...args}
            hasClear
            description="With multi select and clear button"
            label="Autocomplete multi-select with clear button with extra wide menu options"
          />
        </Stack>
      </>
    );
  },
};

export const WithTooltip: Story = {
  args: {
    placeholder: 'Select a year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    value: {label: '2021', value: '2021'},
  },
  render: args => {
    return (
      <>
        <Box mb={4}>
          <Typography variant="body2" color="secondary">
            For Autocomplete implementations to show a tooltip, it must be wrapped in another MUI
            component or div. Because Autocomplete has built in tooltips to handle long text
            options, please be considerate of tooltip usage alongside long text options.
          </Typography>
        </Box>
        <Stack width={theme => theme.fixedWidths.xs} spacing={4} mt={10}>
          <Tooltip title="Autocomplete must be wrapped to show a tooltip">
            <div>
              <ExampleAutocompleteSingle {...args} />
            </div>
          </Tooltip>
        </Stack>
      </>
    );
  },
};

export const UsageInTable: Story = {
  args: {
    placeholder: 'Select a year',
    options: [
      {label: '2021', value: '2021'},
      {label: '2022 - ex disabled', value: '2022', disabled: true},
      {label: '2023', value: '2023'},
      {label: '2026', value: '2026'},
      {label: '2027', value: '2027'},
      {label: '2028', value: '2028'},
      {label: '2029', value: '2029'},
      {label: '2030', value: '2030'},
      {label: '2031', value: '2031'},
      {label: '2032', value: '2032'},
    ],
    value: {label: '2021', value: '2021'},
    inputVariant: 'filled',
  },
  render: args => (
    <TableContainer>
      <Table padding="none">
        <TableHead>
          <TableRow>
            <TableCell>Field 1</TableCell>
            <TableCell>Field 2</TableCell>
            <TableCell>Field 3</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <ExampleAutocompleteSingle {...args} />
            </TableCell>
            <TableCell>
              <ExampleAutocompleteSingle {...args} color="warning" />
            </TableCell>
            <TableCell>
              <ExampleAutocompleteSingle {...args} color="error" hasClear />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  ),
};

export const WithStartAdornment = () => {
  const [value, setValue] = React.useState<string | null>(null);

  const handleChange = (option: AutocompleteOptionType) => {
    setValue(String(option.value));
  };

  const options = [
    {
      label: 'corn',
      value: 'corn',
    },
    {
      label: 'legume',
      value: 'legume',
    },
    {
      label: 'rice',
      value: 'rice',
    },
    {
      label: 'barley',
      value: 'barley',
    },
  ];
  return (
    <Box maxWidth={300}>
      <Autocomplete
        onChange={(_, v) => handleChange(v)}
        value={value}
        options={options}
        renderInputProps={{
          startAdornment: (
            <Box paddingRight={2}>
              <SvgIcon
                type={(value as 'corn' | 'legume' | 'rice' | 'barley') || 'question'}
                fontSize="h5"
              />
            </Box>
          ),
        }}
      />
    </Box>
  );
};
