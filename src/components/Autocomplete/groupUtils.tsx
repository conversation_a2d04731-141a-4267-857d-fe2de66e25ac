import {isArray, isDefined} from 'src/utils/typeGuards';

import type {AutocompleteOptionType} from './types';

export const getGroupOptions = <T extends AutocompleteOptionType>(
  group: string,
  options: readonly T[]
) => options.filter(option => option.group === group);

export const getIsEntireGroupSelected = <T extends AutocompleteOptionType>(
  groupOptions: readonly T[],
  value: T[]
) => {
  if (isArray(value) && isDefined(value)) {
    return groupOptions.every(option => value.includes(option));
  }
  return undefined;
};

export const isEntireGroupDisabled = <T extends AutocompleteOptionType>(
  groupOptions: readonly T[]
) => {
  return groupOptions.every(option => option.disabled);
};

export const getFilteredGroups = <T extends AutocompleteOptionType>(
  options: readonly T[],
  searchText: string
): string[] => {
  const filteredOptions = options.filter(option => {
    return option.label.toLowerCase().includes(searchText.toLowerCase());
  });
  const uniqueGroupNames = Array.from(
    new Set(filteredOptions.map(option => option.group).filter(isDefined))
  );
  return uniqueGroupNames;
};
