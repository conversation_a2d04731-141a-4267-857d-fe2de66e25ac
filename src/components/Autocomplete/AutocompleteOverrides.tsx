import {
  autocompleteClasses,
  chipClasses,
  filledInputClasses,
  iconButtonClasses,
  inputBaseClasses,
  outlinedInputClasses,
} from '@mui/material';
import type {Components, Theme} from '@mui/material/styles';

import {SvgIcon} from 'src/components/Icon';

export const AutocompleteOverrides: Components<Theme>['MuiAutocomplete'] = {
  defaultProps: {
    limitTags: 1,
    multiple: false,
    popupIcon: <SvgIcon type="chevron-down" />,
    clearIcon: <SvgIcon type="cross-circled" />,
  },
  styleOverrides: {
    root: ({theme}) => ({
      color: theme.palette.semanticPalette.text.main,
      [`.${autocompleteClasses.popupIndicator}`]: {
        paddingRight: theme.spacing(0.5),
        [`&.${iconButtonClasses.sizeSmall}`]: {
          marginRight: theme.spacing(0.5),
        },
      },
      [`.${autocompleteClasses.clearIndicator}`]: {
        [`&.${iconButtonClasses.sizeSmall}`]: {
          // Note: Aligns spacing between popup icon and clear indicator states.
          marginRight: theme.spacing(1),
        },
      },
      [`.${iconButtonClasses.root}`]: {
        // Override default icon button size for medium to be small because we cant replace icon button
        fontSize: theme.typography.h5.lineHeight,
        [`&.${iconButtonClasses.sizeMedium}`]: {
          padding: `${theme.spacing(2)} ${theme.spacing(1)}`,
        },
      },
      [`.${iconButtonClasses.root}:hover`]: {
        backgroundColor: 'transparent',
      },
      [`.${outlinedInputClasses.root} .${autocompleteClasses.endAdornment}`]: {
        right: theme.spacing(3),
      },

      [`.${filledInputClasses.root} .${autocompleteClasses.endAdornment}`]: {
        right: theme.spacing(2.5),
      },
    }),
    clearIndicator: ({theme}) => {
      return {
        visibility: 'visible',
        color: theme.palette.semanticPalette.text.main,
      };
    },
    endAdornment: ({theme}) => ({
      transform: `translate(0px, 0%)`,
      top: `calc(50% - ${theme.spacing(4.5)})`,
    }),
    inputRoot: ({theme, ownerState}) => ({
      [`&.${autocompleteClasses.inputRoot}.${outlinedInputClasses.root}`]: {
        paddingTop: theme.spacing(0),
        paddingBottom: theme.spacing(0),
        paddingLeft: theme.spacing(3),
        ...(ownerState.color === 'disabled' && {
          backgroundColor: theme.palette.grey[200],
        }),
        [`&.${outlinedInputClasses.focused}, &.${outlinedInputClasses.focused} .${outlinedInputClasses.notchedOutline}`]:
          {
            ...(ownerState.color === 'disabled' && {
              borderColor: theme.palette.semanticPalette.stroke.main,
            }),
          },
      },
      [`&.${autocompleteClasses.inputRoot}.${filledInputClasses.root}`]: {
        paddingTop: theme.spacing(3),
        paddingBottom: theme.spacing(3),
        paddingLeft: theme.spacing(3),
        paddingRight: theme.spacing(3),
        ...(ownerState.color === 'disabled' && {
          backgroundColor: theme.palette.grey[200],
        }),
        ...(ownerState.color === 'warning' && {
          backgroundColor: theme.palette.semanticPalette.surface.warning,
        }),
        ...(ownerState.color === 'error' && {
          backgroundColor: theme.palette.semanticPalette.surface.error,
        }),
        [`&.${inputBaseClasses.focused}`]: {
          ...(ownerState.color === 'disabled' && {
            borderColor: theme.palette.semanticPalette.stroke.main,
          }),
          ...(ownerState.color === 'warning' && {
            borderColor: theme.palette.semanticPalette.stroke.warning,
          }),
          ...(ownerState.color === 'error' && {
            borderColor: theme.palette.semanticPalette.stroke.error,
          }),
        },
      },
      ...(ownerState.focused === true && {
        [`&.${autocompleteClasses.inputRoot}.${filledInputClasses.root} .${chipClasses.root}`]: {
          padding: '0',
        },
      }),
    }),
    input: ({theme}) => ({
      [`&.${autocompleteClasses.input}`]: {
        paddingLeft: 0,
        paddingTop: theme.spacing(2),
        paddingBottom: theme.spacing(2),
        paddingRight: 0,
        minWidth: 'initial',
        [`&.${filledInputClasses.input}`]: {
          paddingTop: theme.spacing(0),
          paddingBottom: theme.spacing(0),
        },
      },
    }),
    listbox: ({theme}) => ({
      paddingTop: theme.spacing(1),
      paddingBottom: theme.spacing(1),
      [`& .${autocompleteClasses.option}[aria-disabled="true"]`]: {
        color: theme.palette.text.disabled,
        opacity: 1,
      },
      [`& .${autocompleteClasses.groupLabel}`]: {
        color: theme.palette.text.primary,
        fontSize: theme.typography.h6.fontSize,
        borderBottom: `1px solid ${theme.palette.divider}`,
        lineHeight: theme.spacing(11),
        top: -4,
      },
    }),
    popper: ({theme}) => ({
      paddingTop: theme.spacing(1),
    }),
    popupIndicator: ({theme}) => ({
      color: theme.palette.semanticPalette.text.main,
    }),
  },
};
