import {fireEvent, render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {DesignSystemProvider} from 'src/design-system-provider';
import {isDefined} from 'src/utils/typeGuards';

import {Autocomplete} from './index';

const mockOptions = [
  {label: 'The Shawshank Redemption', value: 'shawshank'},
  {label: 'The Godfather', value: 'godfather'},
  {label: 'The Dark Knight', value: 'dark-knight'},
];

const mockGroupOptions = [
  {label: 'The Shawshank Redemption', value: 'shawshank', group: 'Drama'},
  {label: 'The Godfather', value: 'godfather', group: 'Drama'},
  {label: 'The Dark Knight', value: 'dark-knight', group: 'Action'},
];

const mockSingleValue = mockOptions[0];

const mockMultiValue = [mockOptions[0]];

// Needed for TypographyOverflow value components
window.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

describe('Autocomplete', () => {
  it('should render the autocomplete input with the correct value', () => {
    renderWithProvider(
      <Autocomplete onChange={() => {}} options={mockOptions} value={mockSingleValue} />
    );

    expect(screen.getByRole('combobox')).toHaveValue('The Shawshank Redemption');
  });

  it('should render placeholder text', () => {
    const placeholder = 'My little placeholder';
    renderWithProvider(
      <Autocomplete
        onChange={() => {}}
        placeholder={placeholder}
        options={mockOptions}
        value={mockSingleValue}
      />
    );

    expect(screen.getByPlaceholderText(placeholder)).toHaveValue(mockSingleValue.label);
  });

  it('should render checkboxes for options when multiple is true', () => {
    renderWithProvider(
      <Autocomplete onChange={() => {}} options={mockOptions} value={mockMultiValue} multiple />
    );

    const input = screen.getByRole('button', {name: 'Open'});
    fireEvent.click(input);

    expect(screen.getByRole('option', {name: 'The Godfather'})).toBeInTheDocument();
    expect(screen.getAllByRole('checkbox')).toHaveLength(3);
  });

  it('should render `x items selected` when multiple is true and many are selected', () => {
    renderWithProvider(
      <Autocomplete
        onChange={() => {}}
        options={mockOptions}
        value={[mockOptions[0], mockOptions[1]]}
        multiple
        hasSelectAll
      />
    );

    expect(screen.getByText('2 items selected')).toBeInTheDocument();
  });

  it('should render `X éléments sélectionnés` when multiple is true, many are selected and getLimitTagsText is set', () => {
    renderWithProvider(
      <Autocomplete
        onChange={() => {}}
        options={mockOptions}
        value={[mockOptions[0], mockOptions[1]]}
        multiple
        localeText={{
          getLimitTagsText: more => `${more} éléments sélectionnés`,
        }}
      />
    );

    expect(screen.getByText('2 éléments sélectionnés')).toBeInTheDocument();
  });

  it('should render `X éléments sélectionnés` when multiple is true, many are selected and focused', () => {
    renderWithProvider(
      <Autocomplete
        onChange={() => {}}
        options={mockOptions}
        value={[mockOptions[0], mockOptions[1]]}
        multiple
        hasSelectAll
        localeText={{
          getLimitTagsText: more => `${more} éléments sélectionnés`,
          getLimitTagsTextFocused: more => `${more} éléments sélectionnés`,
        }}
      />
    );

    const input = screen.getByRole('button', {name: 'Open'});
    fireEvent.click(input);

    expect(screen.getByText('2 éléments sélectionnés')).toBeInTheDocument();
  });

  it('should select all items when selectAll is clicked', () => {
    const onChangeHandler = jest.fn();
    renderWithProvider(
      <Autocomplete
        onChange={onChangeHandler}
        options={mockOptions}
        value={mockMultiValue}
        multiple
        hasSelectAll
      />
    );

    const input = screen.getByRole('button', {name: 'Open'});
    fireEvent.click(input);

    expect(screen.getByRole('option', {name: 'Select All'})).toBeInTheDocument();

    const selectAll = screen.getByRole('option', {name: 'Select All'});
    fireEvent.click(selectAll);

    expect(onChangeHandler).toHaveBeenCalledTimes(1);
    expect(onChangeHandler).toHaveBeenCalledWith(
      expect.anything(),
      mockOptions,
      'selectOption',
      expect.objectContaining({option: {label: 'Select All', value: 'select-all'}})
    );
  });

  it('should deselect all options when deselect all is clicked', () => {
    const onChangeHandler = jest.fn();
    renderWithProvider(
      <Autocomplete
        onChange={onChangeHandler}
        options={mockOptions}
        value={mockMultiValue}
        multiple
        hasSelectAll
      />
    );

    const input = screen.getByRole('button', {name: 'Open'});
    fireEvent.click(input);

    expect(screen.getByRole('option', {name: 'Deselect All'})).toBeInTheDocument();

    const selectAll = screen.getByRole('option', {name: 'Deselect All'});
    fireEvent.click(selectAll);

    expect(onChangeHandler).toHaveBeenCalledTimes(1);
    expect(onChangeHandler).toHaveBeenCalledWith(
      expect.anything(),
      [],
      'selectOption',
      expect.objectContaining({option: {label: 'Deselect All', value: 'deselect-all'}})
    );
  });

  it('should clear the input when hasClear is true', () => {
    const onChangeHandler = jest.fn();
    renderWithProvider(
      <Autocomplete
        onChange={onChangeHandler}
        options={mockOptions}
        value={[mockOptions[0], mockOptions[1]]}
        multiple
        hasClear
      />
    );

    const closeIcon = screen.getByRole('button', {name: 'Clear'});

    fireEvent.click(closeIcon);

    expect(onChangeHandler).toHaveBeenCalledTimes(1);
    expect(onChangeHandler).toHaveBeenCalledWith(expect.anything(), [], 'clear', undefined);
  });

  describe('with multiselect groups', () => {
    it('should render group options', () => {
      renderWithProvider(<Autocomplete onChange={() => {}} options={mockGroupOptions} multiple />);

      const input = screen.getByRole('button', {name: 'Open'});
      fireEvent.click(input);

      expect(screen.getByText('Drama')).toBeInTheDocument();
      expect(screen.getByText('Action')).toBeInTheDocument();
    });

    it('should render group checkboxes when multiple is selected', () => {
      renderWithProvider(<Autocomplete onChange={() => {}} options={mockGroupOptions} multiple />);

      const input = screen.getByRole('button', {name: 'Open'});
      fireEvent.click(input);

      expect(screen.getAllByRole('checkbox')).toHaveLength(2);
    });

    it('should select all options in a group when clicking on the group checkbox', () => {
      const onChangeHandler = jest.fn();
      render(<Autocomplete onChange={onChangeHandler} options={mockGroupOptions} multiple />);

      const input = screen.getByRole('button', {name: 'Open'});
      fireEvent.click(input);

      const groupOption = screen.getByText('Drama');
      const groupOptionCheckbox = groupOption.querySelector('input[type="checkbox"]');

      expect(groupOptionCheckbox).not.toBe(null);
      if (isDefined(groupOptionCheckbox)) {
        fireEvent.click(groupOptionCheckbox);

        expect(onChangeHandler).toHaveBeenCalledTimes(1);
        expect(onChangeHandler).toHaveBeenCalledWith(
          expect.anything(),
          [mockGroupOptions[0], mockGroupOptions[1]],
          'selectOption',
          undefined
        );
      }
    });

    it('should expand the group when clicking on the group label', () => {
      render(<Autocomplete onChange={() => {}} options={mockGroupOptions} multiple />);

      const input = screen.getByRole('button', {name: 'Open'});
      fireEvent.click(input);

      const groupOption = screen.getByText('Drama');
      fireEvent.click(groupOption);

      const expectedGroupOptions = mockGroupOptions.filter(option => option.group === 'Drama');
      expect(expectedGroupOptions.length).toBeGreaterThan(0);

      expectedGroupOptions.forEach(option => {
        expect(screen.getByRole('option', {name: option.label})).toBeInTheDocument();
      });

      const unexpectedGroupOptions = mockGroupOptions.filter(option => option.group !== 'Drama');
      expect(unexpectedGroupOptions.length).toBeGreaterThan(0);

      unexpectedGroupOptions.forEach(option => {
        expect(screen.queryByRole('option', {name: option.label})).not.toBeInTheDocument();
      });
    });

    it('should close the group when clicking on an expanded group again', () => {
      render(<Autocomplete onChange={() => {}} options={mockGroupOptions} multiple />);

      const input = screen.getByRole('button', {name: 'Open'});
      fireEvent.click(input);

      const groupOption = screen.getByText('Drama');
      fireEvent.click(groupOption);
      fireEvent.click(groupOption);

      expect(screen.queryByText('The Shawshank Redemption')).not.toBeInTheDocument();
      expect(screen.queryByText('The Godfather')).not.toBeInTheDocument();
    });

    it('should show expanded group options when typing in the input', async () => {
      render(
        <Autocomplete
          placeholder="Type here"
          onChange={() => {}}
          options={mockGroupOptions}
          multiple
        />
      );

      const inputElement = screen.getByPlaceholderText('Type here');
      userEvent.type(inputElement, 'God');

      await screen.findByText('The Godfather');

      expect(screen.getByText('The Godfather')).toBeInTheDocument();
      expect(screen.getByText('Drama')).toBeInTheDocument();

      expect(screen.queryByText('The Shawshank Redemption')).not.toBeInTheDocument();
    });

    it('should not display options when typing in the input and no options match', async () => {
      render(
        <Autocomplete
          placeholder="Type here"
          onChange={() => {}}
          options={mockGroupOptions}
          multiple
        />
      );

      const inputElement = screen.getByPlaceholderText('Type here');
      userEvent.type(inputElement, 'Not a movie');

      await screen.findByText('No options');

      expect(screen.getByText('No options')).toBeInTheDocument();
    });

    it('Should show disabled group when all options are disabled', async () => {
      const disabledGroupOptions = [
        {label: 'The Shawshank Redemption', value: 'shawshank', group: 'Drama', disabled: true},
        {label: 'The Godfather', value: 'godfather', group: 'Drama', disabled: true},
      ];

      render(<Autocomplete onChange={() => {}} options={disabledGroupOptions} multiple />);

      const input = screen.getByRole('button', {name: 'Open'});
      fireEvent.click(input);

      expect(screen.getByText('Drama')).toBeInTheDocument();
      expect(screen.getByText('Drama')).toHaveAttribute('aria-disabled', 'true');
    });
  });

  describe('with single select groups', () => {
    it('should render group options with headers', () => {
      renderWithProvider(<Autocomplete onChange={() => {}} options={mockGroupOptions} />);

      const input = screen.getByRole('button', {name: 'Open'});
      fireEvent.click(input);

      mockGroupOptions.forEach(option => {
        if (option.group) {
          expect(screen.getByText(option.group)).toBeInTheDocument();
        }
        expect(screen.getByRole('option', {name: option.label})).toBeInTheDocument();
      });
    });
  });
});

const renderWithProvider = (children: JSX.Element) => {
  render(<DesignSystemProvider muiThemeKey={0}>{children}</DesignSystemProvider>);
};
