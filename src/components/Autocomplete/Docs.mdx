import { <PERSON>a, <PERSON><PERSON>, <PERSON><PERSON>, Story } from "@storybook/blocks";
import * as AutocompleteStories from './index.stories.tsx'

<Meta of={AutocompleteStories}/>

## Autocomplete Overview
MUI Documentation: [Autocomplete](https://material-ui.com/components/autocomplete/)

`Autocomplete` is a normal text input enhanced by a panel of suggested options.

`Autocomplete` is an controlled component that is meant to be used for advanced scenarios such as searching and grouping.

For simple use-cases, consider using the `SelectField` component from instead.

`Autocomplete` requires:
- an `options` Array of objects with keys `label` & `value` to display as select options
- an `onChange` handler
- a `value` prop to define the selected value

When `multiple` is `true` more props become available which support `Autocomplete` multiple variant:

The below props support i18n.
- when `multiple` is `true` `localeText.getLimitTagsText` is used to display the number of selected items within the `TextField` input
- when `multiple` is `true` `localeText.getLimitTagsTextFocused` is used to display the number of selected items within the `TextField` input while the user is typing.
- when `multiple` is `true` and `hasSelectAll`  is `true` `localeText.selectAll` and `localeText.deselectAll` are used to display the select all and deselect all options within the `Autocomplete` popup.

## Basic Example
<Canvas of={AutocompleteStories.Basic} />
<Controls of={AutocompleteStories.Basic}/>

## Multiple
<Canvas of={AutocompleteStories.Multiple} />

## Free Solo

#### NOTE: FreeSolo in multi state has not yet been designed. Though it is available please consult with design before using.

Set `freeSolo` to true so the textbox can contain any arbitrary value.

Please read more here: https://mui.com/material-ui/react-autocomplete/#free-solo
<Canvas of={AutocompleteStories.FreeSolo} />

## States
<Canvas of={AutocompleteStories.States} />

## Filled
<Canvas of={AutocompleteStories.Filled} />

## Groups
Adding a group property to options array can enable grouping of options.
<Canvas of={AutocompleteStories.Groups} />

## Translations
<Canvas of={AutocompleteStories.Translations} />

## Custom Option Display Label 
A custom option display label can be achieved by passing a custom JSX element for an option
using the property CustomOptionDisplayLabel, which overrides label (label is still used for search string purposes)
<Canvas of={AutocompleteStories.CustomOptionDisplayLabel} />

## Edge Cases
Below are common edge cases for the `Autocomplete` component and how they should be handled.
<Canvas of={AutocompleteStories.LongTextTruncation} />
<Canvas of={AutocompleteStories.WithTooltip} />
<Canvas of={AutocompleteStories.WithStartAdornment} />
