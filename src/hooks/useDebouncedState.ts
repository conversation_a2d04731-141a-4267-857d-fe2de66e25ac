import {useCallback, useRef, useState} from 'react';
import type {Dispatch, SetStateAction} from 'react';

/**
 * setState is called after `ms` of inactivity.
 *
 * Example usage:
 * ```
 *   const [state, setState] = useDebouncedState(state, 200);
 * ```
 */
export function useDebouncedState<State>(
  initialState: State,
  ms: number
): [State, Dispatch<SetStateAction<State>>] {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [state, setState] = useState(initialState);

  const debouncedSetState: Dispatch<SetStateAction<State>> = useCallback(
    updatedState => {
      if (timeoutRef.current !== null) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        setState(updatedState);
        timeoutRef.current = null;
      }, ms);
    },
    [setState, ms]
  );

  return [state, debouncedSetState];
}
