import {useLayoutEffect, useRef} from 'react';

import {useDebouncedState} from './useDebouncedState';

export function useDimensions(element?: Element | null): Pick<DOMRect, 'width' | 'height'> {
  const [dimensions, setDimensions] = useDebouncedState<Pick<DOMRect, 'width' | 'height'>>(
    {
      width: 0,
      height: 0,
    },
    200
  );
  const resizeObserver = useRef<ResizeObserver>();

  useLayoutEffect(() => {
    if (!element) {
      return;
    }

    if (resizeObserver.current) {
      resizeObserver.current.unobserve(element);
      resizeObserver.current.observe(element);
    } else {
      resizeObserver.current = new ResizeObserver(entries => {
        for (let i = 0; i < entries.length; i++) {
          const entry = entries[i];
          const {width, height} = entry.contentRect;

          setDimensions({width, height});
        }
      });

      resizeObserver.current.observe(element);
    }

    return () => {
      resizeObserver.current?.disconnect();
    };
  }, [element, setDimensions]);

  return dimensions;
}
