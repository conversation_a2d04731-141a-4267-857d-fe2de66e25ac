import {useEffect, useRef, useState} from 'react';

type SizeGetter = (el?: Element | null) => number;
// Watches for height and width changes of an element
export const useWatchSize = (
  el?: Element | null,
  heightGetter?: SizeGetter,
  widthGetter?: SizeGetter
): [height: number | undefined, width: number | undefined] => {
  const [height, setHeight] = useState<number | undefined>(getSize(el).height);
  const [width, setWidth] = useState<number | undefined>(getSize(el).width);
  const observer = useRef<ResizeObserver>();

  // Watches for size changes
  useEffect(() => {
    if (el) {
      if (observer.current) {
        observer.current.unobserve(el);
        observer.current.observe(el);
      } else {
        observer.current = new ResizeObserver(() => {
          const size = getSize(el, heightGetter, widthGetter);
          if (size.height !== undefined) {
            setHeight(size.height);
          }
          if (size.width !== undefined) {
            setWidth(size.width);
          }
        });
        observer.current.observe(el);
      }
    }

    return function cleanup() {
      observer.current?.disconnect();
    };
  }, [el, heightGetter, widthGetter]);

  return [height, width];
};

// Gets the size via either passed in getter or via bounding rect
const getSize = (el?: Element | null, heightGetter?: SizeGetter, widthGetter?: SizeGetter) => {
  const boundingRect = el?.getBoundingClientRect?.();
  const height = heightGetter ? heightGetter(el) : boundingRect?.height;
  const width = widthGetter ? widthGetter(el) : boundingRect?.width;

  return {
    height,
    width,
  };
};
