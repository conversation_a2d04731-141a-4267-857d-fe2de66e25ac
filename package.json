{"name": "@regrow-internal/design-system", "version": "1.1.144", "type": "module", "license": "UNLICENSED", "files": ["dist"], "main": "./dist/design-system.umd.cjs", "module": "./dist/design-system.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/design-system.js", "require": "./dist/design-system.umd.cjs"}, "./fonts/": {"import": "./dist/styles/"}}, "scripts": {"start": "storybook dev --no-open -p 6006", "test:unit": "jest", "test:ui": "vitest --ui", "test:storybook": "test-storybook", "bench": "vitest bench", "build": "tsc && vite build", "preview": "vite preview", "build-storybook": "storybook build", "preinstall": "npx only-allow pnpm", "prepare": "husky install", "tsc": "tsc --noEmit", "eslint": "eslint 'src/**/*.{ts,tsx}'", "prettier:write": "prettier -w \"src/**/*.{ts,tsx}\" --config ./prettier.config.cjs --ignore-unknown", "prepublish": "pnpm build && pnpm artifactregistry-login", "version-alpha": "pnpm version prerelease --preid alpha && git push", "version-patch": "pnpm version patch && git push", "version-minor": "pnpm version minor && git push", "version-major": "pnpm version major && git push", "artifactregistry-login": "npx google-artifactregistry-auth", "up-storybook-latest": "storybook upgrade --package-manager pnpm --prerelease", "chromatic": "npx chromatic --project-token=bf4c2a7f02cd"}, "peerDependencies": {"chartjs-plugin-datalabels": "^2.2.0", "react": "^17.0.1", "react-chartjs-2": "^5.2.0", "react-dom": "^17.0.1", "styled-components": "^5.3.0"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.0.2", "@mui/styled-engine-sc": "^5.11.11", "@storybook/addon-a11y": "^7.4.0", "@storybook/addon-backgrounds": "^7.4.0", "@storybook/addon-docs": "^7.4.0", "@storybook/addon-essentials": "^7.4.0", "@storybook/addon-highlight": "^7.4.0", "@storybook/addon-mdx-gfm": "^7.4.0", "@storybook/addon-measure": "^7.4.0", "@storybook/addon-outline": "^7.4.0", "@storybook/addon-toolbars": "^7.4.0", "@storybook/blocks": "^7.4.0", "@storybook/manager-api": "^7.4.0", "@storybook/react": "^7.4.0", "@storybook/react-vite": "^7.4.0", "@storybook/test-runner": "^0.17.0", "@storybook/testing-library": "0.2.0", "@storybook/theming": "^7.4.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "20.14.0", "@types/react": "^17.0.11", "@types/react-datepicker": "~3.1.8", "@types/react-dom": "^17.0.8", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^2.1.0", "@vitest/ui": "^0.29.2", "chartjs-plugin-datalabels": "^2.2.0", "chromatic": "^6.12.0", "date-fns": "^2.29.3", "deepmerge": "^4.3.1", "eslint": "^8.25.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.13", "husky": "^8.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^24.0.0", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "react": "^17.0.1", "react-chartjs-2": "5.2.0", "react-datepicker": "^4.16.0", "react-dom": "^17.0.1", "react-router-dom": "^5.3.4", "storybook": "^7.4.0", "storybook-addon-designs": "^7.0.0-beta.2", "styled-components": "^5.3.0", "ts-node": "^10.9.2", "typescript": "^5.7.0", "vite": "^3.2.11", "vite-plugin-dts": "^1.6.6", "vitest": "^0.29.2"}, "engines": {"node": "20.15.1", "pnpm": "8.7.4"}, "pnpm": {"overrides": {"@mui/styled-engine": "npm:@mui/styled-engine-sc@^5.11.11", "@babel/traverse": "^7.23.4"}}, "dependencies": {"@mui/lab": "5.0.0-alpha.138", "@mui/material": "^5.17.1", "@mui/system": "^5.17.1", "@mui/x-data-grid-pro": "^7.29.7", "@mui/x-license": "^7.29.1", "@testing-library/react": "12.1.5", "chart.js": "^4.3.0", "playwright": "^1.43.1", "ts-jest": "^29.1.2"}, "packageManager": "pnpm@8.7.4+sha256.7d14339f572583eb550b7629f085d0443166ddbfa25fa32f8420179123fab98a"}