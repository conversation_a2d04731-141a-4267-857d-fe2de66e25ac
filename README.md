# Regrow Design System

## Use this package in another repo

Setup authentication for `@regrow-internal` packages

```
gcloud auth login
npx google-artifactregistry-auth
```

## Writing tests

Storybook test runner turns all of your stories into executable tests. It is powered by <PERSON><PERSON> and <PERSON><PERSON>.

## Local Setup

requires `node`, `nvm`, `npm`

- `<NAME_EMAIL>:regrowag/design-system.git` - Clone the repository from [github](https://github.com/regrowag/design-system)

- `nvm use` - use the projects node version

- `npm install -g pnpm@8.7.4` - globally install the required version of pnpm

- `pnpm i` - install dependencies

- `pnpm start` - start storybook on localhost:6006

## Common commands

- `pnpm build` - build the design system library for CI/CD

- `pnpm build-storybook` - build storybook for us in CI/CD pipeline

- `pnpm pack` - compress the current build of Design System into a compressed .tgz file for import (more details below)

- `pnpm test` - run tests

- `pnpm test:storybook` - run storybook tests via [storybook test runner](https://storybook.js.org/docs/writing-tests/test-runner)

- `pnpm test:unit` - run unit tests

- `pnpm bench` - run benchmarks

- `pnpm version` - (-major, -minor, -patch, -alpha) version the Design System package (more details below)

- `pnpm publish` - builds, publishes and tags as `latest`, the current branch of Design System to our shared [Google Artifact Registry](https://console.cloud.google.com/artifacts/npm/flurosat-154904/us-west1/regrow-js/@regrow-internal%2Fdesign-system?project=flurosat-154904). (more details below)

## How to test the package locally

When you are satisfied with your changes in storybook and want to test your UI updates in the consuming application use the following directions:

**1. Build and Pack your local `design-system` repository**

- Navigate to your local `design-system` repository
- run `pnpm build` to rebuild your `dist` folder
- run `pnpm pack` to compress your app into a `.tgz` file located at the root of your design-system repo

**2. Update your UI repo (ex: `flurosense-ui`) design-system package reference to your local packed design-system**

- in your `package.json` update your `dependencies` to read: `"@regrow-internal/design-system": "file:../relative/path/to/design-system/regrow-internal-design-system-X.X.X.tgz"` (replace file name with .tgz file name)

**3. Reset your dependency lock files and refetch/reinstall your packages**

- run `git checkout pnpm-lock.yaml` to reset `pnpm-lock.yaml`
- run `pnpm i` to reinstall packages (esp design system)

**4. Restart your UI server**

- run `pnpm start` to restart your consuming applications server
- hard refresh your browser instance to clear any cache / or close your tab and reload your page

You should now be able to test your `design-system` in the consuming application. If you make additional changes within your `design-system`, you will need to perform `step 1`, `step 3` and `step 4` again.

## How to publish the package

### Version

Run **_ONE_** of the following command to version a alpha | patch | minor | major version:

When your branch has passed code review, run these commands **_BEFORE merging the PR_**

```
pnpm version-alpha
```

```
pnpm version-patch
```

```
pnpm version-minor
```

```
pnpm version-major
```

These commands will increment the package version (based on alpha, patch, minor, major specified),

### Publish

Once you have checked your versioning in `package.json` you can merge your pr

Once you have merged your pr into `main`, publish the updated Design System package from the pulled `main` branch to our Google Artifact Registry. This will publish and update the distribution tag to latest. Note, appropriate permissions are required to publish.

```
pnpm publish
```

Packages live within our [regrow-js Google Cloud Artifact Registry](https://console.cloud.google.com/artifacts/npm/flurosat-154904/us-west1/regrow-js/@regrow-internal%2Fdesign-system?project=flurosat-154904)

## Hosted design system

### How to deploy the design system

We deploy the design system through github actions. We use `Prod` as our source of truth for what is on `main`, while `Dev` is used primarily for acceptance testing branches before merging. `Prod` is deployed automatically when a branch is merged to `main`, but can also be run manually. `Dev` is always run manually.

To manually deploy an environment (dev, dev1, dev2, dev3, prod)

1. Navigate to the repository's [Actions page](https://github.com/regrowag/design-system/actions)
1. On the left hand side, you'll see `k8s-deploy-dev[x]` and `k8s-deploy-prod` actions. Choose the appropriate one.
1. Once you choose a workflow, you should see a `Run Workflow` button on the right hand side of the page.
1. Clicking that button opens a menu with a `Use workflow from` option. Choose the branch you want to deploy, and click `Run Workflow`, et voila!

See [Github documentation](https://docs.github.com/en/actions/using-workflows/manually-running-a-workflow) for helpful pictures.

### How to view the hosted design system

[Design system production storybook](https://design-system.regrow.ag/?path=/story/components-timeline--timeline)

Note, you must be connected to VPN to access development environments

[Design system dev storybook](http://design-system.int.dev.regrow.cloud/?path=/story/components-icon--example)

[Design system dev1 storybook](http://design-system-1.int.dev.regrow.cloud/?path=/story/components-icon--example)

[Design system dev2 storybook](http://design-system-2.int.dev.regrow.cloud/?path=/story/components-icon--example)

[Design system dev3 storybook](http://design-system-3.int.dev.regrow.cloud/?path=/story/components-icon--example)

## Additional Resources

- For our design source of truth, review our [Figma](https://www.figma.com/file/uATsqjejX1WLjhMjezGB5i/Regrow-Design-System?mode=dev) Design File
- For more information on how we got started, architecture and organization principles, current meeting notes, review our [Confluence](https://regrow.atlassian.net/wiki/spaces/DES/pages/2777317637/Design+System) Space
- For more details on our roadmap and current work, review our [JIRA](https://regrow.atlassian.net/jira/software/c/projects/DES/boards/93/backlog) backlog and kanban board
- Our Design System is built using MUI Core, specifically the [Material UI](https://mui.com/material-ui/getting-started/), MUI System and [MUI X](https://mui.com/x/whats-new/) packages. We utilize [styled-components](https://styled-components.com/) for our style engine.
- [Click here to learn how to write storybook stories using the new CSF3 format :)](https://storybook.js.org/blog/component-story-format-3-0/)
