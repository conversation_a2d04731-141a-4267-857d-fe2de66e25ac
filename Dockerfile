FROM node:20.15.1 as build

WORKDIR /app

RUN npm install -g pnpm@8.7.4

COPY package.json .
COPY pnpm-lock.yaml .

RUN pnpm install

COPY . .

ENV NODE_OPTIONS="--max-old-space-size=3584"

RUN pnpm build && pnpm build-storybook

FROM nginx:1.23.2-alpine

RUN sed -i '9 i application/javascript mjs;' /etc/nginx/mime.types
COPY --from=build /app/storybook-static /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

